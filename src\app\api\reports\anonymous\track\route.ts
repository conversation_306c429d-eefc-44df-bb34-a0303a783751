import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import connectDB from '@/lib/db/mongodb';
import { Report, Message } from '@/lib/db/models';
import { SecurityAuditLogger } from '@/lib/security/audit-logger';

export const runtime = 'nodejs';

// Validation schema for report tracking
const trackReportSchema = z.object({
  reportToken: z.string().min(1, 'Report token is required')
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = trackReportSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { reportToken } = validationResult.data;

    await connectDB();

    // Find the report by token
    const report = await Report.findOne({ 
      referenceNumber: reportToken,
      isAnonymous: true 
    });

    if (!report) {
      // Log failed tracking attempt
      await SecurityAuditLogger.logSuspiciousActivity(
        request,
        'Invalid report token for tracking',
        {
          reportToken: reportToken,
          timestamp: new Date().toISOString()
        }
      );

      return NextResponse.json(
        { success: false, error: 'Invalid report token' },
        { status: 404 }
      );
    }

    // Get message count for this report
    const messageCount = await Message.countDocuments({
      reportId: report._id
    });

    // Log successful tracking access
    console.log('ANONYMOUS REPORT TRACKED:', {
      reportId: report.reportId,
      reportToken: reportToken,
      timestamp: new Date().toISOString()
    });

    // Return basic report status information (no sensitive details)
    return NextResponse.json({
      success: true,
      data: {
        reportId: report.reportId,
        title: report.title,
        category: report.category,
        status: report.status,
        priority: report.priority,
        dateSubmitted: report.dateSubmitted,
        lastUpdated: report.lastUpdated,
        location: report.location,
        dateOfOccurrence: report.dateOfOccurrence,
        hasMessages: messageCount > 0,
        messageCount: messageCount
      }
    }, { status: 200 });

  } catch (error) {
    console.error('Track report error:', error);
    
    await SecurityAuditLogger.logSuspiciousActivity(
      request,
      'Report tracking failed',
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    );

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to track report. Please try again.' 
      },
      { status: 500 }
    );
  }
}

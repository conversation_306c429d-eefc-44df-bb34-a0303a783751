"use client";

import Footer from "@/components/home-components/shared/Footer";
import Header from "@/components/home-components/shared/Header";
import ContactForm from "@/components/home-components/contact/ContactForm";
import OfficeCard from "@/components/home-components/contact/OfficeCard";
import DepartmentCard from "@/components/home-components/contact/DepartmentCard";
import ScheduleDemo from "@/components/home-components/contact/ScheduleDemo";
import FAQSection from "@/components/home-components/contact/FAQSection";
import Image from "next/image";
import { offices, departments, contactFaqs } from "@/lib/staticContent";
import { useRouter } from "next/navigation";

const SectionHeading = ({ title, description }: { title: string; description: string }) => (
  <div className="text-center mb-12">
    <h2 className="text-[28px] font-bold text-[#1E4841] mb-4">
      {title}
    </h2>
    <p className="text-[#4B5563] text-lg max-w-2xl mx-auto">
      {description}
    </p>
  </div>
);

const FeatureItem = ({ icon, title, description }: { icon: string; title: string; description: string }) => (
  <div className="flex items-start gap-4 mt-6">
    <div className="w-8 h-8 rounded-full bg-[#ECF4E9] mt-1 flex items-center justify-center">
      <Image
        src={icon}
        alt={title}
        width={12}
        height={14}
        className="w-3 h-auto"
      />
    </div>
    <div className="flex flex-col">
      <h3 className="text-lg font-semibold text-[#242E2C]">
        {title}
      </h3>
      <p className="text-base text-[#6B7271]">
        {description}
      </p>
    </div>
  </div>
);

export default function ContactPage() {
  const router = useRouter();

  const handleWhistleblowerClick = () => {
    router.push('/products/whistleblower');
  };

  return (
    <div>
      <Header />
      <main id="main-content" className="flex-1 pt-20 bg-white">
        {/* Hero Section */}
        <section className="relative px-4 sm:px-8 md:px-12 lg:px-[180px] py-24 sm:py-28 md:py-32 lg:py-36 mx-auto bg-[#1E4841]">
          <Image
            src="/desktop/contact/hero.jpg"
            alt="Investigator background"
            fill
            priority
            className="object-cover mix-blend-hard-light opacity-10"
          />
          <div className="max-w-6xl mx-auto text-center relative z-10">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Get in Touch
            </h1>
            <p className="text-lg md:text-xl text-[#F3F4F6] max-w-2xl mx-auto">
              We&apos;re here to help with any questions about our whistleblowing solutions. Reach out to our team for support, demos, or information.
            </p>
          </div>
        </section>

        {/* Contact Form Section */}
        <section className="py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <SectionHeading 
              title="Send us a Message"
              description="Fill out the form below and our team will get back to you within 24 hours."
            />
            <ContactForm />
          </div>
        </section>

        {/* Global Offices Section */}
        <section className="py-16 px-4 bg-[#F9FAFB]">
          <div className="max-w-3xl mx-auto">
            <SectionHeading
              title="Our Global Offices"
              description="Visit us at one of our office locations around the world."
            />
            <div className="grid md:grid-cols-2 gap-8">
              {offices.map((office, index) => (
                <OfficeCard key={index} {...office} />
              ))}
            </div>
          </div>
        </section>

        {/* Department Contacts Section */}
        <section className="py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <SectionHeading
              title="Department Contacts"
              description="Reach out directly to the department best suited to assist you."
            />
            <div className="grid md:grid-cols-2 gap-6">
              {departments.map((dept, index) => (
                <DepartmentCard
                  key={index}
                  {...dept}
                  onCtaClick={dept.title === "Whistleblower Support" ? handleWhistleblowerClick : undefined}
                />
              ))}
            </div>
          </div>
        </section>

        {/* Schedule Section */}
        <section className="py-16 px-4 bg-[#F9FAFB]">
          <div className="max-w-6xl mx-auto flex flex-col md:flex-row gap-24">
            <div className="text-left flex-1/2">
              <h2 className="text-[28px] font-bold text-[#1E4841] mb-4">
                Schedule a Personalized Demo
              </h2>
              <p className="text-[#4B5563] text-lg max-w-2xl mx-auto">
                See our whistleblowing platform in action with a personalized demo tailored to your organization&apos;s needs. Our product specialists will guide you through the features most relevant to your compliance requirements.
              </p>
              <FeatureItem 
                icon="/desktop/contact/icons/tour.svg"
                title="Comprehensive Platform Tour"
                description="Get a complete walkthrough of our reporting, investigation, and analytics modules."
              />
              <FeatureItem
                icon="/desktop/contact/icons/sales.svg"
                title="Expert Consultation"
                description="Discuss your specific requirements with our compliance and technical specialists."
              />
              <FeatureItem
                icon="/desktop/contact/icons/q-a.svg"
                title="Q&A Session"
                description="Get answers to all your questions about implementation, security, and compliance."
              />
            </div>
            <ScheduleDemo />
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 px-4">
          <div className="max-w-4xl mx-auto">
            <SectionHeading
              title="Frequently Asked Questions"
              description="Find quick answers to common questions about our platform and services."
            />
            <FAQSection faqs={contactFaqs} />
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
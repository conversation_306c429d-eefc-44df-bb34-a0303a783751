import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

// Extend Next.js recommended configs and add overrides to reduce noise
const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Allow explicit any in this codebase for pragmatism
      "@typescript-eslint/no-explicit-any": "off",
      // Ignore unused vars when intentionally prefixed with _
      "@typescript-eslint/no-unused-vars": [
        "warn",
        { argsIgnorePattern: "^_", varsIgnorePattern: "^_" }
      ],
      // Allow simple apostrophes in JSX text
      "react/no-unescaped-entities": "off",
    },
  },
];

export default eslintConfig;

"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { Shield } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import IncidentInfoStep from '@/components/anonymous-report/IncidentInfoStep';
import EvidenceStep from '@/components/anonymous-report/EvidenceStep';
import ConsentStep from '@/components/anonymous-report/ConsentStep';
import ConfirmationStep from '@/components/anonymous-report/ConfirmationStep';

interface AnonymousSession {
  companyName: string;
  sessionId: string;
  expiresAt: string;
}

interface ReportFormData {
  // Step 1 - Incident Info
  title: string;
  category: string;
  dateOfOccurrence: string;
  location: string;
  description: string;
  
  // Step 2 - Evidence
  evidenceFiles: File[];
  evidenceDescription: string;
  
  // Step 3 - Consent
  privacyConsent: boolean;
  
  // Step 4 - Confirmation (populated after submission)
  reportToken?: string;
  submissionId?: string;
}

const steps = [
  { number: 1, title: "Incident Info", description: "Report details" },
  { number: 2, title: "Evidence", description: "Supporting documents" },
  { number: 3, title: "Consent", description: "Privacy agreement" },
  { number: 4, title: "Confirmation", description: "Submission complete" }
];

export default function AnonymousNewReportPage() {
  const router = useRouter();
  const [session, setSession] = useState<AnonymousSession | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [formData, setFormData] = useState<ReportFormData>({
    title: '',
    category: '',
    dateOfOccurrence: '',
    location: '',
    description: '',
    evidenceFiles: [],
    evidenceDescription: '',
    privacyConsent: false
  });

  useEffect(() => {
    // Check for anonymous session
    const sessionData = localStorage.getItem('anonymousSession');
    if (sessionData) {
      try {
        const parsedSession = JSON.parse(sessionData);
        const expiresAt = new Date(parsedSession.expiresAt);
        
        if (expiresAt > new Date()) {
          setSession(parsedSession);
        } else {
          localStorage.removeItem('anonymousSession');
          toast({
            title: "Session Expired",
            description: "Your anonymous session has expired. Please start a new session.",
            variant: "destructive"
          });
          router.push('/login/whistleblower');
        }
      } catch {
        localStorage.removeItem('anonymousSession');
        router.push('/login/whistleblower');
      }
    } else {
      router.push('/login/whistleblower');
    }
    setIsLoading(false);
  }, [router]);

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = (stepNumber: number) => {
    if (stepNumber <= currentStep || stepNumber === currentStep - 1) {
      setCurrentStep(stepNumber);
    }
  };

  const updateFormData = (updates: Partial<ReportFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Submit the report
      const response = await fetch('/api/reports/anonymous/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          sessionId: session?.sessionId,
          companyName: session?.companyName
        }),
      });

      const result = await response.json();

      if (result.success) {
        updateFormData({
          reportToken: result.data.reportToken,
          submissionId: result.data.submissionId
        });
        setCurrentStep(4);
        toast({
          title: "Report Submitted Successfully",
          description: "Your anonymous report has been securely submitted."
        });
      } else {
        throw new Error(result.error || 'Failed to submit report');
      }
    } catch (error) {
      console.error('Report submission error:', error);
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your report. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Shield className="h-12 w-12 text-[#1E4841] mx-auto mb-4 animate-spin" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <IncidentInfoStep
            formData={formData}
            updateFormData={updateFormData}
            onNext={handleNext}
          />
        );
      case 2:
        return (
          <EvidenceStep
            formData={formData}
            updateFormData={updateFormData}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 3:
        return (
          <ConsentStep
            formData={formData}
            updateFormData={updateFormData}
            onSubmit={handleSubmit}
            onBack={handleBack}
            isSubmitting={isSubmitting}
          />
        );
      case 4:
        return (
          <ConfirmationStep
            formData={formData}
            session={session}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center mr-4">
                <Image
                  src="/logo.svg"
                  alt="7IRIS Logo"
                  width={83}
                  height={37}
                  className="h-8 w-auto hover:scale-105 transition-transform duration-300"
                  priority
                />
              </Link>
              <div className="flex items-center">
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">Anonymous Session</h1>
                  <p className="text-xs text-gray-600">{session.companyName}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Progress Steps */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Report Progress</h2>
            <div className="flex items-center justify-between relative">
              {/* Progress Line */}
              <div className="absolute top-6 left-0 right-0 h-0.5 bg-gray-200 z-0">
                <div
                  className="h-full bg-[#1E4841] transition-all duration-500 ease-in-out"
                  style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
                />
              </div>

              {steps.map((step) => (
                <div key={step.number} className="flex flex-col items-center relative z-10">
                  <div
                    className={`flex items-center justify-center w-12 h-12 rounded-full border-2 cursor-pointer transition-all duration-300 ${
                      step.number === currentStep
                        ? 'bg-[#1E4841] border-[#1E4841] text-white shadow-lg scale-110'
                        : step.number < currentStep
                        ? 'bg-[#1E4841] border-[#1E4841] text-white'
                        : 'bg-white border-gray-300 text-gray-500 hover:border-gray-400'
                    }`}
                    onClick={() => handleStepClick(step.number)}
                  >
                    {step.number < currentStep ? (
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <span className="text-sm font-semibold">{step.number}</span>
                    )}
                  </div>
                  <div className="mt-3 text-center">
                    <p className={`text-sm font-medium ${
                      step.number === currentStep
                        ? 'text-[#1E4841]'
                        : step.number < currentStep
                        ? 'text-[#1E4841]'
                        : 'text-gray-500'
                    }`}>
                      {step.title}
                    </p>
                    <p className={`text-xs mt-1 ${
                      step.number === currentStep
                        ? 'text-[#1E4841]'
                        : 'text-gray-400'
                    }`}>
                      {step.number === currentStep ? 'Current' :
                       step.number < currentStep ? 'Complete' : 'Pending'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-grow max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderStep()}
      </div>

      {/* Footer */}
      <footer className="bg-[#2C3E50] text-white mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-center text-sm">
            <div className="mb-2 sm:mb-0">
              <p>© 2025 Secure Reporting System. All rights reserved.</p>
            </div>
            <div className="flex space-x-6">
              <Link href="/privacy-policy" className="hover:text-gray-300 transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms-of-service" className="hover:text-gray-300 transition-colors">
                Terms of Service
              </Link>
              <Link href="/contact" className="hover:text-gray-300 transition-colors">
                Contact
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowRight, HelpCircle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface ReportFormData {
  title: string;
  category: string;
  dateOfOccurrence: string;
  location: string;
  description: string;
  evidenceFiles: File[];
  evidenceDescription: string;
  privacyConsent: boolean;
  reportToken?: string;
  submissionId?: string;
}

interface IncidentInfoStepProps {
  formData: ReportFormData;
  updateFormData: (updates: Partial<ReportFormData>) => void;
  onNext: () => void;
}

const categories = [
  "Financial Misconduct",
  "Accounting Fraud / Financial Manipulation", 
  "Corruption",
  "Harassment",
  "Safety Violation",
  "Environmental",
  "Discrimination",
  "Other"
];

export default function IncidentInfoStep({ formData, updateFormData, onNext }: IncidentInfoStepProps) {
  const handleInputChange = (field: keyof ReportFormData, value: string) => {
    updateFormData({ [field]: value });
  };

  const isFormValid = () => {
    return (
      formData.title.trim() !== '' &&
      formData.category !== '' &&
      formData.description.trim() !== ''
    );
  };

  const handleNext = () => {
    if (isFormValid()) {
      onNext();
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-gray-900">
          Report Incident Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Report Title */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="title" className="text-sm font-medium text-gray-700">
              Report Title*
            </Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-gray-400" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Provide a clear, concise title for your report</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Input
            id="title"
            type="text"
            placeholder="Provide a clear, concise title for your report"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            className="w-full"
            maxLength={200}
          />
        </div>

        {/* Category */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="category" className="text-sm font-medium text-gray-700">
              Category*
            </Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-gray-400" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Select the most relevant category</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select the most relevant category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Date of Occurrence */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="dateOfOccurrence" className="text-sm font-medium text-gray-700">
              Date of Occurrence
            </Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-gray-400" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>When did the incident occur? (Optional)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Input
            id="dateOfOccurrence"
            type="date"
            value={formData.dateOfOccurrence}
            onChange={(e) => handleInputChange('dateOfOccurrence', e.target.value)}
            className="w-full"
            max={new Date().toISOString().split('T')[0]} // Prevent future dates
          />
        </div>

        {/* Location */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="location" className="text-sm font-medium text-gray-700">
              Location
            </Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-gray-400" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Department, office, building, or location where the incident occurred</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Input
            id="location"
            type="text"
            placeholder="Department, office, building, or location where the incident occurred"
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            className="w-full"
            maxLength={200}
          />
        </div>

        {/* Incident Details */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="description" className="text-sm font-medium text-gray-700">
              Incident Details*
            </Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-gray-400" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Provide a detailed description of the incident or concern. Include what happened, where, when, and any other relevant information. Be as specific as possible.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Textarea
            id="description"
            placeholder="Provide a detailed description of the incident or concern. Include what happened, where, when, and any other relevant information. Be as specific as possible."
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            className="w-full min-h-[120px] resize-none"
            maxLength={2000}
          />
          <div className="text-xs text-gray-500 text-right">
            {formData.description.length}/2000 characters
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between pt-6">
          <div></div> {/* Empty div for spacing */}
          <Button
            onClick={handleNext}
            disabled={!isFormValid()}
            className="bg-[#1E4841] hover:bg-[#1E4841]/90 text-white px-6 py-2 flex items-center gap-2"
          >
            Next
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

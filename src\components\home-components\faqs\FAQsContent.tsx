"use client";

import { useState, useMemo } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";

// Comprehensive FAQ data
const allFAQs = [
  // General Questions
  {
    id: 1,
    question: "What is a whistleblower platform?",
    answer: "A whistleblower platform is a secure system that allows employees and stakeholders to report misconduct, fraud, or unethical behavior within an organization while protecting their identity and ensuring proper investigation procedures.",
    category: "general",
    tags: ["basics", "definition"]
  },
  {
    id: 2,
    question: "Who can use this platform?",
    answer: "Our platform is designed for organizations of all sizes, their employees, contractors, and external stakeholders who need to report concerns about misconduct or compliance violations.",
    category: "general",
    tags: ["users", "access"]
  },
  {
    id: 3,
    question: "Is my identity protected when I submit a report?",
    answer: "Yes, we offer multiple levels of anonymity protection. You can choose to remain completely anonymous or provide limited contact information for follow-up questions. All reports are encrypted and access is strictly controlled.",
    category: "security",
    tags: ["anonymity", "protection", "privacy"]
  },
  {
    id: 4,
    question: "What types of misconduct can I report?",
    answer: "You can report various types of misconduct including financial fraud, safety violations, discrimination, harassment, corruption, environmental violations, and other ethical concerns.",
    category: "reporting",
    tags: ["misconduct", "types", "violations"]
  },
  {
    id: 5,
    question: "How secure is the platform?",
    answer: "Our platform uses enterprise-grade encryption, secure servers, and follows international security standards including SOC 2 Type II and ISO 27001. All data is protected with multiple layers of security.",
    category: "security",
    tags: ["encryption", "standards", "protection"]
  },
  // Pricing Questions
  {
    id: 6,
    question: "What's included in each pricing plan?",
    answer: "Each plan includes access to our core compliance tools, including report submission, case management, and basic analytics. Higher-tier plans offer additional features like advanced reporting, bulk operations, and API access.",
    category: "pricing",
    tags: ["plans", "features", "included"]
  },
  {
    id: 7,
    question: "Can I change my plan later?",
    answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect at the start of your next billing cycle, and we'll prorate any differences.",
    category: "pricing",
    tags: ["upgrade", "downgrade", "billing"]
  },
  {
    id: 8,
    question: "Do you offer a free trial?",
    answer: "Yes, we offer a 14-day free trial on all plans so you can test our platform before committing. No credit card required to start your trial.",
    category: "pricing",
    tags: ["trial", "free", "testing"]
  },
  {
    id: 9,
    question: "How does billing work?",
    answer: "Billing is monthly or annual depending on your chosen plan. Annual plans receive a 20% discount. All payments are processed securely through our certified payment partners.",
    category: "pricing",
    tags: ["billing", "payment", "annual"]
  },
  {
    id: 10,
    question: "Can I cancel anytime?",
    answer: "Yes, you can cancel your subscription at any time. Your access will continue until the end of your current billing period, and we don't charge any cancellation fees.",
    category: "pricing",
    tags: ["cancel", "subscription", "fees"]
  },
  // Support Questions
  {
    id: 11,
    question: "What support do you provide?",
    answer: "We offer comprehensive support including documentation, video tutorials, email support, live chat, and priority phone support for Professional and Enterprise plans.",
    category: "support",
    tags: ["help", "documentation", "contact"]
  },
  {
    id: 12,
    question: "How quickly will I get support?",
    answer: "Response times vary by plan: Basic (24-48 hours), Professional (4-8 hours), Enterprise (1-2 hours). Critical security issues are prioritized across all plans.",
    category: "support",
    tags: ["response", "time", "priority"]
  },
  // Technical Questions
  {
    id: 13,
    question: "Do you offer API access?",
    answer: "Yes, API access is available on Professional and Enterprise plans. Our REST API allows you to integrate with existing systems and automate workflows.",
    category: "technical",
    tags: ["api", "integration", "automation"]
  },
  {
    id: 14,
    question: "Can I integrate with my existing systems?",
    answer: "Yes, we offer various integration options including API access, webhooks, and pre-built connectors for popular business systems like Slack, Microsoft Teams, and JIRA.",
    category: "technical",
    tags: ["integration", "systems", "connectors"]
  },
  {
    id: 15,
    question: "What file types can I upload as evidence?",
    answer: "You can upload various file types including documents (PDF, DOC, DOCX), images (JPG, PNG, GIF), spreadsheets (XLS, XLSX), and other common formats. Files are scanned for security before processing.",
    category: "technical",
    tags: ["files", "upload", "evidence"]
  }
];

const categories = [
  { value: "all", label: "All Categories", count: 0 },
  { value: "general", label: "General", count: 0 },
  { value: "pricing", label: "Pricing", count: 0 },
  { value: "security", label: "Security", count: 0 },
  { value: "reporting", label: "Reporting", count: 0 },
  { value: "support", label: "Support", count: 0 },
  { value: "technical", label: "Technical", count: 0 }
];

interface FAQsContentProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
}

export default function FAQsContent({ searchTerm, onSearchChange }: FAQsContentProps) {
  const [activeTab, setActiveTab] = useState("all");

  // Calculate category counts
  const categoriesWithCounts = useMemo(() => {
    const counts = allFAQs.reduce((acc, faq) => {
      acc[faq.category] = (acc[faq.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return categories.map(cat => ({
      ...cat,
      count: cat.value === "all" ? allFAQs.length : (counts[cat.value] || 0)
    }));
  }, []);

  // Filter FAQs based on search term and active tab
  const filteredFAQs = useMemo(() => {
    return allFAQs.filter(faq => {
      const matchesSearch = searchTerm === "" ||
        faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
        faq.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory = activeTab === "all" || faq.category === activeTab;

      return matchesSearch && matchesCategory;
    });
  }, [searchTerm, activeTab]);



  return (
    <section className="py-16 px-4 sm:px-8 md:px-12 lg:px-[180px]">
      <div className="max-w-6xl mx-auto">
        {/* Search Results Display */}
        {searchTerm && (
          <div className="text-center mb-8">
            <p className="text-gray-600 text-lg">
              {filteredFAQs.length} {filteredFAQs.length === 1 ? 'result' : 'results'} found for &ldquo;{searchTerm}&rdquo;
            </p>
            <Button
              variant="ghost"
              onClick={() => onSearchChange("")}
              className="text-[#1E4841] hover:bg-[#1E4841]/10 mt-2"
            >
              Clear search
            </Button>
          </div>
        )}

        {/* Tabs Section */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-7 mb-12 bg-gray-100 p-2 rounded-lg h-auto">
            {categoriesWithCounts.map((category) => (
              <TabsTrigger
                key={category.value}
                value={category.value}
                className="flex flex-col items-center gap-2 py-4 px-3 text-sm font-medium bg-transparent data-[state=active]:bg-[#1E4841] data-[state=active]:text-white data-[state=inactive]:text-gray-600 hover:bg-gray-200 data-[state=active]:hover:bg-[#1E4841] transition-all duration-200 rounded-md group"
              >
                <span className="truncate font-semibold">{category.label}</span>
                <Badge
                  variant="secondary"
                  className="text-xs group-data-[state=active]:bg-white/20 group-data-[state=active]:text-white bg-gray-200 text-gray-600 border-0 min-w-[24px] h-6 font-medium"
                >
                  {category.count}
                </Badge>
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Tab Content */}
          {categoriesWithCounts.map((category) => (
            <TabsContent key={category.value} value={category.value} className="mt-0">
              {filteredFAQs.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-gray-500 text-lg mb-4">
                    {searchTerm
                      ? `No FAQs found matching &ldquo;${searchTerm}&rdquo; in ${category.label.toLowerCase()}.`
                      : `No FAQs available in ${category.label.toLowerCase()}.`
                    }
                  </p>
                  {searchTerm && (
                    <Button
                      onClick={() => onSearchChange("")}
                      className="bg-[#1E4841] hover:bg-[#1E4841]/90 text-white"
                    >
                      Clear search
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <Accordion type="single" collapsible className="w-full space-y-4">
                    {filteredFAQs.map((faq) => (
                      <AccordionItem
                        key={faq.id}
                        value={`item-${faq.id}`}
                        className="rounded-lg px-6 py-2 hover:shadow-sm transition-shadow border border-gray-200 bg-white"
                      >
                        <AccordionTrigger className="text-left hover:no-underline text-lg font-semibold text-[#242E2C]">
                          {faq.question}
                        </AccordionTrigger>
                        <AccordionContent className="text-[#242E2C] pt-2 pb-4">
                          {faq.answer}
                          <div className="flex flex-wrap gap-2 mt-3">
                            {faq.tags.map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>

        {/* Contact Support Section */}
        <div className="mt-16 text-center bg-[#F9FAFB] rounded-lg p-8">
          <h3 className="text-2xl font-bold text-[#1E4841] mb-4">
            Still have questions?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Can&apos;t find the answer you&apos;re looking for? Our support team is here to help.
            Get in touch and we&apos;ll get back to you as soon as possible.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              asChild
              className="bg-[#1E4841] hover:bg-[#1E4841]/90 text-white px-6 py-3"
            >
              <a href="/contact">Contact Support</a>
            </Button>
            <Button 
              variant="outline" 
              asChild
              className="border-[#1E4841] text-[#1E4841] hover:bg-[#1E4841] hover:text-white px-6 py-3"
            >
              <a href="/contact">Schedule a Demo</a>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}

"use client";
import Image from "next/image";
import Link from "next/link";
import { memo, useState, useCallback, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import { LOGIN_ITEMS, SIGNUP_ITEMS } from "@/lib/mockData";

interface MobileDropdownMenuProps {
    onItemClick?: () => void;
}

const MobileDropdownMenu = memo(({ onItemClick }: MobileDropdownMenuProps) => {
    const [isLoginOpen, setIsLoginOpen] = useState(false);
    const [isSignupOpen, setIsSignupOpen] = useState(false);
    const loginContentRef = useRef<HTMLDivElement>(null);
    const signupContentRef = useRef<HTMLDivElement>(null);

    const toggleLogin = useCallback((e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsLoginOpen(prev => !prev);
        setIsSignupOpen(false); // Close signup when opening login
    }, []);

    const toggleSignup = useCallback((e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsSignupOpen(prev => !prev);
        setIsLoginOpen(false); // Close login when opening signup
    }, []);

    // Enhanced focus management for login dropdown
    useEffect(() => {
        if (isLoginOpen && loginContentRef.current) {
            const firstLink = loginContentRef.current.querySelector('a');
            if (firstLink) {
                setTimeout(() => firstLink.focus(), 150);
            }
        }
    }, [isLoginOpen]);

    // Enhanced focus management for signup dropdown
    useEffect(() => {
        if (isSignupOpen && signupContentRef.current) {
            const firstLink = signupContentRef.current.querySelector('a');
            if (firstLink) {
                setTimeout(() => firstLink.focus(), 150);
            }
        }
    }, [isSignupOpen]);

    return (
        <DropdownMenuContent sideOffset={5} align="end" className="w-[280px] bg-[#ECF4E9] border border-green-200 animate-in slide-in-from-top-2 duration-200">
            {/* Action Buttons */}
            <div className="space-y-2">
                <DropdownMenuItem asChild>
                    <Link href="/start-trial-free" onClick={onItemClick}>
                        <Button className="w-full bg-[#1E4841] text-white hover:bg-green-900 active:bg-green-800 hover:text-gray-100 transition-all duration-200 font-medium justify-center flex items-center gap-2 touch-manipulation focus:ring-2 focus:ring-offset-2 focus:ring-[#1E4841]">
                            <Image
                                src="/desktop/shared/header/start-trial-free.svg"
                                alt="start trial free icon"
                                height={12}
                                width={17}
                            />
                            Start Trial Free
                        </Button>
                    </Link>
                </DropdownMenuItem>

                {/* Login Dropdown */}
                <div className="py-1 border-y border-[#1E484110]">
                    <Button
                        variant="ghost"
                        onClick={toggleLogin}
                        className="w-full flex justify-between items-center px-3 py-1 text-sm font-semibold text-[#1E4841] opacity-70 hover:bg-green-100 active:bg-green-200 transition-colors duration-200 touch-manipulation focus:ring-2 focus:ring-[#1E4841] focus:ring-offset-1"
                        aria-expanded={isLoginOpen}
                        aria-controls="login-dropdown-content"
                    >
                        <span>Login</span>
                        <ChevronDown
                            className={`transition-transform duration-200 ease-out ${isLoginOpen ? 'rotate-180' : ''}`}
                            size={14}
                            aria-hidden="true"
                        />
                    </Button>
                    {isLoginOpen && (
                        <div
                            ref={loginContentRef}
                            id="login-dropdown-content"
                            className="space-y-1 animate-in slide-in-from-top-1 duration-150 ease-out"
                            role="region"
                            aria-label="Login options"
                        >
                            {LOGIN_ITEMS.map((item) => (
                                <DropdownMenuItem key={item.href} asChild>
                                    <Link
                                        href={item.href}
                                        className="flex items-center px-3 py-1 text-sm text-[#1E4841] hover:bg-green-100 active:bg-green-200 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#1E4841] focus:ring-offset-1 touch-manipulation"
                                        onClick={onItemClick}
                                    >
                                        {item.title}
                                    </Link>
                                </DropdownMenuItem>
                            ))}
                        </div>
                    )}
                </div>

                {/* Signup Dropdown */}
                <div className="py-1">
                    <Button
                        onClick={toggleSignup}
                        className="w-full flex justify-between items-center px-3 py-1 text-sm font-semibold text-[#1E4841] bg-[#BBF49C] hover:bg-lime-200 active:bg-lime-300 transition-colors duration-200 touch-manipulation focus:ring-2 focus:ring-[#1E4841] focus:ring-offset-1"
                        aria-expanded={isSignupOpen}
                        aria-controls="signup-dropdown-content"
                    >
                        <span>Signup</span>
                        <ChevronDown
                            className={`transition-transform duration-200 ease-out ${isSignupOpen ? 'rotate-180' : ''}`}
                            size={14}
                            aria-hidden="true"
                        />
                    </Button>
                    {isSignupOpen && (
                        <div
                            ref={signupContentRef}
                            id="signup-dropdown-content"
                            className="space-y-1 animate-in slide-in-from-top-1 duration-150 ease-out"
                            role="region"
                            aria-label="Signup options"
                        >
                            {SIGNUP_ITEMS.map((item) => (
                                <DropdownMenuItem key={item.href} asChild>
                                    <Link
                                        href={item.href}
                                        className="flex items-center px-3 py-1 text-sm text-[#1E4841] hover:bg-green-100 active:bg-green-200 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#1E4841] focus:ring-offset-1 touch-manipulation"
                                        onClick={onItemClick}
                                    >
                                        {item.title}
                                    </Link>
                                </DropdownMenuItem>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </DropdownMenuContent>
    );
});

MobileDropdownMenu.displayName = 'MobileDropdownMenu';

export default MobileDropdownMenu;
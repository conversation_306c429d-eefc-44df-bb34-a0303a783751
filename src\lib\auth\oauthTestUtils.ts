/**
 * OAuth Testing Utilities
 * Helper functions for testing OAuth authentication flow
 */

import { assignRoleToOAuthUser, validateOAuthUserDomain } from './roleAssignment';
import { sanitizeForLog } from '@/lib/utils/security';

export interface MockOAuthProfile {
  email: string;
  name: string;
  provider: 'google' | 'azure-ad';
  sub?: string;
  picture?: string;
  given_name?: string;
  family_name?: string;
  email_verified?: boolean;
}

/**
 * Test OAuth profiles for different scenarios
 */
export const TEST_OAUTH_PROFILES: Record<string, MockOAuthProfile> = {
  // Admin user from admin domain
  adminUser: {
    email: '<EMAIL>',
    name: 'Admin User',
    provider: 'google',
    sub: 'google_admin_123',
    given_name: 'Admin',
    family_name: 'User',
    email_verified: true
  },

  // Regular whistleblower
  whistleblower: {
    email: '<EMAIL>',
    name: '<PERSON>',
    provider: 'google',
    sub: 'google_user_456',
    given_name: '<PERSON>',
    family_name: '<PERSON><PERSON>',
    email_verified: true
  },

  // HR investigator
  investigator: {
    email: '<EMAIL>',
    name: 'HR Manager',
    provider: 'azure-ad',
    sub: 'azure_hr_789',
    given_name: 'HR',
    family_name: 'Manager',
    email_verified: true
  },

  // Blocked domain user
  blockedUser: {
    email: '<EMAIL>',
    name: 'Temp User',
    provider: 'google',
    sub: 'google_temp_999',
    email_verified: true
  },

  // Microsoft user
  microsoftUser: {
    email: '<EMAIL>',
    name: 'Microsoft User',
    provider: 'azure-ad',
    sub: 'azure_outlook_111',
    given_name: 'Microsoft',
    family_name: 'User',
    email_verified: true
  }
};

/**
 * Test role assignment for different user profiles
 */
export function testRoleAssignment() {
  console.log('🧪 Testing OAuth Role Assignment\n');

  Object.entries(TEST_OAUTH_PROFILES).forEach(([key, profile]) => {
    const domain = profile.email.split('@')[1];
    
    console.log(`Testing: ${key} (${sanitizeForLog(profile.email)})`);

    // Test domain validation
    const domainValidation = validateOAuthUserDomain(profile.email);
    console.log(`  Domain validation: ${domainValidation.allowed ? '✅' : '❌'} - ${sanitizeForLog(domainValidation.reason)}`);

    if (domainValidation.allowed) {
      // Test role assignment
      const roleAssignment = assignRoleToOAuthUser({
        email: profile.email,
        domain,
        name: profile.name,
        provider: profile.provider
      });

      console.log(`  Assigned role: ${sanitizeForLog(roleAssignment.role)}`);
      console.log(`  Reason: ${sanitizeForLog(roleAssignment.reason)}`);
      console.log(`  Requires approval: ${roleAssignment.requiresApproval ? 'Yes' : 'No'}`);
    }
    
    console.log('');
  });
}

/**
 * Simulate OAuth callback data
 */
export function createMockOAuthCallback(profile: MockOAuthProfile) {
  return {
    user: {
      id: profile.sub || `${profile.provider}_${Date.now()}`,
      email: profile.email,
      name: profile.name,
      image: profile.picture || `https://ui-avatars.com/api/?name=${encodeURIComponent(profile.name)}`
    },
    account: {
      provider: profile.provider,
      type: 'oauth',
      providerAccountId: profile.sub || `${profile.provider}_${Date.now()}`,
      access_token: 'mock_access_token',
      token_type: 'Bearer',
      scope: 'openid email profile'
    },
    profile: {
      sub: profile.sub,
      name: profile.name,
      given_name: profile.given_name,
      family_name: profile.family_name,
      picture: profile.picture,
      email: profile.email,
      email_verified: profile.email_verified
    }
  };
}

/**
 * Test OAuth flow simulation
 */
export async function simulateOAuthFlow(profileKey: keyof typeof TEST_OAUTH_PROFILES) {
  const profile = TEST_OAUTH_PROFILES[profileKey];
  if (!profile) {
    throw new Error(`Test profile '${profileKey}' not found`);
  }

  console.log(`🔄 Simulating OAuth flow for: ${sanitizeForLog(profile.email)}`);

  // Step 1: Domain validation
  const domainValidation = validateOAuthUserDomain(profile.email);
  if (!domainValidation.allowed) {
    console.log(`❌ OAuth flow blocked: ${sanitizeForLog(domainValidation.reason)}`);
    return { success: false, reason: domainValidation.reason };
  }

  // Step 2: Role assignment
  const domain = profile.email.split('@')[1];
  const roleAssignment = assignRoleToOAuthUser({
    email: profile.email,
    domain,
    name: profile.name,
    provider: profile.provider
  });

  // Step 3: Create mock callback data
  const callbackData = createMockOAuthCallback(profile);

  console.log(`✅ OAuth flow simulation completed`);
  console.log(`   Role: ${sanitizeForLog(roleAssignment.role)}`);
  console.log(`   Active: ${!roleAssignment.requiresApproval}`);
  console.log(`   Provider: ${sanitizeForLog(profile.provider)}`);

  return {
    success: true,
    profile,
    roleAssignment,
    callbackData,
    userData: {
      email: profile.email,
      firstName: profile.given_name || profile.name.split(' ')[0],
      lastName: profile.family_name || profile.name.split(' ').slice(1).join(' '),
      role: roleAssignment.role,
      isActive: !roleAssignment.requiresApproval,
      emailVerified: true,
      oauthProvider: profile.provider,
      oauthId: profile.sub,
      profileImage: profile.picture,
      oauthProfile: {
        sub: profile.sub,
        name: profile.name,
        given_name: profile.given_name,
        family_name: profile.family_name,
        picture: profile.picture,
        email_verified: profile.email_verified
      }
    }
  };
}

/**
 * Run all OAuth tests
 */
export function runOAuthTests() {
  console.log('🚀 Running OAuth Authentication Tests\n');
  
  // Test role assignment
  testRoleAssignment();
  
  // Test OAuth flow simulations
  console.log('🔄 Testing OAuth Flow Simulations\n');
  
  Object.keys(TEST_OAUTH_PROFILES).forEach(async (profileKey) => {
    try {
      const result = await simulateOAuthFlow(profileKey as keyof typeof TEST_OAUTH_PROFILES);
      if (result.success) {
        console.log(`✅ ${sanitizeForLog(profileKey)}: OAuth flow successful`);
      } else {
        console.log(`❌ ${sanitizeForLog(profileKey)}: OAuth flow failed - ${sanitizeForLog(result.reason || '')}`);
      }
    } catch (error) {
      console.log(`💥 ${sanitizeForLog(profileKey)}: OAuth flow error - ${sanitizeForLog(String(error))}`);
    }
  });
  
  console.log('\n✨ OAuth tests completed');
}

/**
 * Validate OAuth configuration
 */
export function validateOAuthConfig() {
  console.log('🔧 Validating OAuth Configuration\n');
  
  const requiredEnvVars = [
    'NEXTAUTH_URL',
    'NEXTAUTH_SECRET',
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'AZURE_AD_CLIENT_ID',
    'AZURE_AD_CLIENT_SECRET',
    'AZURE_AD_TENANT_ID'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.log('❌ Missing required environment variables:');
    missingVars.forEach(varName => console.log(`   - ${varName}`));
    return false;
  }
  
  console.log('✅ All required environment variables are set');
  
  // Validate URLs
  try {
    new URL(process.env.NEXTAUTH_URL!);
    console.log('✅ NEXTAUTH_URL is valid');
  } catch {
    console.log('❌ NEXTAUTH_URL is not a valid URL');
    return false;
  }
  
  console.log('✅ OAuth configuration is valid');
  return true;
}

// Export for use in development/testing
if (typeof window === 'undefined' && process.env.NODE_ENV === 'development') {
  // Only run in Node.js environment during development
  global.oauthTestUtils = {
    testRoleAssignment,
    simulateOAuthFlow,
    runOAuthTests,
    validateOAuthConfig,
    TEST_OAUTH_PROFILES
  };
}

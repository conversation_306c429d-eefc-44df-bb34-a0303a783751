"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter, useParams } from "next/navigation";
import Header from "@/components/dashboard-components/Header";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
    ChevronLeft,
    Send,
    LockKeyhole,
    Timer,
    CheckCheck,
    LogOut
} from "lucide-react";
import { notifyMessageCountUpdate } from "@/lib/utils/messageIndicators";
import { apiClient } from "@/lib/utils/apiClient";
import { useAuth } from "@/hooks/useAuth";
import { ApiResponse } from "@/lib/types";

export default function MobileChatPage() {
    const router = useRouter();
    const params = useParams();
    const conversationId = params.conversationId as string;
    const { user } = useAuth();
    
    const [newMessage, setNewMessage] = useState<string>("");
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [autoLogoutTime, setAutoLogoutTime] = useState<number>(30 * 60); // 30 minutes for admin
    const [showTypingIndicator] = useState<boolean>(false);
    const [showAutoLogout, setShowAutoLogout] = useState<boolean>(false);

    const [conversation, setConversation] = useState<Record<string, unknown> | null>(null);
    const [conversationMessages, setConversationMessages] = useState<Array<Record<string, unknown>>>([]);
    const [loadingMessages, setLoadingMessages] = useState(true);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    // Load conversation and messages
    useEffect(() => {
        const loadConversationData = async () => {
            try {
                setLoadingMessages(true);

                // Load conversation details
                const convResponse = await apiClient.get(`/api/conversations/${conversationId}`) as { success: boolean; data: Record<string, unknown> & { isUnread?: boolean } };
                if (convResponse.success) {
                    setConversation(convResponse.data as Record<string, unknown>);

                    // Mark as read if unread
                    if (convResponse.data.isUnread) {
                        await apiClient.post('/api/conversations/read-status', {
                            conversationId: conversationId
                        });
                        notifyMessageCountUpdate();
                    }
                }

                // Load messages
                const messagesResponse = await apiClient.get(`/api/messages?conversationId=${conversationId}`) as { success: boolean; data: Array<Record<string, unknown>> };
                if (messagesResponse.success) {
                    setConversationMessages((messagesResponse.data || []) as Array<Record<string, unknown>>);
                }
            } catch (error) {
                console.error('Error loading conversation data:', error);
            } finally {
                setLoadingMessages(false);
            }
        };

        if (conversationId) {
            loadConversationData();
        }
    }, [conversationId]);

    useEffect(() => {
        if (!showAutoLogout) return;

        const timer = setInterval(() => {
            setAutoLogoutTime(prev => {
                if (prev <= 1) {
                    router.push('/logout');
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [router, showAutoLogout]);

    const resetAutoLogoutTimer = useCallback(() => {
        setAutoLogoutTime(30 * 60); // 30 minutes for admin
        setShowAutoLogout(false);
    }, []);

    useEffect(() => {
        let inactivityTimeout: NodeJS.Timeout;

        const handleUserActivity = () => {
            resetAutoLogoutTimer();

            if (inactivityTimeout) {
                clearTimeout(inactivityTimeout);
            }

            inactivityTimeout = setTimeout(() => {
                setShowAutoLogout(true);
            }, 5 * 60 * 1000); // 5 minutes for admin
        };

        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

        events.forEach(event => {
            document.addEventListener(event, handleUserActivity, true);
        });

        handleUserActivity();

        return () => {
            events.forEach(event => {
                document.removeEventListener(event, handleUserActivity, true);
            });
            if (inactivityTimeout) {
                clearTimeout(inactivityTimeout);
            }
        };
    }, [resetAutoLogoutTimer]);

    const formatAutoLogoutTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    const handleSendMessage = async () => {
        if (!newMessage.trim()) return;

        const messageContent = newMessage.trim();
        setIsLoading(true);
        setNewMessage("");

        try {
            const response = await apiClient.post('/api/messages', {
                conversationId,
                content: messageContent,
                messageType: 'text'
            }) as ApiResponse;

            if (response.success) {
                // Reload messages to show the new message
                const messagesResponse = await apiClient.get(`/api/messages?conversationId=${conversationId}`) as { success: boolean; data: Array<Record<string, unknown>> };
                if (messagesResponse.success) {
                    setConversationMessages(messagesResponse.data || []);
                }
            } else {
                console.error('Failed to send message:', response.error);
                setNewMessage(messageContent);
            }
        } catch (error) {
            console.error('Error sending message:', error);
            setNewMessage(messageContent);
        } finally {
            setIsLoading(false);
            resetAutoLogoutTimer();
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key.toLowerCase()) {
                case 'enter':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        handleSendMessage();
                    }
                    break;
            }
        }
    };

    const handleBack = () => {
        router.push('/dashboard/admin/secure-message');
    };

    if (!conversation) {
        return (
            <div className="w-full h-screen flex flex-col">
                <Header />
                <main id="main-content" className="flex-1 bg-white flex items-center justify-center">
                    <div className="text-center">
                        <h1 className="text-xl font-semibold text-[#242E2C] mb-2">Conversation Not Found</h1>
                        <p className="text-sm text-[#6B7280] mb-4">The conversation you&apos;re looking for doesn&apos;t exist.</p>
                        <Button onClick={handleBack} className="bg-[#1E4841] text-white hover:bg-[#1E4841]/90">
                            Back to Messages
                        </Button>
                    </div>
                </main>
            </div>
        );
    }

    return (
        <div className="w-full h-screen flex flex-col">
            <Header />
            <main id="main-content" className="flex-1 bg-white" aria-label="Secure message chat">
                <div className="p-4 border-b flex items-center gap-3 bg-gray-50">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleBack}
                        className="p-1"
                        aria-label="Back to conversations"
                    >
                        <ChevronLeft className="w-5 h-5 text-[#6B7271]" />
                    </Button>
                    <div className="relative">
                        <div className={`h-10 w-10 rounded-full ${String(conversation?.avatarBg ?? '')} flex items-center justify-center`}>
                            <span className="text-sm font-medium text-[#1E4841]">
                                {String(conversation?.name ?? '')
                                  .split(' ')
                                  .map(n => n[0])
                                  .join('')
                                  .slice(0, 2)}
                            </span>
                        </div>
                        {Boolean((conversation as { isOnline?: boolean })?.isOnline) && (
                            <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                        )}
                    </div>
                    <div className="flex-1">
                        <div className="flex items-center gap-2">
                            <p className="text-base font-medium text-[#111827]">{String(conversation?.name ?? '')}</p>
                            {Boolean((conversation as { isOnline?: boolean })?.isOnline) && (
                                <span className="text-xs text-green-600 font-medium">Online</span>
                            )}
                        </div>
                        <div className="flex items-center gap-2 text-xs text-[#6B7280]">
                            <span>{String((conversation as { caseId?: string })?.caseId ?? '')}</span>
                            <span className="text-xs">•</span>
                            <LockKeyhole className="w-3 h-3" />
                            <span>Encrypted</span>
                        </div>
                    </div>
                </div>
                
                <div className="flex-1 overflow-y-auto p-4 space-y-4 min-h-0">
                    <div className="text-center">
                        <p className="text-xs text-[#4B5563] bg-gray-100 px-3 py-1 rounded-full inline-block">
                            May 15, 2025
                        </p>
                    </div>
                    
                    <div className="flex justify-center">
                        <div className="flex items-center gap-2 text-xs text-[#1E4841] bg-[#ECF4E9] px-4 py-2 rounded-lg max-w-2xl text-center">
                            <LockKeyhole className="w-4 h-4" />
                            <span>This conversation is end-to-end encrypted.</span>
                        </div>
                    </div>
                    
                    {loadingMessages ? (
                        <div className="flex justify-center items-center py-8">
                            <div className="w-6 h-6 border-2 border-[#BBF49C] border-t-transparent rounded-full animate-spin" />
                            <span className="ml-2 text-sm text-gray-500">Loading messages...</span>
                        </div>
                    ) : (
                        conversationMessages.map((message) => {
                            const senderRole = (message as { senderId?: { role?: string } }).senderId?.role;
                            const senderId = (message as { senderId?: { _id?: string; id?: string } }).senderId?._id || (message as { senderId?: { id?: string } }).senderId?.id;
                            const messageSenderId = (message as { senderId?: string }).senderId || senderId;
                            const isCurrentUserMessage = messageSenderId === user?.id || senderId === user?.id;
                            const showOnRight = isCurrentUserMessage;


                            const messageId = (message as { _id?: string; id?: string })._id || (message as { id?: string }).id || 'unknown';

                            const timestamp = (() => {
                                const ts = (message as { timestamp?: unknown }).timestamp;
                                if (ts) {
                                    const d = typeof ts === 'string' || typeof ts === 'number'
                                        ? new Date(ts)
                                        : ts instanceof Date
                                        ? ts
                                        : new Date();
                                    return d.toLocaleTimeString('en-US', {
                                        hour: 'numeric',
                                        minute: '2-digit',
                                        hour12: true
                                    });
                                }
                                return 'Now';
                            })();

                            return (
                                <div key={messageId}>
                                    {showOnRight ? (
                                <div className="flex items-start gap-2 justify-end">
                                    <div className="flex flex-col gap-1 max-w-[80%]">
                                        <div className="bg-[#ECF4E9] rounded-2xl rounded-tr-md p-3">
                                            <p className="text-sm text-[#1F2937]">
                                                {String((message as { content?: string }).content ?? '')}
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-2 text-xs text-[#6B7280] justify-end px-2">
                                            <LockKeyhole className="w-3 h-3" />
                                            <span>Encrypted</span>
                                            <span>•</span>
                                            <CheckCheck className="w-3 h-3 text-green-600" />
                                            <span>Read</span>
                                            <span>•</span>
                                            <span>{timestamp}</span>
                                        </div>
                                    </div>
                                    <div className="w-8 h-8 bg-[#1E4841] rounded-full flex items-center justify-center text-white text-sm font-medium">
                                        {(((message as { senderId?: { firstName?: string } }).senderId?.firstName) || 'You').charAt(0)}
                                    </div>
                                </div>
                            ) : (
                                <div className="flex items-start gap-2">
                                    <div className="w-8 h-8 bg-[#BBF49C] rounded-full flex items-center justify-center text-[#1E4841] text-sm font-medium">
                                        {(((message as { senderId?: { firstName?: string } }).senderId?.firstName) || (senderRole === 'whistleblower' ? 'W' : 'U')).charAt(0)}
                                    </div>
                                    <div className="flex flex-col gap-1 max-w-[80%]">
                                        <div className="bg-[#F9FAFB] border border-[#E5E7EB] rounded-2xl rounded-tl-md p-3">
                                            <p className="text-sm text-[#1F2937]">
                                                {String((message as { content?: string }).content ?? '')}
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-2 text-xs text-[#6B7280] px-2">
                                            <span>{timestamp}</span>
                                            <span>•</span>
                                            <LockKeyhole className="w-3 h-3" />
                                            <span>Encrypted</span>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                        );
                    })
                    )}



                    {showTypingIndicator && (
                        <div className="flex items-start gap-2">
                            <div className={`h-8 w-8 rounded-full ${String((conversation as { avatarBg?: string })?.avatarBg ?? '')} flex items-center justify-center flex-shrink-0`}>
                                <span className="text-xs font-medium text-[#1E4841]">
                                    {String((conversation as { name?: string })?.name ?? '').split(' ').map(n => n[0]).join('').slice(0, 2)}
                                </span>
                            </div>
                            <div className="bg-[#F9FAFB] border border-[#E5E7EB] rounded-2xl rounded-tl-md p-3">
                                <div className="flex items-center gap-1">
                                    <div className="w-2 h-2 bg-[#6B7280] rounded-full animate-bounce"></div>
                                    <div className="w-2 h-2 bg-[#6B7280] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                    <div className="w-2 h-2 bg-[#6B7280] rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
                
                <div className="p-4 border-t bg-gray-50">
                    {showAutoLogout && (
                        <div className="flex items-center gap-2 text-xs text-[#FF2121] mb-3">
                            <Timer className="w-4 h-4" />
                            <span>Auto-logout in {formatAutoLogoutTime(autoLogoutTime)}</span>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => router.push('/logout')}
                                className="ml-auto p-1 text-[#FF2121] hover:bg-red-50"
                                aria-label="Logout now"
                            >
                                <LogOut className="w-3 h-3" />
                            </Button>
                        </div>
                    )}
                    <div className="flex gap-2">
                        <Textarea
                            ref={textareaRef}
                            name="message"
                            aria-label="Type a message"
                            placeholder="Type your message... (Ctrl+Enter to send)"
                            value={newMessage}
                            onChange={(e) => {
                                setNewMessage(e.target.value);
                                resetAutoLogoutTimer();
                            }}
                            className="flex-1 min-h-[60px] resize-none border-2 border-[#D1D5DB] focus:border-[#1E4841] focus:ring-[#1E4841]"
                            onKeyDown={handleKeyDown}
                        />
                        <Button
                            onClick={handleSendMessage}
                            disabled={!newMessage.trim() || isLoading}
                            className="px-4 py-2 bg-[#1E4841] text-white hover:bg-[#1E4841]/90 disabled:opacity-50 self-end"
                            aria-label="Send message"
                        >
                            {isLoading ? (
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            ) : (
                                <Send className="w-4 h-4" />
                            )}
                        </Button>
                    </div>
                    <p className="text-xs text-[#6B7280] mt-2">
                        Press Ctrl+Enter to send • All messages are encrypted
                    </p>
                </div>
            </main>
        </div>
    );
}
import nodemailer from 'nodemailer';
import logger from '@/lib/utils/logger';
import { createWelcomeEmailTemplate } from './templates/welcomeTemplate';
import { createReportSubmissionTemplate } from './templates/reportTemplate';

interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject: string;
  message: string;
  department?: string;
}

interface UserData {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
}

interface ReportData {
  id: string;
  title: string;
  category: string;
  priority: string;
  status: string;
  submittedAt: Date;
}

interface DemoRequestData {
  fullName: string;
  email: string;
  company: string;
  jobTitle: string;
  phone?: string;
  companySize: string;
  industry: string;
  message: string;
  preferredDate?: string;
  preferredTime?: string;
}

interface SupportRequestNotificationData {
  ticketNumber: string;
  priorityLevel: string;
  issueCategory: string;
  issueDescription: string;
  fullName: string;
  emailAddress: string;
  attachmentCount: number;
  supportEmail: string;
}

interface SupportRequestAutoReplyData {
  ticketNumber: string;
  fullName: string;
  emailAddress: string;
  priorityLevel: string;
}

interface CustomerSuccessNotificationData {
  inquiryNumber: string;
  subject: string;
  message: string;
  preferredContactMethod: string;
  preferredTimeSlot: string;
  isUrgent: boolean;
  customerName: string;
  customerEmail: string;
  customerSuccessEmail: string;
}

interface CustomerSuccessAutoReplyData {
  inquiryNumber: string;
  customerName: string;
  customerEmail: string;
  subject: string;
  isUrgent: boolean;
}

interface MediaInquiryNotificationData {
  inquiryNumber: string;
  mediaOrganization: string;
  journalistName: string;
  email: string;
  phoneNumber: string;
  typeOfInquiry: string;
  deadline: Date;
  detailedMessage: string;
  priority: string;
  credentialsCount: number;
  mediaEmail: string;
}

interface MediaInquiryAutoReplyData {
  inquiryNumber: string;
  journalistName: string;
  email: string;
  mediaOrganization: string;
  typeOfInquiry: string;
  deadline: Date;
}

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.EMAIL_SERVER_HOST || process.env.SMTP_HOST,
      port: parseInt(process.env.EMAIL_SERVER_PORT || process.env.SMTP_PORT || '587'),
      secure: process.env.EMAIL_SERVER_SECURE === 'true' || process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_SERVER_USER || process.env.SMTP_USER,
        pass: process.env.EMAIL_SERVER_PASSWORD || process.env.SMTP_PASS,
      },
      tls: {
        rejectUnauthorized: false // For development/testing
      }
    });
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      const mailOptions = {
        from: `"${process.env.SMTP_FROM_NAME || 'Whistleblower System'}" <${process.env.EMAIL_SERVER_USER || process.env.SMTP_USER}>`,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
        attachments: options.attachments,
      };

      const info = await this.transporter.sendMail(mailOptions);
      logger.info('Email sent successfully', { messageId: info.messageId, to: options.to, subject: options.subject });
      return true;
    } catch (error) {
      logger.error('Error sending email', { error, to: options.to, subject: options.subject });
      return false;
    }
  }

  async sendContactFormEmail(formData: ContactFormData): Promise<boolean> {
    try {
      const recipientEmail = process.env.CONTACT_FORM_RECIPIENT || process.env.EMAIL_SERVER_USER || process.env.SMTP_USER;
      
      if (!recipientEmail) {
        console.error('No recipient email configured for contact form');
        return false;
      }

      const departmentMap = {
        sales: 'Sales Team',
        technical: 'Technical Support',
        customer: 'Customer Service',
        media: 'Media Relations'
      };

      const departmentName = formData.department 
        ? departmentMap[formData.department as keyof typeof departmentMap] || 'General Inquiry'
        : 'General Inquiry';

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="border-bottom: 3px solid #1E4841; padding-bottom: 20px; margin-bottom: 30px;">
              <h1 style="color: #1E4841; margin: 0; font-size: 24px;">New Contact Form Submission</h1>
              <p style="color: #6B7280; margin: 5px 0 0 0; font-size: 14px;">Department: ${departmentName}</p>
            </div>
            
            <div style="margin-bottom: 25px;">
              <h2 style="color: #1E4841; font-size: 18px; margin-bottom: 15px; border-left: 4px solid #BBF49C; padding-left: 15px;">Contact Information</h2>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151; width: 120px;">Name:</td>
                  <td style="padding: 8px 0; color: #6B7280;">${formData.name}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151;">Email:</td>
                  <td style="padding: 8px 0; color: #6B7280;"><a href="mailto:${formData.email}" style="color: #1E4841; text-decoration: none;">${formData.email}</a></td>
                </tr>
                ${formData.phone ? `
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151;">Phone:</td>
                  <td style="padding: 8px 0; color: #6B7280;"><a href="tel:${formData.phone}" style="color: #1E4841; text-decoration: none;">${formData.phone}</a></td>
                </tr>
                ` : ''}
                ${formData.company ? `
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151;">Company:</td>
                  <td style="padding: 8px 0; color: #6B7280;">${formData.company}</td>
                </tr>
                ` : ''}
              </table>
            </div>
            
            <div style="margin-bottom: 25px;">
              <h2 style="color: #1E4841; font-size: 18px; margin-bottom: 15px; border-left: 4px solid #BBF49C; padding-left: 15px;">Subject</h2>
              <p style="color: #374151; font-size: 16px; margin: 0; font-weight: 500;">${formData.subject}</p>
            </div>
            
            <div style="margin-bottom: 25px;">
              <h2 style="color: #1E4841; font-size: 18px; margin-bottom: 15px; border-left: 4px solid #BBF49C; padding-left: 15px;">Message</h2>
              <div style="background-color: #F9FAFB; padding: 20px; border-radius: 6px; border-left: 4px solid #BBF49C;">
                <p style="color: #374151; line-height: 1.6; margin: 0; white-space: pre-wrap;">${formData.message}</p>
              </div>
            </div>
            
            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; text-align: center;">
              <p style="color: #6B7280; font-size: 12px; margin: 0;">
                This message was sent via the Whistleblower System contact form.<br>
                Submitted on ${new Date().toLocaleString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric', 
                  hour: 'numeric', 
                  minute: '2-digit',
                  timeZoneName: 'short'
                })}
              </p>
            </div>
          </div>
        </div>
      `;

      const textContent = `
New Contact Form Submission - ${departmentName}

Contact Information:
Name: ${formData.name}
Email: ${formData.email}
${formData.phone ? `Phone: ${formData.phone}` : ''}
${formData.company ? `Company: ${formData.company}` : ''}

Subject: ${formData.subject}

Message:
${formData.message}

---
This message was sent via the Whistleblower System contact form.
Submitted on ${new Date().toLocaleString()}
      `;

      return await this.sendEmail({
        to: recipientEmail,
        subject: `Contact Form: ${formData.subject} - ${departmentName}`,
        text: textContent,
        html: htmlContent
      });
    } catch (error) {
      console.error('Error sending contact form email:', error);
      return false;
    }
  }

  async sendAutoReplyEmail(formData: ContactFormData): Promise<boolean> {
    try {
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="border-bottom: 3px solid #1E4841; padding-bottom: 20px; margin-bottom: 30px; text-align: center;">
              <h1 style="color: #1E4841; margin: 0; font-size: 24px;">Thank You for Contacting Us</h1>
            </div>
            
            <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">Dear ${formData.name},</p>
            
            <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
              Thank you for reaching out to us through our contact form. We have received your message regarding 
              "<strong>${formData.subject}</strong>" and appreciate you taking the time to contact us.
            </p>
            
            <div style="background-color: #F0F9FF; padding: 20px; border-radius: 6px; border-left: 4px solid #BBF49C; margin: 25px 0;">
              <h3 style="color: #1E4841; margin: 0 0 10px 0; font-size: 16px;">What happens next?</h3>
              <ul style="color: #374151; margin: 0; padding-left: 20px;">
                <li style="margin-bottom: 8px;">Our team will review your message within 24-48 hours</li>
                <li style="margin-bottom: 8px;">You'll receive a personalized response from the appropriate department</li>
                <li style="margin-bottom: 8px;">For urgent matters, please call our support line</li>
              </ul>
            </div>
            
            <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
              If you have any additional questions or need immediate assistance, please don't hesitate to reach out to us.
            </p>
            
            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; text-align: center;">
              <p style="color: #1E4841; font-weight: bold; margin: 0 0 5px 0;">Whistleblower System Team</p>
              <p style="color: #6B7280; font-size: 12px; margin: 0;">
                This is an automated response. Please do not reply to this email.
              </p>
            </div>
          </div>
        </div>
      `;

      const textContent = `
Thank You for Contacting Us

Dear ${formData.name},

Thank you for reaching out to us through our contact form. We have received your message regarding "${formData.subject}" and appreciate you taking the time to contact us.

What happens next?
- Our team will review your message within 24-48 hours
- You'll receive a personalized response from the appropriate department
- For urgent matters, please call our support line

If you have any additional questions or need immediate assistance, please don't hesitate to reach out to us.

Best regards,
Whistleblower System Team

---
This is an automated response. Please do not reply to this email.
      `;

      return await this.sendEmail({
        to: formData.email,
        subject: `Thank you for contacting us - ${formData.subject}`,
        text: textContent,
        html: htmlContent
      });
    } catch (error) {
      console.error('Error sending auto-reply email:', error);
      return false;
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info('SMTP connection verified successfully');
      return true;
    } catch (error) {
      logger.error('SMTP connection failed:', error);
      return false;
    }
  }

  // User Registration and Authentication Emails
  async sendWelcomeEmail(userData: UserData): Promise<boolean> {
    try {
      const { html, text } = createWelcomeEmailTemplate({
        firstName: userData.firstName,
        email: userData.email,
        role: userData.role,
        accountId: userData.id
      });

      return await this.sendEmail({
        to: userData.email,
        subject: 'Welcome to Whistleblower System - Account Created Successfully',
        text,
        html
      });
    } catch (error) {
      logger.error('Error sending welcome email:', error);
      return false;
    }
  }

  async sendPasswordResetEmail(email: string, resetToken: string): Promise<boolean> {
    try {
      const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${resetToken}`;

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="border-bottom: 3px solid #1E4841; padding-bottom: 20px; margin-bottom: 30px; text-align: center;">
              <h1 style="color: #1E4841; margin: 0; font-size: 24px;">Password Reset Request</h1>
            </div>

            <div style="margin-bottom: 30px;">
              <p style="color: #555; line-height: 1.6; margin-bottom: 20px;">
                We received a request to reset your password for your Whistleblower System account.
              </p>

              <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <p style="margin: 0; color: #856404; font-weight: bold;">⚠️ Security Notice</p>
                <p style="margin: 5px 0 0 0; color: #856404;">
                  This link will expire in 1 hour for your security. If you didn't request this reset, please ignore this email.
                </p>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${resetUrl}"
                   style="background-color: #1E4841; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                  Reset Your Password
                </a>
              </div>

              <p style="color: #666; font-size: 14px; text-align: center;">
                Or copy and paste this link into your browser:<br>
                <span style="word-break: break-all; color: #1E4841;">${resetUrl}</span>
              </p>
            </div>

            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; text-align: center;">
              <p style="color: #6B7280; font-size: 12px; margin: 0;">
                If you continue to have problems, please contact our support team.<br>
                This email was sent to ${email}
              </p>
            </div>
          </div>
        </div>
      `;

      const textContent = `
Password Reset Request

We received a request to reset your password for your Whistleblower System account.

To reset your password, click the link below:
${resetUrl}

This link will expire in 1 hour for your security.

If you didn't request this reset, please ignore this email.

If you continue to have problems, please contact our support team.
      `;

      return await this.sendEmail({
        to: email,
        subject: 'Password Reset Request - Whistleblower System',
        text: textContent,
        html: htmlContent
      });
    } catch (error) {
      logger.error('Error sending password reset email:', error);
      return false;
    }
  }

  // Report-related emails
  async sendReportSubmissionNotification(reportData: ReportData, submitterEmail: string): Promise<boolean> {
    try {
      const { html, text } = createReportSubmissionTemplate({
        reportId: reportData.id,
        title: reportData.title,
        category: reportData.category,
        priority: reportData.priority,
        status: reportData.status,
        submittedAt: reportData.submittedAt,
        submitterEmail
      });

      return await this.sendEmail({
        to: submitterEmail,
        subject: `Report Submitted Successfully - ID: ${reportData.id}`,
        text,
        html
      });
    } catch (error) {
      logger.error('Error sending report submission notification:', error);
      return false;
    }
  }

  async sendReportAssignmentNotification(reportData: ReportData, assigneeEmail: string, assigneeName: string): Promise<boolean> {
    try {
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="border-bottom: 3px solid #1E4841; padding-bottom: 20px; margin-bottom: 30px; text-align: center;">
              <h1 style="color: #1E4841; margin: 0; font-size: 24px;">New Report Assignment</h1>
            </div>

            <div style="margin-bottom: 30px;">
              <p style="color: #555; line-height: 1.6; margin-bottom: 20px;">
                Hello ${assigneeName}, a new report has been assigned to you for investigation.
              </p>

              <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
                <h3 style="color: #1E4841; margin: 0 0 15px 0; font-size: 16px;">Report Details:</h3>
                <p style="margin: 5px 0; color: #555;"><strong>Report ID:</strong> ${reportData.id}</p>
                <p style="margin: 5px 0; color: #555;"><strong>Title:</strong> ${reportData.title}</p>
                <p style="margin: 5px 0; color: #555;"><strong>Category:</strong> ${reportData.category}</p>
                <p style="margin: 5px 0; color: #555;"><strong>Priority:</strong> <span style="color: ${reportData.priority === 'urgent' ? '#dc3545' : reportData.priority === 'high' ? '#fd7e14' : '#28a745'};">${reportData.priority.toUpperCase()}</span></p>
                <p style="margin: 5px 0; color: #555;"><strong>Submitted:</strong> ${reportData.submittedAt.toLocaleDateString()}</p>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard/admin/reports/${reportData.id}"
                   style="background-color: #1E4841; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                  Review Report
                </a>
              </div>
            </div>

            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; text-align: center;">
              <p style="color: #6B7280; font-size: 12px; margin: 0;">
                Please handle this report with confidentiality and according to company policies.<br>
                Report ID: ${reportData.id}
              </p>
            </div>
          </div>
        </div>
      `;

      return await this.sendEmail({
        to: assigneeEmail,
        subject: `New Report Assignment - ${reportData.title} (${reportData.priority.toUpperCase()})`,
        html: htmlContent
      });
    } catch (error) {
      logger.error('Error sending report assignment notification:', error);
      return false;
    }
  }

  // Demo and Sales emails
  async sendDemoRequestNotification(demoData: DemoRequestData): Promise<boolean> {
    try {
      const salesEmail = process.env.SALES_EMAIL || process.env.CONTACT_FORM_RECIPIENT || process.env.EMAIL_SERVER_USER;

      if (!salesEmail) {
        logger.error('No sales email configured for demo requests');
        return false;
      }

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="border-bottom: 3px solid #1E4841; padding-bottom: 20px; margin-bottom: 30px; text-align: center;">
              <h1 style="color: #1E4841; margin: 0; font-size: 24px;">🎯 New Demo Request</h1>
            </div>

            <div style="margin-bottom: 30px;">
              <p style="color: #555; line-height: 1.6; margin-bottom: 20px;">
                A new demo request has been submitted through the website.
              </p>

              <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
                <h3 style="color: #1E4841; margin: 0 0 15px 0; font-size: 16px;">Contact Information:</h3>
                <p style="margin: 5px 0; color: #555;"><strong>Name:</strong> ${demoData.fullName}</p>
                <p style="margin: 5px 0; color: #555;"><strong>Email:</strong> ${demoData.email}</p>
                <p style="margin: 5px 0; color: #555;"><strong>Company:</strong> ${demoData.company}</p>
                <p style="margin: 5px 0; color: #555;"><strong>Job Title:</strong> ${demoData.jobTitle}</p>
                ${demoData.phone ? `<p style="margin: 5px 0; color: #555;"><strong>Phone:</strong> ${demoData.phone}</p>` : ''}
                <p style="margin: 5px 0; color: #555;"><strong>Company Size:</strong> ${demoData.companySize}</p>
                <p style="margin: 5px 0; color: #555;"><strong>Industry:</strong> ${demoData.industry}</p>
              </div>

              ${demoData.preferredDate || demoData.preferredTime ? `
              <div style="background-color: #e3f2fd; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <h3 style="color: #1565c0; margin: 0 0 10px 0; font-size: 16px;">📅 Scheduling Preferences:</h3>
                ${demoData.preferredDate ? `<p style="margin: 5px 0; color: #1565c0;"><strong>Preferred Date:</strong> ${demoData.preferredDate}</p>` : ''}
                ${demoData.preferredTime ? `<p style="margin: 5px 0; color: #1565c0;"><strong>Preferred Time:</strong> ${demoData.preferredTime}</p>` : ''}
              </div>
              ` : ''}

              <div style="background-color: #fff3e0; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <h3 style="color: #ef6c00; margin: 0 0 10px 0; font-size: 16px;">💬 Message:</h3>
                <p style="margin: 0; color: #ef6c00; white-space: pre-wrap;">${demoData.message}</p>
              </div>
            </div>

            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; text-align: center;">
              <p style="color: #6B7280; font-size: 12px; margin: 0;">
                Demo request submitted on ${new Date().toLocaleString()}<br>
                Follow up within 24 hours for best conversion rates
              </p>
            </div>
          </div>
        </div>
      `;

      return await this.sendEmail({
        to: salesEmail,
        subject: `🎯 New Demo Request from ${demoData.fullName} at ${demoData.company}`,
        html: htmlContent
      });
    } catch (error) {
      logger.error('Error sending demo request notification:', error);
      return false;
    }
  }

  async sendDemoConfirmationEmail(demoData: DemoRequestData): Promise<boolean> {
    try {
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="border-bottom: 3px solid #1E4841; padding-bottom: 20px; margin-bottom: 30px; text-align: center;">
              <h1 style="color: #1E4841; margin: 0; font-size: 24px;">Thank You for Your Demo Request!</h1>
            </div>

            <div style="margin-bottom: 30px;">
              <p style="color: #555; line-height: 1.6; margin-bottom: 20px;">
                Dear ${demoData.fullName},
              </p>
              <p style="color: #555; line-height: 1.6; margin-bottom: 20px;">
                Thank you for your interest in our Whistleblower System! We've received your demo request and our sales team will contact you within 24 hours to schedule a personalized demonstration.
              </p>

              <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
                <h3 style="color: #1E4841; margin: 0 0 15px 0; font-size: 16px;">What to Expect:</h3>
                <ul style="color: #555; line-height: 1.8; padding-left: 20px; margin: 0;">
                  <li>A personalized demo tailored to your industry and company size</li>
                  <li>Discussion of your specific compliance and reporting needs</li>
                  <li>Overview of our security features and data protection</li>
                  <li>Pricing information and implementation timeline</li>
                  <li>Q&A session to address all your questions</li>
                </ul>
              </div>

              <div style="background-color: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <p style="margin: 0; color: #2e7d32; font-weight: bold;">📞 Next Steps</p>
                <p style="margin: 5px 0 0 0; color: #2e7d32;">
                  Our sales representative will reach out to you at ${demoData.email} ${demoData.phone ? `or ${demoData.phone}` : ''} to schedule your demo at a convenient time.
                </p>
              </div>
            </div>

            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; text-align: center;">
              <p style="color: #6B7280; font-size: 12px; margin: 0;">
                If you have any immediate questions, please don't hesitate to contact us.<br>
                This email was sent to ${demoData.email}
              </p>
            </div>
          </div>
        </div>
      `;

      return await this.sendEmail({
        to: demoData.email,
        subject: 'Demo Request Confirmed - We\'ll Contact You Soon!',
        html: htmlContent
      });
    } catch (error) {
      logger.error('Error sending demo confirmation email:', error);
      return false;
    }
  }

  // Newsletter emails
  async sendNewsletterWelcomeEmail(email: string): Promise<boolean> {
    try {
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="border-bottom: 3px solid #1E4841; padding-bottom: 20px; margin-bottom: 30px; text-align: center;">
              <h1 style="color: #1E4841; margin: 0; font-size: 24px;">Welcome to Our Newsletter!</h1>
            </div>

            <div style="margin-bottom: 30px;">
              <p style="color: #555; line-height: 1.6; margin-bottom: 20px;">
                Thank you for subscribing to the Whistleblower System newsletter! You'll now receive:
              </p>

              <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
                <ul style="color: #555; line-height: 1.8; padding-left: 20px; margin: 0;">
                  <li>Latest updates on compliance and whistleblowing regulations</li>
                  <li>Best practices for creating ethical workplace cultures</li>
                  <li>Product updates and new feature announcements</li>
                  <li>Industry insights and case studies</li>
                  <li>Exclusive content and early access to resources</li>
                </ul>
              </div>

              <p style="color: #555; line-height: 1.6; margin-bottom: 20px;">
                We respect your privacy and will never share your email address. You can unsubscribe at any time.
              </p>
            </div>

            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; text-align: center;">
              <p style="color: #6B7280; font-size: 12px; margin: 0;">
                You're receiving this because you subscribed to our newsletter at ${email}<br>
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/unsubscribe?email=${encodeURIComponent(email)}" style="color: #6B7280;">Unsubscribe</a>
              </p>
            </div>
          </div>
        </div>
      `;

      return await this.sendEmail({
        to: email,
        subject: 'Welcome to Whistleblower System Newsletter!',
        html: htmlContent
      });
    } catch (error) {
      logger.error('Error sending newsletter welcome email:', error);
      return false;
    }
  }

  // Account security emails
  async sendAccountUnlockEmail(email: string, unlockToken: string): Promise<boolean> {
    try {
      const unlockUrl = `${process.env.NEXT_PUBLIC_APP_URL}/unlock-account?token=${unlockToken}`;

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="border-bottom: 3px solid #dc3545; padding-bottom: 20px; margin-bottom: 30px; text-align: center;">
              <h1 style="color: #dc3545; margin: 0; font-size: 24px;">🔒 Account Locked</h1>
            </div>

            <div style="margin-bottom: 30px;">
              <p style="color: #555; line-height: 1.6; margin-bottom: 20px;">
                Your account has been temporarily locked due to multiple failed login attempts. This is a security measure to protect your account.
              </p>

              <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <p style="margin: 0; color: #856404; font-weight: bold;">⚠️ Security Notice</p>
                <p style="margin: 5px 0 0 0; color: #856404;">
                  This unlock link will expire in 1 hour. If you didn't attempt to log in, please contact support immediately.
                </p>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${unlockUrl}"
                   style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                  Unlock My Account
                </a>
              </div>
            </div>

            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; text-align: center;">
              <p style="color: #6B7280; font-size: 12px; margin: 0;">
                If you continue to have problems, please contact our support team.<br>
                This email was sent to ${email}
              </p>
            </div>
          </div>
        </div>
      `;

      return await this.sendEmail({
        to: email,
        subject: '🔒 Account Locked - Unlock Required',
        html: htmlContent
      });
    } catch (error) {
      logger.error('Error sending account unlock email:', error);
      return false;
    }
  }

  async sendSupportRequestNotification(data: SupportRequestNotificationData): Promise<boolean> {
    try {
      const priorityColors = {
        urgent: '#DC2626',
        high: '#EA580C',
        medium: '#D97706',
        low: '#059669'
      };

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <div style="background-color: #1E4841; padding: 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px;">New Support Request</h1>
          </div>

          <div style="padding: 30px; background-color: #ffffff;">
            <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #1F2937; margin: 0 0 10px 0; font-size: 18px;">Ticket #${data.ticketNumber}</h2>
              <div style="display: inline-block; background-color: ${priorityColors[data.priorityLevel as keyof typeof priorityColors] || '#6B7280'}; color: white; padding: 4px 12px; border-radius: 16px; font-size: 12px; font-weight: bold; text-transform: uppercase;">
                ${data.priorityLevel} Priority
              </div>
            </div>

            <div style="margin-bottom: 20px;">
              <h3 style="color: #374151; margin: 0 0 10px 0; font-size: 16px;">Customer Information</h3>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Name:</strong> ${data.fullName}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Email:</strong> ${data.emailAddress}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Category:</strong> ${data.issueCategory.replace('_', ' ').toUpperCase()}</p>
            </div>

            <div style="margin-bottom: 20px;">
              <h3 style="color: #374151; margin: 0 0 10px 0; font-size: 16px;">Issue Description</h3>
              <div style="background-color: #F9FAFB; padding: 15px; border-radius: 6px; border-left: 4px solid #1E4841;">
                <p style="margin: 0; color: #374151; line-height: 1.6;">${data.issueDescription}</p>
              </div>
            </div>

            ${data.attachmentCount > 0 ? `
              <div style="margin-bottom: 20px;">
                <p style="color: #6B7280; margin: 0;"><strong>Attachments:</strong> ${data.attachmentCount} file(s) uploaded</p>
              </div>
            ` : ''}

            <div style="text-align: center; margin-top: 30px;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/support/${data.ticketNumber}"
                 style="background-color: #1E4841; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                View Support Request
              </a>
            </div>
          </div>
        </div>
      `;

      return await this.sendEmail({
        to: data.supportEmail,
        subject: `[${data.priorityLevel.toUpperCase()}] New Support Request - ${data.ticketNumber}`,
        html: htmlContent
      });
    } catch (error) {
      logger.error('Error sending support request notification:', error);
      return false;
    }
  }

  async sendSupportRequestAutoReply(data: SupportRequestAutoReplyData): Promise<boolean> {
    try {
      const slaMap = {
        urgent: '4 hours',
        high: '24 hours',
        medium: '48 hours',
        low: '72 hours'
      };

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <div style="background-color: #1E4841; padding: 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px;">Support Request Received</h1>
          </div>

          <div style="padding: 30px; background-color: #ffffff;">
            <p style="color: #374151; font-size: 16px; margin-bottom: 20px;">Dear ${data.fullName},</p>

            <p style="color: #6B7280; line-height: 1.6; margin-bottom: 20px;">
              Thank you for contacting our support team. We have received your support request and assigned it ticket number <strong>${data.ticketNumber}</strong>.
            </p>

            <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="color: #1F2937; margin: 0 0 10px 0; font-size: 16px;">Your Request Details</h3>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Ticket Number:</strong> ${data.ticketNumber}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Priority:</strong> ${data.priorityLevel.charAt(0).toUpperCase() + data.priorityLevel.slice(1)}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Expected Response Time:</strong> ${slaMap[data.priorityLevel as keyof typeof slaMap] || '48 hours'}</p>
            </div>

            <p style="color: #6B7280; line-height: 1.6; margin-bottom: 20px;">
              Our support team will review your request and respond within the expected timeframe. You can track the status of your request using your ticket number.
            </p>

            <div style="text-align: center; margin-top: 30px;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/support/track?ticket=${data.ticketNumber}&email=${encodeURIComponent(data.emailAddress)}"
                 style="background-color: #1E4841; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                Track Your Request
              </a>
            </div>

            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; margin-top: 30px;">
              <p style="color: #6B7280; font-size: 14px; margin: 0;">
                If you have any urgent questions, please contact us at ${process.env.SUPPORT_PHONE || '+971 4 123 4567'}.
              </p>
            </div>
          </div>
        </div>
      `;

      return await this.sendEmail({
        to: data.emailAddress,
        subject: `Support Request Received - ${data.ticketNumber}`,
        html: htmlContent
      });
    } catch (error) {
      logger.error('Error sending support request auto-reply:', error);
      return false;
    }
  }

  async sendCustomerSuccessNotification(data: CustomerSuccessNotificationData): Promise<boolean> {
    try {
      const subjectLabels = {
        account_setup: 'Account Setup & Onboarding',
        feature_training: 'Feature Training',
        best_practices: 'Best Practices Consultation',
        integration_support: 'Integration Support',
        performance_review: 'Performance Review',
        renewal_discussion: 'Renewal Discussion',
        other: 'Other'
      };

      const contactMethodLabels = {
        email: 'Email',
        phone: 'Phone Call',
        video_call: 'Video Call',
        in_person: 'In-Person Meeting'
      };

      const timeSlotLabels = {
        morning: 'Morning (9:00 AM - 12:00 PM)',
        afternoon: 'Afternoon (12:00 PM - 5:00 PM)',
        evening: 'Evening (5:00 PM - 8:00 PM)',
        flexible: 'Flexible'
      };

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <div style="background-color: #1E4841; padding: 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px;">New Customer Success Request</h1>
          </div>

          <div style="padding: 30px; background-color: #ffffff;">
            <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #1F2937; margin: 0 0 10px 0; font-size: 18px;">Inquiry #${data.inquiryNumber}</h2>
              ${data.isUrgent ? `
                <div style="display: inline-block; background-color: #DC2626; color: white; padding: 4px 12px; border-radius: 16px; font-size: 12px; font-weight: bold;">
                  URGENT
                </div>
              ` : ''}
            </div>

            <div style="margin-bottom: 20px;">
              <h3 style="color: #374151; margin: 0 0 10px 0; font-size: 16px;">Customer Information</h3>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Name:</strong> ${data.customerName}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Email:</strong> ${data.customerEmail}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Subject:</strong> ${subjectLabels[data.subject as keyof typeof subjectLabels]}</p>
            </div>

            <div style="margin-bottom: 20px;">
              <h3 style="color: #374151; margin: 0 0 10px 0; font-size: 16px;">Contact Preferences</h3>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Preferred Method:</strong> ${contactMethodLabels[data.preferredContactMethod as keyof typeof contactMethodLabels]}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Preferred Time:</strong> ${timeSlotLabels[data.preferredTimeSlot as keyof typeof timeSlotLabels]}</p>
            </div>

            <div style="margin-bottom: 20px;">
              <h3 style="color: #374151; margin: 0 0 10px 0; font-size: 16px;">Message</h3>
              <div style="background-color: #F9FAFB; padding: 15px; border-radius: 6px; border-left: 4px solid #1E4841;">
                <p style="margin: 0; color: #374151; line-height: 1.6;">${data.message}</p>
              </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/customer-success/${data.inquiryNumber}"
                 style="background-color: #1E4841; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                View Customer Request
              </a>
            </div>
          </div>
        </div>
      `;

      return await this.sendEmail({
        to: data.customerSuccessEmail,
        subject: `${data.isUrgent ? '[URGENT] ' : ''}New Customer Success Request - ${data.inquiryNumber}`,
        html: htmlContent
      });
    } catch (error) {
      logger.error('Error sending customer success notification:', error);
      return false;
    }
  }

  async sendCustomerSuccessAutoReply(data: CustomerSuccessAutoReplyData): Promise<boolean> {
    try {
      const subjectLabels = {
        account_setup: 'Account Setup & Onboarding',
        feature_training: 'Feature Training',
        best_practices: 'Best Practices Consultation',
        integration_support: 'Integration Support',
        performance_review: 'Performance Review',
        renewal_discussion: 'Renewal Discussion',
        other: 'Other'
      };

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <div style="background-color: #1E4841; padding: 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px;">Request Received</h1>
          </div>

          <div style="padding: 30px; background-color: #ffffff;">
            <p style="color: #374151; font-size: 16px; margin-bottom: 20px;">Dear ${data.customerName},</p>

            <p style="color: #6B7280; line-height: 1.6; margin-bottom: 20px;">
              Thank you for reaching out to our Customer Success team. We have received your request regarding <strong>${subjectLabels[data.subject as keyof typeof subjectLabels]}</strong> and assigned it inquiry number <strong>${data.inquiryNumber}</strong>.
            </p>

            <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="color: #1F2937; margin: 0 0 10px 0; font-size: 16px;">Your Customer Success Manager</h3>
              <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <div style="width: 50px; height: 50px; border-radius: 50%; background-color: #1E4841; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 15px;">
                  ST
                </div>
                <div>
                  <p style="margin: 0; color: #1F2937; font-weight: bold;">Sarah Thompson</p>
                  <p style="margin: 0; color: #6B7280; font-size: 14px;">Customer Success Manager</p>
                </div>
              </div>
              <p style="margin: 5px 0; color: #6B7280; font-size: 14px;"><strong>Email:</strong> <EMAIL></p>
              <p style="margin: 5px 0; color: #6B7280; font-size: 14px;"><strong>Phone:</strong> +971 99 127 6569</p>
            </div>

            <p style="color: #6B7280; line-height: 1.6; margin-bottom: 20px;">
              Sarah will contact you within ${data.isUrgent ? '24 hours' : '1 business day'} to discuss your request and schedule a time that works best for you.
            </p>

            <div style="text-align: center; margin-top: 30px;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/customer-success/track?inquiry=${data.inquiryNumber}&email=${encodeURIComponent(data.customerEmail)}"
                 style="background-color: #1E4841; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                Track Your Request
              </a>
            </div>

            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; margin-top: 30px;">
              <p style="color: #6B7280; font-size: 14px; margin: 0;">
                If you have any immediate questions, feel free to reach out to Sarah directly at the contact information above.
              </p>
            </div>
          </div>
        </div>
      `;

      return await this.sendEmail({
        to: data.customerEmail,
        subject: `Customer Success Request Received - ${data.inquiryNumber}`,
        html: htmlContent
      });
    } catch (error) {
      logger.error('Error sending customer success auto-reply:', error);
      return false;
    }
  }

  async sendMediaInquiryNotification(data: MediaInquiryNotificationData): Promise<boolean> {
    try {
      const inquiryTypeLabels = {
        interview_request: 'Interview Request',
        press_release: 'Press Release Inquiry',
        company_information: 'Company Information',
        product_demo: 'Product Demonstration',
        expert_commentary: 'Expert Commentary',
        case_study: 'Case Study Request',
        other: 'Other'
      };

      const priorityColors = {
        urgent: '#DC2626',
        high: '#EA580C',
        medium: '#D97706',
        low: '#059669'
      };

      const formatDate = (date: Date) => {
        return date.toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      };

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <div style="background-color: #1E4841; padding: 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px;">New Media Inquiry</h1>
          </div>

          <div style="padding: 30px; background-color: #ffffff;">
            <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #1F2937; margin: 0 0 10px 0; font-size: 18px;">Inquiry #${data.inquiryNumber}</h2>
              <div style="display: inline-block; background-color: ${priorityColors[data.priority as keyof typeof priorityColors] || '#6B7280'}; color: white; padding: 4px 12px; border-radius: 16px; font-size: 12px; font-weight: bold; text-transform: uppercase;">
                ${data.priority} Priority
              </div>
            </div>

            <div style="margin-bottom: 20px;">
              <h3 style="color: #374151; margin: 0 0 10px 0; font-size: 16px;">Journalist Information</h3>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Name:</strong> ${data.journalistName}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Organization:</strong> ${data.mediaOrganization}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Email:</strong> ${data.email}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Phone:</strong> ${data.phoneNumber}</p>
            </div>

            <div style="margin-bottom: 20px;">
              <h3 style="color: #374151; margin: 0 0 10px 0; font-size: 16px;">Inquiry Details</h3>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Type:</strong> ${inquiryTypeLabels[data.typeOfInquiry as keyof typeof inquiryTypeLabels]}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Deadline:</strong> ${formatDate(data.deadline)}</p>
              ${data.credentialsCount > 0 ? `<p style="margin: 5px 0; color: #6B7280;"><strong>Press Credentials:</strong> ${data.credentialsCount} file(s) uploaded</p>` : ''}
            </div>

            <div style="margin-bottom: 20px;">
              <h3 style="color: #374151; margin: 0 0 10px 0; font-size: 16px;">Message</h3>
              <div style="background-color: #F9FAFB; padding: 15px; border-radius: 6px; border-left: 4px solid #1E4841;">
                <p style="margin: 0; color: #374151; line-height: 1.6;">${data.detailedMessage}</p>
              </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/media/${data.inquiryNumber}"
                 style="background-color: #1E4841; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                View Media Inquiry
              </a>
            </div>
          </div>
        </div>
      `;

      return await this.sendEmail({
        to: data.mediaEmail,
        subject: `[${data.priority.toUpperCase()}] New Media Inquiry - ${data.inquiryNumber}`,
        html: htmlContent
      });
    } catch (error) {
      logger.error('Error sending media inquiry notification:', error);
      return false;
    }
  }

  async sendMediaInquiryAutoReply(data: MediaInquiryAutoReplyData): Promise<boolean> {
    try {
      const inquiryTypeLabels = {
        interview_request: 'Interview Request',
        press_release: 'Press Release Inquiry',
        company_information: 'Company Information',
        product_demo: 'Product Demonstration',
        expert_commentary: 'Expert Commentary',
        case_study: 'Case Study Request',
        other: 'Other'
      };

      const formatDate = (date: Date) => {
        return date.toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      };

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <div style="background-color: #1E4841; padding: 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px;">Media Inquiry Received</h1>
          </div>

          <div style="padding: 30px; background-color: #ffffff;">
            <p style="color: #374151; font-size: 16px; margin-bottom: 20px;">Dear ${data.journalistName},</p>

            <p style="color: #6B7280; line-height: 1.6; margin-bottom: 20px;">
              Thank you for your media inquiry regarding our whistleblowing platform. We have received your ${inquiryTypeLabels[data.typeOfInquiry as keyof typeof inquiryTypeLabels].toLowerCase()} and assigned it inquiry number <strong>${data.inquiryNumber}</strong>.
            </p>

            <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="color: #1F2937; margin: 0 0 10px 0; font-size: 16px;">Your Inquiry Details</h3>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Inquiry Number:</strong> ${data.inquiryNumber}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Organization:</strong> ${data.mediaOrganization}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Type:</strong> ${inquiryTypeLabels[data.typeOfInquiry as keyof typeof inquiryTypeLabels]}</p>
              <p style="margin: 5px 0; color: #6B7280;"><strong>Your Deadline:</strong> ${formatDate(data.deadline)}</p>
            </div>

            <p style="color: #6B7280; line-height: 1.6; margin-bottom: 20px;">
              Our media relations team will review your inquiry and respond within 1-2 business days. We understand the importance of meeting your deadline and will prioritize accordingly.
            </p>

            <div style="background-color: #FEF3C7; border: 1px solid #F59E0B; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
              <p style="color: #92400E; margin: 0; font-size: 14px;">
                <strong>For urgent press inquiries:</strong> Please call +971 6 574 1211 for immediate assistance.
              </p>
            </div>

            <div style="text-align: center; margin-top: 30px;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/media/track?inquiry=${data.inquiryNumber}&email=${encodeURIComponent(data.email)}"
                 style="background-color: #1E4841; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                Track Your Inquiry
              </a>
            </div>

            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; margin-top: 30px;">
              <p style="color: #6B7280; font-size: 14px; margin: 0;">
                This is an automated response. Please do not reply to this email. For questions about your inquiry, please use the tracking link above or contact our media relations team directly.
              </p>
            </div>
          </div>
        </div>
      `;

      return await this.sendEmail({
        to: data.email,
        subject: `Media Inquiry Received - ${data.inquiryNumber}`,
        html: htmlContent
      });
    } catch (error) {
      logger.error('Error sending media inquiry auto-reply:', error);
      return false;
    }
  }
}

export const emailService = new EmailService();
export default EmailService;
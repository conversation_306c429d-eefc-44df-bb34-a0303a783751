import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import connectDB from '@/lib/db/mongodb';
import { Report, Message } from '@/lib/db/models';
import { SecurityAuditLogger } from '@/lib/security/audit-logger';

export const runtime = 'nodejs';

// Validation schema for detailed tracking request
const trackDetailedSchema = z.object({
  reportToken: z.string().min(1, 'Report token is required')
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = trackDetailedSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { reportToken } = validationResult.data;

    await connectDB();

    // Find the anonymous report by reference number
    const report = await Report.findOne({ 
      referenceNumber: reportToken,
      isAnonymous: true 
    }).populate('assignedInvestigator', 'firstName lastName role');

    if (!report) {
      // Log failed access attempt
      await SecurityAuditLogger.logSuspiciousActivity(
        request,
        'Invalid report token for detailed tracking',
        {
          reportToken: reportToken,
          timestamp: new Date().toISOString()
        }
      );

      return NextResponse.json(
        { success: false, error: 'Invalid report token' },
        { status: 404 }
      );
    }

    // Get messages for this report
    const messages = await Message.find({
      reportId: report._id
    }).populate('senderId', 'firstName lastName role')
      .sort({ createdAt: 1 })
      .limit(50);

    // Helper function to get status details
    const getStatusDetails = (status: string) => {
      switch (status.toLowerCase()) {
        case 'under review':
          return {
            title: 'Under Review',
            description: 'Your report has been received and is currently under preliminary review by our internal administration team. The team will verify the completeness of the information received, the nature of the allegation, and determine the appropriate course of action.',
            assignedTo: 'Admin Team',
            priority: report.priority || 'Medium',
            note: 'All submitted information has been received and is being reviewed to determine if further investigation is required.'
          };
        case 'under investigation':
          return {
            title: 'Under Investigation',
            description: 'Following the completion of our initial review, your case has been assigned to a qualified investigator. The investigator will conduct a thorough and impartial investigation into the matters you have reported.',
            assignedTo: report.assignedInvestigator && typeof report.assignedInvestigator === 'object' && 'firstName' in report.assignedInvestigator
              ? `${(report.assignedInvestigator as any).firstName} ${(report.assignedInvestigator as any).lastName}`
              : 'Investigator Team',
            priority: 'High',
            nextUpdate: 'May 15, 2025',
            note: 'An investigator will be assigned within 48 hours to begin the formal investigation process.'
          };
        case 'closed':
          return {
            title: 'Case Closed Securely',
            description: 'This case has been successfully resolved and closed. All corrective actions have been verified as effective. Thank you for your contribution to maintaining our organizational integrity.',
            assignedTo: 'Admin Team',
            closureDate: report.lastUpdated?.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }) || 'Unknown',
            note: 'Case has been successfully resolved with appropriate corrective actions implemented.'
          };
        default:
          return {
            title: 'Under Review',
            description: 'Your report is being processed.',
            assignedTo: 'Admin Team',
            note: 'Your report is currently being reviewed by our team.'
          };
      }
    };

    // Helper function to get timeline data
    const getTimelineData = (status: string, messages: any[]) => {
      const items = [];

      // Add timeline items based on status and messages
      if (messages.length > 0) {
        messages.forEach((message, index) => {
          const isLast = index === messages.length - 1;
          const sender = message.senderId && typeof message.senderId === 'object' && 'role' in message.senderId
            ? message.senderId as any
            : null;

          items.push({
            title: sender?.role === 'system' ? 'System Update' :
                   sender?.role === 'admin' ? 'Admin Team' :
                   sender?.role === 'investigator' ? `${sender.firstName || ''} ${sender.lastName || ''}`.trim() || 'Investigator' :
                   'You (Anonymous)',
            description: message.content.substring(0, 150) + (message.content.length > 150 ? '...' : ''),
            timestamp: message.createdAt?.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            }) || 'Unknown',
            status: isLast && status.toLowerCase() !== 'closed' ? 'current' : 'completed',
            badge: sender?.role === 'admin' ? 'Admin Team' :
                   sender?.role === 'investigator' ? 'Investigator' : undefined
          });
        });
      }

      // Add final status if closed
      if (status.toLowerCase() === 'closed') {
        items.push({
          title: 'Communication Channel Locked',
          description: 'This channel has been closed and archived. No new messages can be sent.',
          timestamp: '',
          status: 'completed'
        });
      }

      return {
        title: status.toLowerCase() === 'closed' ? 'Case Timeline' : 'Investigation Timeline',
        items
      };
    };

    // Calculate progress based on status
    const getProgressSteps = (status: string) => {
      const steps = {
        submitted: { completed: true, current: false },
        underReview: { completed: false, current: false },
        investigation: { completed: false, current: false },
        actionTaken: { completed: false, current: false },
        closed: { completed: false, current: false }
      };

      switch (status.toLowerCase()) {
        case 'under review':
          steps.underReview = { completed: false, current: true };
          break;
        case 'under investigation':
          steps.underReview = { completed: true, current: false };
          steps.investigation = { completed: false, current: true };
          break;
        case 'closed':
          steps.underReview = { completed: true, current: false };
          steps.investigation = { completed: true, current: false };
          steps.actionTaken = { completed: true, current: false };
          steps.closed = { completed: true, current: false };
          break;
        default:
          steps.underReview = { completed: false, current: true };
      }

      return steps;
    };

    // Format the detailed response
    const reportData = {
      id: report.referenceNumber,
      reportId: report.reportId,
      submittedDate: report.dateSubmitted?.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }) || 'Unknown',
      lastUpdated: report.lastUpdated?.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }) || 'Unknown',
      status: report.status,
      progress: getProgressSteps(report.status),
      currentStatus: getStatusDetails(report.status),
      timeline: getTimelineData(report.status, messages),
      category: {
        type: report.category,
        description: report.description,
        department: report.departmentInvolved || 'Finance',
        location: report.location || 'Headquarters',
        incidentDate: report.dateOfOccurrence?.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long'
        }) || 'Unknown'
      },
      messages: messages.map(message => {
        const sender = message.senderId && typeof message.senderId === 'object' && 'role' in message.senderId
          ? message.senderId as any
          : null;

        return {
          id: message._id,
          sender: sender?.role === 'admin' || sender?.role === 'investigator'
            ? `${sender.firstName || ''} ${sender.lastName || ''}`.trim() || 'Staff Member'
            : 'You (Anonymous)',
          timestamp: message.createdAt?.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          }) || 'Unknown',
          message: message.content
        };
      }),
      additionalInfo: {
        description: 'If you have additional information that may assist our investigators, please provide it below. You can upload files or add text details.',
        referenceNumber: 'Please add reference information that may help the investigators.',
        note: report.status.toLowerCase() === 'closed' ? 'This case is now closed.' : undefined,
        additionalNote: report.status.toLowerCase() === 'closed' ? 'We are no longer accepting new evidence or updates for this investigation.' : undefined
      }
    };

    // Log successful access
    console.log('ANONYMOUS DETAILED TRACKING ACCESSED:', {
      reportId: report.reportId,
      reportToken: reportToken,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      data: reportData
    }, { status: 200 });

  } catch (error) {
    console.error('Detailed track report error:', error);
    
    await SecurityAuditLogger.logSuspiciousActivity(
      request,
      'Detailed report tracking failed',
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    );

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to load report details. Please try again.' 
      },
      { status: 500 }
    );
  }
}

import { NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import { AuditLog } from '@/lib/db/models';
import { IAuditLog } from '@/lib/db/models/interfaces';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Only admins can view audit logs
    if (request.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Admin role required.' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const actionType = searchParams.get('actionType');
    const category = searchParams.get('category');
    const severity = searchParams.get('severity');
    const userId = searchParams.get('userId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Build query for company isolation
    const query: any = { companyId: request.user.companyId };
    
    if (actionType) {
      query.actionType = actionType;
    }
    
    if (category) {
      query.category = category;
    }
    
    if (severity) {
      query.severity = severity;
    }
    
    if (userId) {
      query.userId = userId;
    }
    
    // Date range filter
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate);
      }
    }

    // Get audit logs
    const auditLogs = await AuditLog.find(query)
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(offset);

    // Transform data for frontend
    const transformedLogs = auditLogs.map((log: any) => ({
      id: log._id.toString(),
      // createdAt exists via Mongoose timestamps; cast to any for TS
      timestamp: new Date(log.createdAt).toISOString().replace('T', ' ').substring(0, 19),
      user: log.userName,
      userRole: log.userRole,
      action: log.action,
      actionType: log.actionType,
      details: log.description,
      resourceType: log.resourceType,
      resourceId: log.resourceId,
      ipAddress: log.ipAddress,
      severity: log.severity,
      category: log.category,
      isSystemGenerated: log.isSystemGenerated,
      isSuspicious: log.isSuspicious,
      metadata: log.metadata
    }));

    // Get total count for pagination
    const totalCount = await AuditLog.countDocuments(query);

    // Get summary statistics
    const stats = await AuditLog.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalLogs: { $sum: 1 },
          securityEvents: { $sum: { $cond: [{ $eq: ['$category', 'security'] }, 1, 0] } },
          suspiciousActivities: { $sum: { $cond: ['$isSuspicious', 1, 0] } },
          systemGenerated: { $sum: { $cond: ['$isSystemGenerated', 1, 0] } },
          highSeverity: { $sum: { $cond: [{ $eq: ['$severity', 'high'] }, 1, 0] } },
          criticalSeverity: { $sum: { $cond: [{ $eq: ['$severity', 'critical'] }, 1, 0] } }
        }
      }
    ]);

    const summary = stats[0] || {
      totalLogs: 0,
      securityEvents: 0,
      suspiciousActivities: 0,
      systemGenerated: 0,
      highSeverity: 0,
      criticalSeverity: 0
    };

    return NextResponse.json({
      success: true,
      data: transformedLogs,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      },
      summary
    });
  } catch (error) {
    console.error('Audit logs API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const logData = await request.json();
    
    // Validate required fields
    const requiredFields = ['action', 'actionType', 'resourceType', 'description'];
    for (const field of requiredFields) {
      if (!logData[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Get client IP address
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const ipAddress = forwardedFor?.split(',')[0] || realIp || 'unknown';

    // Create audit log entry
    const newAuditLog = await AuditLog.create({
      companyId: request.user.companyId,
      userId: request.user.id,
      userName: `${request.user.firstName || ''} ${request.user.lastName || ''}`.trim() || request.user.email,
      userRole: request.user.role,
      action: logData.action,
      actionType: logData.actionType,
      resourceType: logData.resourceType,
      resourceId: logData.resourceId,
      description: logData.description,
      details: logData.details,
      ipAddress,
      userAgent: request.headers.get('user-agent') || 'unknown',
      severity: logData.severity || 'low',
      category: logData.category || 'operational',
      isSystemGenerated: false,
      isSuspicious: logData.isSuspicious || false,
      metadata: {
        source: 'api',
        correlationId: logData.correlationId,
        tags: logData.tags || [],
        ...logData.metadata
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        id: (newAuditLog as unknown as IAuditLog)._id.toString(),
        timestamp: (newAuditLog as unknown as IAuditLog).createdAt.toISOString(),
        action: (newAuditLog as unknown as IAuditLog).action,
        description: (newAuditLog as unknown as IAuditLog).description
      },
      message: 'Audit log entry created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Create audit log API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { Report, Message } from '@/lib/db/models';

export const runtime = 'nodejs';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ referenceNumber: string }> }
) {
  try {
    await connectDB();
    
    const { referenceNumber } = await params;
    
    if (!referenceNumber) {
      return NextResponse.json(
        { success: false, error: 'Reference number is required' },
        { status: 400 }
      );
    }

    // Find report by reference number (try both original case and uppercase)
    let report = await Report.findOne({
      referenceNumber: referenceNumber.trim()
    }).populate('companyId', 'name');

    // If not found, try uppercase
    if (!report) {
      report = await Report.findOne({
        referenceNumber: referenceNumber.trim().toUpperCase()
      }).populate('companyId', 'name');
    }

    if (!report) {
      return NextResponse.json(
        { success: false, error: 'Report not found with this reference number' },
        { status: 404 }
      );
    }

    // For anonymous reports, provide detailed information
    if (report.isAnonymous) {
      // Get messages for this report
      const messages = await Message.find({
        reportId: report._id
      }).populate('senderId', 'firstName lastName role')
        .sort({ createdAt: 1 })
        .limit(50);

      // Helper function to get progress steps
      const getProgressSteps = (status: string) => {
        const steps = {
          submitted: { completed: true, current: false },
          underReview: { completed: false, current: false },
          investigation: { completed: false, current: false },
          actionTaken: { completed: false, current: false },
          closed: { completed: false, current: false }
        };

        switch (status.toLowerCase()) {
          case 'under review':
            steps.underReview = { completed: false, current: true };
            break;
          case 'under investigation':
            steps.underReview = { completed: true, current: false };
            steps.investigation = { completed: false, current: true };
            break;
          case 'closed':
            steps.underReview = { completed: true, current: false };
            steps.investigation = { completed: true, current: false };
            steps.actionTaken = { completed: true, current: false };
            steps.closed = { completed: true, current: false };
            break;
          default:
            steps.underReview = { completed: false, current: true };
        }

        return steps;
      };

      // Return detailed anonymous report information
      const trackingInfo = {
        id: report.referenceNumber,
        reportId: report.reportId,
        submittedDate: report.dateSubmitted?.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }) || 'Unknown',
        lastUpdated: report.lastUpdated?.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }) || 'Unknown',
        status: report.status,
        progress: getProgressSteps(report.status),
        category: {
          type: report.category,
          description: report.description,
          department: report.departmentInvolved || 'Finance',
          location: report.location || 'Headquarters',
          incidentDate: report.dateOfOccurrence?.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long'
          }) || 'Unknown'
        },
        messages: messages.map(message => {
          const sender = message.senderId && typeof message.senderId === 'object' && 'role' in message.senderId
            ? message.senderId as any
            : null;

          return {
            id: message._id,
            sender: sender?.role === 'admin' || sender?.role === 'investigator'
              ? `${sender.firstName || ''} ${sender.lastName || ''}`.trim() || 'Staff Member'
              : 'You (Anonymous)',
            timestamp: message.createdAt?.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            }) || 'Unknown',
            message: message.content
          };
        }),
        additionalInfo: {
          description: 'If you have additional information that may assist our investigators, please provide it below. You can upload files or add text details.',
          referenceNumber: 'Please add reference information that may help the investigators.',
          note: report.status.toLowerCase() === 'closed' ? 'This case is now closed.' : undefined,
          additionalNote: report.status.toLowerCase() === 'closed' ? 'We are no longer accepting new evidence or updates for this investigation.' : undefined
        }
      };

      return NextResponse.json({
        success: true,
        data: trackingInfo
      });
    }

    // For non-anonymous reports, return limited information
    const trackingInfo = {
      referenceNumber: report.referenceNumber,
      title: report.title,
      status: report.status,
      priority: report.priority,
      dateSubmitted: report.dateSubmitted,
      lastUpdated: report.lastUpdated,
      category: report.category,
      isAnonymous: report.isAnonymous,
      estimatedCompletion: report.estimatedCompletion,
      progress: report.progress || 0,
      company: report.companyId ? {
        name: (report.companyId as unknown as { name: string }).name
      } : null,
      assignedInvestigator: report.assignedInvestigator,
      statusHistory: report.statusHistory || []
    };

    return NextResponse.json({
      success: true,
      data: trackingInfo
    });

  } catch (error) {
    console.error('Report tracking error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

"use client";

import { Card, CardContent } from "@/components/ui/card";
import { MapPin, Phone, Mail, Clock, ArrowRight } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

/**
 * OfficeCard Component
 *
 * Displays office location information with real Google Maps integration.
 * Features:
 * - Interactive Google Maps embed showing exact office location
 * - Fallback to placeholder when Google Maps API key is not available
 * - Functional "Get Directions" button that opens Google Maps
 * - Responsive design that preserves the original layout
 *
 * Requirements:
 * - NEXT_PUBLIC_GOOGLE_MAPS_API_KEY environment variable for maps to work
 * - Internet connection for map loading
 */

interface OfficeCardProps {
  title: string;
  address: string;
  phone: string;
  email: string;
  hours: string;
  mapImage: string;
}

// Function to generate Google Maps embed URL
const getGoogleMapsEmbedUrl = (address: string) => {
  const encodedAddress = encodeURIComponent(address.replace(/\n/g, ', '));
  return `https://www.google.com/maps/embed/v1/place?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || 'demo'}&q=${encodedAddress}`;
};

// Function to generate Google Maps directions URL
const getGoogleMapsDirectionsUrl = (address: string) => {
  const encodedAddress = encodeURIComponent(address.replace(/\n/g, ', '));
  return `https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`;
};

export default function OfficeCard({
  title,
  address,
  phone,
  email,
  hours
}: Omit<OfficeCardProps, 'mapImage'>) {
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300 py-0 border-none shadow-md">
      <div className="relative h-48 bg-gray-200">
        {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? (
          <iframe
            src={getGoogleMapsEmbedUrl(address)}
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allowFullScreen
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            className="w-full h-full"
            title={`Map of ${title} office location`}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-[#1E4841]/10 to-[#1E4841]/20 flex items-center justify-center">
            <div className="text-center">
              <MapPin className="w-12 h-12 text-[#1E4841] mx-auto mb-2" />
              <p className="text-sm text-[#1E4841] font-medium">Office Location</p>
              <p className="text-xs text-gray-600 mt-1">Interactive Map</p>
            </div>
          </div>
        )}
      </div>

      <CardContent className="p-6 pt-0">
        <h3 className="text-xl font-semibold text-[#1E4841] mb-4">
          {title}
        </h3>

        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <MapPin className="w-5 h-5 text-gray-500 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-gray-600 whitespace-pre-line">
              {address}
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Phone className="w-5 h-5 text-gray-500 flex-shrink-0" />
            <Link
              href={`tel:${phone}`}
              className="text-sm text-[#1E4841] hover:underline"
            >
              {phone}
            </Link>
          </div>

          <div className="flex items-center gap-3">
            <Mail className="w-5 h-5 text-gray-500 flex-shrink-0" />
            <Link
              href={`mailto:${email}`}
              className="text-sm text-[#1E4841] hover:underline"
            >
              {email}
            </Link>
          </div>

          <div className="flex items-center gap-3">
            <Clock className="w-5 h-5 text-gray-500 flex-shrink-0" />
            <p className="text-sm text-gray-600">
              {hours}
            </p>
          </div>
        </div>
        
        <Button
          variant="ghost"
          className="mt-4 flex items-center gap-2 hover:gap-4 hover:bg-transparent transition-all duration-300"
          asChild
        >
          <Link
            href={getGoogleMapsDirectionsUrl(address)}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-2 hover:gap-4 transition-all duration-300"
          >
            Get Directions <ArrowRight />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}
"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Bell, AlertTriangle, Info, CheckCircle, Clock } from "lucide-react";
import { useNotificationContext } from "@/contexts/NotificationContext";
import Link from "next/link";

const getNotificationIcon = (type: string) => {
    switch (type) {
        case 'warning':
            return AlertTriangle;
        case 'urgent':
            return AlertTriangle;
        case 'info':
            return Info;
        case 'success':
            return CheckCircle;
        default:
            return Bell;
    }
};

const getNotificationColor = (type: string) => {
    switch (type) {
        case 'warning':
            return 'text-yellow-600 bg-yellow-50';
        case 'urgent':
            return 'text-red-600 bg-red-50';
        case 'info':
            return 'text-blue-600 bg-blue-50';
        case 'success':
            return 'text-green-600 bg-green-50';
        default:
            return 'text-gray-600 bg-gray-50';
    }
};

export default function AdminNotifications() {
    const { notifications, unreadCount, isLoading } = useNotificationContext();
    
    // Show only first 5 notifications for dashboard widget
    const displayNotifications = notifications.slice(0, 5);

    if (isLoading) {
        return (
            <Card className="bg-white border-0 shadow-sm">
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <CardTitle className="text-lg font-semibold text-gray-900">
                                Notifications
                            </CardTitle>
                            <Skeleton className="w-6 h-4" />
                        </div>
                        <Button variant="outline" size="sm" className="hover:bg-[#BBF49C] hover:border-[#BBF49C]">
                            View All
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="space-y-3">
                        {[...Array(3)].map((_, index) => (
                            <div key={index} className="flex items-start gap-3 p-3 rounded-lg border bg-gray-50 border-gray-200">
                                <Skeleton className="w-7 h-7 rounded-lg" />
                                <div className="flex-1 min-w-0">
                                    <Skeleton className="h-4 w-3/4 mb-2" />
                                    <Skeleton className="h-3 w-1/2" />
                                </div>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="bg-white border-0 shadow-sm">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <CardTitle className="text-lg font-semibold text-gray-900">
                            Notifications
                        </CardTitle>
                        {unreadCount > 0 && (
                            <Badge variant="secondary" className="bg-red-100 text-red-800">
                                {unreadCount}
                            </Badge>
                        )}
                    </div>
                    <Link href="/dashboard/admin/notifications">
                        <Button variant="outline" size="sm" className="hover:bg-[#BBF49C] hover:border-[#BBF49C]">
                            View All
                        </Button>
                    </Link>
                </div>
            </CardHeader>
            <CardContent>
                {notifications.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <Bell className="w-8 h-8 text-gray-400" />
                        </div>
                        <h3 className="text-sm font-medium text-gray-900 mb-1">No notifications</h3>
                        <p className="text-xs text-gray-500">You&apos;re all caught up!</p>
                    </div>
                ) : (
                    <div className="space-y-3">
                        {displayNotifications.map((notification) => {
                            const Icon = getNotificationIcon(notification.type);
                            const colorClasses = getNotificationColor(notification.type);
                            
                            return (
                                <div 
                                    key={notification._id} 
                                    className={`flex items-start gap-3 p-3 rounded-lg border ${
                                        notification.status === 'unread' ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'
                                    }`}
                                >
                                    <div className={`p-1.5 rounded-lg ${colorClasses}`}>
                                        <Icon className="h-4 w-4" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center gap-2">
                                            <h4 className="text-sm font-medium text-gray-900">
                                                {notification.title}
                                            </h4>
                                            {notification.status === 'unread' && (
                                                <div className="w-2 h-2 bg-blue-600 rounded-full" />
                                            )}
                                        </div>
                                        <p className="text-sm text-gray-600 mt-1">
                                            {notification.message}
                                        </p>
                                        <div className="flex items-center gap-1 mt-2 text-xs text-gray-500">
                                            <Clock className="h-3 w-3" />
                                            {notification.createdAt ? new Date(notification.createdAt).toLocaleString() : 'N/A'}
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
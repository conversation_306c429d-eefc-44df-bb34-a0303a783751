import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import connectDB from '@/lib/db/mongodb';
import { Report, Company, User } from '@/lib/db/models';
import { SecurityAuditLogger } from '@/lib/security/audit-logger';
import { IPAnonymizer } from '@/lib/security/ipAnonymization';
import jwt from 'jsonwebtoken';

export const runtime = 'nodejs';

// Validation schema for anonymous report submission
const anonymousReportSchema = z.object({
  // Step 1 - Incident Info
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  category: z.enum([
    'Financial Misconduct',
    'Accounting Fraud / Financial Manipulation',
    'Corruption',
    'Harassment',
    'Safety Violation',
    'Environmental',
    'Discrimination',
    'Other'
  ]),
  dateOfOccurrence: z.string().optional(),
  location: z.string().max(200, 'Location too long').optional(),
  description: z.string().min(1, 'Description is required').max(2000, 'Description too long'),
  
  // Step 2 - Evidence
  evidenceDescription: z.string().max(1000, 'Evidence description too long').optional(),
  
  // Step 3 - Consent
  privacyConsent: z.boolean().refine(val => val === true, 'Privacy consent is required'),
  
  // Session info
  sessionId: z.string().min(1, 'Session ID is required'),
  companyName: z.string().min(1, 'Company name is required')
});

// Generate a unique report token for anonymous tracking using incremental format
async function generateReportToken(): Promise<string> {
  const reportCount = await Report.countDocuments();
  const year = new Date().getFullYear();
  const paddedCount = (reportCount + 1).toString().padStart(3, '0');
  return `RPT-${year}-${paddedCount}`;
}

// Create anonymous user for the report
async function createAnonymousUser(companyId: string, sessionId: string) {
  const anonymousUserId = `anon_${sessionId}_${Date.now()}`;
  
  const anonymousUser = await User.create({
    email: `${anonymousUserId}@anonymous.local`,
    firstName: 'Anonymous',
    lastName: 'User',
    role: 'whistleblower',
    companyId: companyId,
    isActive: true,
    isAnonymous: true,
    anonymousSessionId: sessionId,
    // Don't store any personally identifiable information
    metadata: {
      createdForReport: true,
      sessionId: sessionId
    }
  });

  return anonymousUser;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = anonymousReportSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const reportData = validationResult.data;

    await connectDB();

    // Verify the anonymous session
    const authHeader = request.headers.get('authorization');
    let _sessionValid = false;
    let _companyId: string | null = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const decoded = jwt.verify(
          token, 
          process.env.JWT_SECRET || 'your-secret-key-change-in-production'
        ) as any;
        
        if (decoded.type === 'anonymous' && decoded.sessionId === reportData.sessionId) {
          _sessionValid = true;
          _companyId = decoded.companyId;
        }
      } catch {
        // Invalid token, but we can still try to find company by name
      }
    }

    // Find or create company
    let company = await Company.findOne({ 
      name: { $regex: new RegExp(`^${reportData.companyName.trim()}$`, 'i') }
    });

    if (!company) {
      company = await Company.create({
        name: reportData.companyName.trim(),
        contactEmail: `anonymous@${reportData.companyName.toLowerCase().replace(/\s+/g, '')}.com`,
        website: `${reportData.companyName.toLowerCase().replace(/\s+/g, '')}.com`,
        isActive: true,
        subscriptionStatus: 'Active'
      });
    }

    // Create anonymous user for this report
    const anonymousUser = await createAnonymousUser(company._id.toString(), reportData.sessionId);

    // Generate unique identifiers
    const reportCount = await Report.countDocuments();
    const reportToken = await generateReportToken();
    const reportId = `RPT-${new Date().getFullYear()}-${(reportCount + 1).toString().padStart(3, '0')}`;

    // Anonymize IP address for privacy
    const clientIP = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const anonymizedIP = IPAnonymizer.anonymizeIP(clientIP);

    // Create the anonymous report
    const newReport = await Report.create({
      reportId,
      referenceNumber: reportToken, // Use report token as reference number for tracking
      userId: anonymousUser._id,
      companyId: company._id,
      
      // Step 1 data
      title: reportData.title,
      description: reportData.description,
      category: reportData.category,
      dateOfOccurrence: reportData.dateOfOccurrence ? new Date(reportData.dateOfOccurrence) : undefined,
      location: reportData.location,
      isAnonymous: true,
      
      // Step 2 data
      hasEvidence: !!reportData.evidenceDescription,
      evidenceDescription: reportData.evidenceDescription,
      evidenceFiles: [], // Files will be uploaded separately
      
      // System fields
      status: 'New',
      priority: 'Medium',
      urgencyLevel: 'Medium',
      isDraft: false,
      dateSubmitted: new Date(),
      lastUpdated: new Date(),
      
      // Anonymous tracking
      anonymousToken: reportToken,
      
      // Metadata (anonymized)
      metadata: {
        ipAddress: anonymizedIP,
        userAgent: request.headers.get('user-agent') || 'unknown',
        submissionMethod: 'web',
        isAnonymous: true,
        sessionId: reportData.sessionId
      }
    });

    // Log the anonymous report submission (without PII)
    console.log('ANONYMOUS REPORT SUBMITTED:', {
      reportId: newReport.reportId,
      reportToken: reportToken,
      category: reportData.category,
      companyName: reportData.companyName,
      hasEvidence: !!reportData.evidenceDescription,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      data: {
        reportId: newReport.reportId,
        reportToken: reportToken,
        submissionId: newReport._id.toString(),
        message: 'Anonymous report submitted successfully'
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Anonymous report submission error:', error);
    
    // Log the error (without sensitive data)
    await SecurityAuditLogger.logSuspiciousActivity(
      request,
      'Anonymous report submission failed',
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    );

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to submit anonymous report. Please try again.' 
      },
      { status: 500 }
    );
  }
}

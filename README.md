# 🔒 Whistleblower Platform

A secure, enterprise-grade whistleblower reporting platform built with Next.js, featuring advanced security, real-time messaging, and comprehensive audit logging.

## 🚀 Quick Start

### Development
```bash
npm run dev          # Start development server with WebSocket support
npm run dev:next     # Start Next.js only (port 3002)
```

### Production
```bash
npm run build        # Build for production
npm run start        # Start production server
```

### Database & Setup
```bash
npm run db:seed      # Seed database with sample data
npm run dev-users    # Manage secure test users (development only)
```

### Security & Testing
```bash
npm run security:check    # Validate security configuration
npm run security:test     # Run comprehensive security tests
npm run security:audit    # Check for vulnerable dependencies
npm run lint             # Run ESLint
```

## 🔧 Environment Setup

1. Copy the production environment template:
```bash
cp .env.production.template .env.local
```

2. Generate secure secrets:
```bash
# JWT Secret
node -e "console.log('JWT_SECRET=' + require('crypto').randomBytes(64).toString('hex'))"

# NextAuth Secret
node -e "console.log('NEXTAUTH_SECRET=' + require('crypto').randomBytes(64).toString('hex'))"

# Encryption Key
node -e "console.log('MESSAGE_ENCRYPTION_KEY=' + require('crypto').randomBytes(32).toString('hex'))"
```

3. Configure your environment variables in `.env.local`

### 🗺️ Google Maps Integration

For office location maps on the contact page:

1. Get a Google Maps API key from [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the "Maps Embed API" for your project
3. Add the API key to your `.env.local`:
```bash
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key_here
```

**Note**: Without the API key, office location cards will show a placeholder instead of real maps.

## 📚 Documentation

- **Security Quick Start**: `docs/SECURITY-QUICKSTART.md`
- **Production Template**: `.env.production.template`

## 🏗️ Architecture

### 🔐 **Enterprise Security**
- Advanced session management with JWT blacklisting
- Comprehensive file upload security with malware detection
- Real-time security monitoring and audit logging
- Rate limiting and CSRF protection
- HTTPS enforcement and security headers

### 💬 **Real-time Communication**
- WebSocket-based messaging system
- Live typing indicators and read receipts
- Secure conversation management
- File attachment support

### 📊 **Comprehensive Reporting**
- Anonymous and authenticated reporting
- Evidence file uploads with security scanning
- Report status tracking and management
- Advanced search and filtering

### 🛡️ **Data Protection**
- End-to-end message encryption
- GDPR compliance features
- Data retention policies
- Secure user authentication

### 🎯 **User Management**
- Role-based access control (Admin, Investigator, Whistleblower)
- Company isolation and multi-tenancy
- User session management
- Security dashboard

## 🔒 Security Features

- **Zero hardcoded secrets** - All secrets managed via environment variables
- **Advanced session management** - JWT with blacklisting and multi-session tracking
- **File security scanning** - Malware detection and content validation
- **Comprehensive audit logging** - All security events tracked
- **Production-grade headers** - CSP, HSTS, and security headers
- **Input validation** - XSS and injection prevention
- **Rate limiting** - Brute force protection

## 📋 Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server with WebSocket |
| `npm run build` | Build for production |
| `npm run start` | Start production server |
| `npm run db:seed` | Seed database with sample data |
| `npm run dev-users` | Manage secure test users |
| `npm run security:check` | Validate security configuration |
| `npm run security:test` | Run security test suite |
| `npm run security:audit` | Check for vulnerabilities |

## 🚀 Deployment

1. **Environment Setup**: Configure all required environment variables
2. **Security Check**: Run `npm run security:check` to validate configuration
3. **Build**: Run `npm run build` to create production build
4. **Deploy**: Deploy to your preferred platform (Vercel, AWS, etc.)

For detailed deployment instructions, see the production environment template.

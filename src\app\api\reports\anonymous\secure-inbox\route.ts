import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import connectDB from '@/lib/db/mongodb';
import { Report, Message } from '@/lib/db/models';
import { SecurityAuditLogger } from '@/lib/security/audit-logger';

export const runtime = 'nodejs';

// Validation schema for secure inbox access
const secureInboxSchema = z.object({
  reportToken: z.string().min(1, 'Report token is required')
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = secureInboxSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { reportToken } = validationResult.data;

    await connectDB();

    // Find the report by token
    const report = await Report.findOne({ 
      referenceNumber: reportToken,
      isAnonymous: true 
    }).populate('userId', 'firstName lastName email');

    if (!report) {
      // Log failed access attempt
      await SecurityAuditLogger.logSuspiciousActivity(
        request,
        'Invalid report token access attempt',
        {
          reportToken: reportToken,
          timestamp: new Date().toISOString()
        }
      );

      return NextResponse.json(
        { success: false, error: 'Invalid report token' },
        { status: 404 }
      );
    }

    // Get messages for this report (if any)
    const messages = await Message.find({
      reportId: report._id
    }).populate('senderId', 'firstName lastName role')
      .sort({ createdAt: -1 })
      .limit(50);

    // Log successful access
    console.log('ANONYMOUS INBOX ACCESSED:', {
      reportId: report.reportId,
      reportToken: reportToken,
      timestamp: new Date().toISOString()
    });

    // Return report details and messages (sanitized for anonymous access)
    return NextResponse.json({
      success: true,
      data: {
        report: {
          reportId: report.reportId,
          title: report.title,
          category: report.category,
          status: report.status,
          priority: report.priority,
          dateSubmitted: report.dateSubmitted,
          lastUpdated: report.lastUpdated,
          description: report.description,
          location: report.location,
          dateOfOccurrence: report.dateOfOccurrence,
          hasEvidence: report.hasEvidence,
          evidenceDescription: report.evidenceDescription,
          evidenceFiles: report.evidenceFiles?.map(file => ({
            originalName: file.originalName,
            fileSize: file.fileSize,
            uploadedAt: file.uploadedAt,
            description: file.description
          })) || []
        },
        messages: messages.map(message => {
          // Handle populated senderId (user object) vs unpopulated (ObjectId)
          const sender = message.senderId && typeof message.senderId === 'object' && 'role' in message.senderId
            ? message.senderId as any
            : null;

          const senderRole = sender?.role || 'system';
          const isFromInvestigator = senderRole === 'admin' || senderRole === 'investigator';

          return {
            id: message._id,
            content: message.content,
            senderRole,
            senderName: isFromInvestigator && sender
              ? `${sender.firstName || ''} ${sender.lastName || ''}`.trim() || 'Staff Member'
              : 'Anonymous',
            createdAt: message.createdAt,
            isFromInvestigator
          };
        }),
        canReply: true // Anonymous users can reply to messages
      }
    }, { status: 200 });

  } catch (error) {
    console.error('Secure inbox access error:', error);
    
    await SecurityAuditLogger.logSuspiciousActivity(
      request,
      'Secure inbox access failed',
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    );

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to access secure inbox. Please try again.' 
      },
      { status: 500 }
    );
  }
}

// GET endpoint for retrieving inbox data with query parameter
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const reportToken = searchParams.get('token');

    if (!reportToken) {
      return NextResponse.json(
        { success: false, error: 'Report token is required' },
        { status: 400 }
      );
    }

    // Use the same logic as POST
    return POST(new NextRequest(request.url, {
      method: 'POST',
      headers: request.headers,
      body: JSON.stringify({ reportToken })
    }));

  } catch (error) {
    console.error('Secure inbox GET error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to access secure inbox' },
      { status: 500 }
    );
  }
}

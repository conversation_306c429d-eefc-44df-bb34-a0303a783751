"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useSocket, MessageWithSender } from '@/hooks/useSocket';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
// import { Separator } from '@/components/ui/separator';
import LexicalEditor, { LexicalEditorRef } from '@/components/ui/lexical-editor';
import { EditorState } from 'lexical';
import {
  Send,
  Paperclip,
  MoreVertical,
  Phone,
  Video,
  Info,
  Search,
  Archive,
  Trash2,
  Edit,
  Reply,
  // Heart,
  ThumbsUp,
  // Smile,
  CheckCheck,
  Check,
  // Clock,
  // AlertCircle,
  X,
  Eye
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { format } from 'date-fns';
import { Conversation } from '@/lib/types';

interface ConversationLocal {
  _id: string;
  reportId: {
    _id: string;
    reportId: string;
    title: string;
  };
  participants: Array<{
    _id: string;
    firstName?: string;
    lastName?: string;
    role: string;
  }>;
  status: 'active' | 'closed' | 'archived';
  lastMessageAt?: Date;
  isEncrypted: boolean;
}

// MessageWithSender is now imported from the hook

interface RealTimeMessagingProps {
  userId: string;
  userRole: 'whistleblower' | 'investigator' | 'admin';
  initialConversations?: ConversationLocal[];
  onConversationSelect?: (conversationId: string) => void;
  activeConversationId?: string;
  onResetAutoLogout?: () => void;
}

const MessageStatus: React.FC<{ 
  message: MessageWithSender; 
  currentUserId: string;
  onlineUsers: Set<string>;
}> = ({ message, currentUserId }) => {
  if (message.senderId._id !== currentUserId) return null;

  const readByOthers = message.readBy?.filter(read => read.userId !== currentUserId) || [];
  const allParticipantsRead = readByOthers.length > 0;

  return (
    <div className="flex items-center gap-1 text-xs text-gray-500 mt-1">
      {allParticipantsRead ? (
        <CheckCheck className="w-3 h-3 text-blue-500" />
      ) : (
        <Check className="w-3 h-3" />
      )}
      <span className="text-xs">
        {format(new Date(message.createdAt!), 'HH:mm')}
      </span>
    </div>
  );
};

const TypingIndicator: React.FC<{ typingUsers: string[] }> = ({ typingUsers }) => {
  if (typingUsers.length === 0) return null;

  return (
    <div className="flex items-center gap-2 p-3 text-sm text-gray-500">
      <div className="flex gap-1">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
      <span>
        {typingUsers.length === 1 
          ? `Someone is typing...`
          : `${typingUsers.length} people are typing...`
        }
      </span>
    </div>
  );
};

const MessageBubble: React.FC<{
  message: MessageWithSender;
  isOwn: boolean;
  onlineUsers: Set<string>;
  currentUserId: string;
  onReply?: (message: MessageWithSender) => void;
  onReact?: (messageId: string, emoji: string) => void;
  onEdit?: (message: MessageWithSender) => void;
  onDelete?: (messageId: string) => void;
}> = ({ 
  message, 
  isOwn, 
  onlineUsers, 
  currentUserId, 
  onReply, 
  onReact, 
  onEdit, 
  onDelete 
}) => {
  const [showActions, setShowActions] = useState(false);

  // const formatMessageTime = (date: Date) => {
  //   if (isToday(date)) {
  //     return format(date, 'HH:mm');
  //   } else if (isYesterday(date)) {
  //     return `Yesterday ${format(date, 'HH:mm')}`;
  //   } else {
  //     return format(date, 'MMM dd, HH:mm');
  //   }
  // };

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || '?';
  };

  return (
    <div 
      className={`flex gap-3 p-3 hover:bg-gray-50 group ${isOwn ? 'flex-row-reverse' : ''}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {!isOwn && (
        <Avatar className="w-8 h-8 flex-shrink-0">
          <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
            {getInitials(message.senderId.firstName, message.senderId.lastName)}
          </AvatarFallback>
        </Avatar>
      )}
      
      <div className={`flex-1 max-w-[70%] ${isOwn ? 'text-right' : ''}`}>
        {!isOwn && (
          <div className="flex items-center gap-2 mb-1">
            <span className="text-sm font-medium text-gray-900">
              {message.senderId.firstName} {message.senderId.lastName}
            </span>
            <Badge variant="outline" className="text-xs">
              {message.senderId.role}
            </Badge>
            {onlineUsers.has(message.senderId._id) && (
              <div className="w-2 h-2 bg-green-500 rounded-full" />
            )}
          </div>
        )}
        
        <div className={`relative ${isOwn ? 'ml-auto' : ''}`}>
          <div 
            className={`rounded-lg p-3 ${
              isOwn 
                ? 'bg-blue-500 text-white' 
                : 'bg-white border border-gray-200'
            }`}
          >
            {message.replyTo && (
              <div className="mb-2 p-2 bg-gray-100 rounded border-l-2 border-gray-300 text-sm">
                <span className="text-gray-600">Replying to a message</span>
              </div>
            )}
            
            {message.isDeleted ? (
              <span className="italic text-gray-500">This message has been deleted</span>
            ) : (
              <div>
                {message.htmlContent && message.htmlContent.trim() !== '' ? (
                  <div 
                    dangerouslySetInnerHTML={{ __html: message.htmlContent }}
                    className="prose prose-sm max-w-none [&>*]:my-1 [&>p]:my-1 [&>ul]:my-1 [&>ol]:my-1"
                  />
                ) : (
                  <p className="whitespace-pre-wrap">{message.content}</p>
                )}
                
                {message.attachments && message.attachments.length > 0 && (
                  <div className="mt-2 space-y-2">
                    {message.attachments.map((attachment, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 bg-gray-100 rounded">
                        <Paperclip className="w-4 h-4" />
                        <div className="flex-1">
                          <a 
                            href={attachment.fileUrl}
                            download={attachment.fileName}
                            className="text-sm text-blue-600 hover:text-blue-800 underline"
                          >
                            {attachment.fileName}
                          </a>
                          <span className="text-xs text-gray-500 ml-2">
                            ({(attachment.fileSize / 1024).toFixed(1)} KB)
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-1"
                          onClick={() => window.open(attachment.fileUrl, '_blank')}
                        >
                          <Eye className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
            
            {message.editedAt && (
              <span className="text-xs text-gray-400 mt-1 block">
                (edited)
              </span>
            )}
          </div>
          
          {/* Reactions */}
          {message.reactions && message.reactions.length > 0 && (
            <div className="flex gap-1 mt-1">
              {message.reactions.map((reaction, index) => (
                <button
                  key={index}
                  className="flex items-center gap-1 px-2 py-1 bg-gray-100 rounded-full text-xs hover:bg-gray-200"
                  onClick={() => onReact?.(message._id!, reaction.emoji)}
                >
                  <span>{reaction.emoji}</span>
                  <span>1</span>
                </button>
              ))}
            </div>
          )}
          
          {/* Message Actions */}
          {showActions && (
            <div className={`absolute top-0 ${isOwn ? 'left-0 -translate-x-full' : 'right-0 translate-x-full'} flex gap-1 bg-white border rounded-lg shadow-lg p-1`}>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-8 h-8 p-0"
                      onClick={() => onReact?.(message._id!, '👍')}
                    >
                      <ThumbsUp className="w-3 h-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>React</TooltipContent>
                </Tooltip>
                
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-8 h-8 p-0"
                      onClick={() => onReply?.(message)}
                    >
                      <Reply className="w-3 h-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Reply</TooltipContent>
                </Tooltip>
                
                {isOwn && !message.isDeleted && (
                  <>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-8 h-8 p-0"
                          onClick={() => onEdit?.(message)}
                        >
                          <Edit className="w-3 h-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Edit</TooltipContent>
                    </Tooltip>
                    
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-8 h-8 p-0 text-red-500 hover:text-red-700"
                          onClick={() => onDelete?.(message._id!)}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Delete</TooltipContent>
                    </Tooltip>
                  </>
                )}
              </TooltipProvider>
            </div>
          )}
        </div>
        
        <MessageStatus 
          message={message} 
          currentUserId={currentUserId} 
          onlineUsers={onlineUsers}
        />
      </div>
    </div>
  );
};

export const RealTimeMessaging: React.FC<RealTimeMessagingProps> = ({
  userId,
  userRole,
  initialConversations = [],
  onConversationSelect,
  activeConversationId: propActiveConversationId,
  onResetAutoLogout
}) => {
  const [conversations] = useState<ConversationLocal[]>(initialConversations);
  const [activeConversationId, setActiveConversationId] = useState<string>(propActiveConversationId || '');
  const [messages, setMessages] = useState<MessageWithSender[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [replyingTo, setReplyingTo] = useState<MessageWithSender | null>(null);
  // const [editingMessage] = useState<MessageWithSender | null>(null);
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const editorRef = useRef<LexicalEditorRef>(null);

  // Handler functions for socket events
  const handleNewMessage = useCallback((message: MessageWithSender) => {
    console.log('New message received:', message);
    setMessages(prev => [...prev, message]);
    onResetAutoLogout?.();
  }, [onResetAutoLogout]);

  const handleUserTyping = useCallback((data: { conversationId: string; userId: string; isTyping: boolean }) => {
    console.log('User typing event:', data);
    // This will be handled by the socket hook's internal state
    onResetAutoLogout?.();
  }, [onResetAutoLogout]);

  const handleUserStatusChange = useCallback((status: { userId: string; isOnline: boolean; timestamp: Date }) => {
    console.log('User status change:', status);
    // This will be handled by the socket hook's internal state
    onResetAutoLogout?.();
  }, [onResetAutoLogout]);

  const handleConversationCreated = useCallback((conversation: Conversation) => {
    console.log('Conversation created:', conversation);
    // Handle new conversation creation if needed
    onResetAutoLogout?.();
  }, [onResetAutoLogout]);

  // Socket connection
  const {
    isConnected,
    onlineUsers,
    typingUsers,
    sendMessage,
    joinConversation,
    // leaveConversation,
    startTyping,
    stopTyping,
    // markMessageAsRead,
    // createConversation,
    refreshMessages
  } = useSocket(
    userId,
    userRole,
    handleNewMessage,
    handleUserTyping,
    handleUserStatusChange,
    handleConversationCreated
  );

  // Scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // Sync activeConversationId with prop changes
  useEffect(() => {
    if (propActiveConversationId && propActiveConversationId !== activeConversationId) {
      setActiveConversationId(propActiveConversationId);
    }
  }, [propActiveConversationId, activeConversationId]);



  // Load conversations
  // const loadConversations = useCallback(async () => {
  //   try {
  //     const response = await fetch(`/api/conversations?userId=${userId}`);
  //     const data = await response.json();
  //     if (data.success) {
  //       setConversations(data.data);
  //     }
  //   } catch (error) {
  //     console.error('Error loading conversations:', error);
  //   }
  // }, [userId]);

  // Load messages for a conversation
  const loadMessages = useCallback(async (conversationId: string) => {
    try {
      setIsLoading(true);
      const messages = await refreshMessages(conversationId);
      setMessages(messages);
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setIsLoading(false);
    }
  }, [refreshMessages]);

  // Load messages when conversation changes
  useEffect(() => {
    let mounted = true;
    
    if (activeConversationId) {
      // Join conversation first
      joinConversation(activeConversationId);
      
      // Then load messages
      (async () => {
        try {
          await loadMessages(activeConversationId);
          if (mounted) {
            onConversationSelect?.(activeConversationId);
          }
        } catch (error) {
          console.error('Error loading messages:', error);
        }
      })();
    }

    return () => {
      mounted = false;
    };
  }, [activeConversationId, joinConversation, onConversationSelect, loadMessages]);

  // Send message
  const handleSendMessage = useCallback(async () => {
    if (!newMessage.trim() || !activeConversationId) return;

    const messageContent = newMessage.trim();
    const htmlContent = editorRef.current?.getHTML();

    // Upload files first if any
    let attachments: Array<{
      fileName: string;
      fileUrl: string;
      fileSize: number;
      mimeType: string;
    }> = [];

    if (attachedFiles.length > 0) {
      const uploadPromises = attachedFiles.map(async (file) => {
        const formData = new FormData();
        formData.append('file', file);

        try {
          const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
          });

          const data = await response.json();
          if (data.success) {
            return {
              fileName: data.data.fileName,
              fileUrl: data.data.fileUrl,
              fileSize: data.data.fileSize,
              mimeType: data.data.mimeType
            };
          }
          return null;
        } catch (error) {
          console.error('Upload error:', error);
          return null;
        }
      });

      const uploadResults = await Promise.all(uploadPromises);
      attachments = uploadResults.filter(result => result !== null) as typeof attachments;
    }

    // Send message via the messaging hook
    await sendMessage({
      conversationId: activeConversationId,
      content: messageContent,
      htmlContent,
      messageType: attachments.length > 0 ? 'file' : 'text',
      attachments: attachments.length > 0 ? attachments : undefined
    });

    // Clear input
    setNewMessage('');
    setAttachedFiles([]);
    setReplyingTo(null);
    editorRef.current?.clear();
    stopTyping(activeConversationId);
    
    // Reset auto-logout timer on message send
    onResetAutoLogout?.();
  }, [newMessage, activeConversationId, sendMessage, attachedFiles, stopTyping, onResetAutoLogout]);

  // Handle editor changes
  const handleEditorChange = useCallback((editorState: EditorState) => {
    editorState.read(() => {
      const root = editorState._nodeMap.get('root');
      if (root) {
        const textContent = root.getTextContent();
        setNewMessage(textContent);
        
        // Start typing indicator
        if (textContent.trim() && activeConversationId) {
          startTyping(activeConversationId);
        } else if (activeConversationId) {
          stopTyping(activeConversationId);
        }
        
        // Reset auto-logout timer on typing
        onResetAutoLogout?.();
      }
    });
  }, [activeConversationId, startTyping, stopTyping, onResetAutoLogout]);

  // Handle message reactions
  const handleReact = useCallback(async (messageId: string, emoji: string) => {
    try {
      await fetch(`/api/messages?messageId=${messageId}&action=add_reaction`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, emoji })
      });
      
      // Update local state
      setMessages(prev => prev.map(msg => 
        msg._id === messageId 
          ? {
              ...msg,
              reactions: [
                ...(msg.reactions?.filter(r => r.userId !== userId) || []),
                { userId, emoji, createdAt: new Date() }
              ]
            }
          : msg
      ));
      
      // Reset auto-logout timer on reaction
      onResetAutoLogout?.();
    } catch (error) {
      console.error('Error adding reaction:', error);
    }
  }, [userId, onResetAutoLogout]);

  // Handle message deletion
  const handleDelete = useCallback(async (messageId: string) => {
    if (!confirm('Are you sure you want to delete this message?')) return;

    try {
      await fetch(`/api/messages?messageId=${messageId}&userId=${userId}`, {
        method: 'DELETE'
      });
      
      // Update local state
      setMessages(prev => prev.map(msg => 
        msg._id === messageId 
          ? { ...msg, isDeleted: true, content: 'This message has been deleted' }
          : msg
      ));
      
      // Reset auto-logout timer on delete
      onResetAutoLogout?.();
    } catch (error) {
      console.error('Error deleting message:', error);
    }
  }, [userId, onResetAutoLogout]);

  // Handle message editing
  const handleEditMessage = useCallback(async (message: MessageWithSender) => {
    if (!message || message.senderId._id !== userId) return;

    const newContent = prompt('Edit your message:', message.content);
    if (!newContent || newContent === message.content) return;

    try {
      const response = await fetch(`/api/messages?messageId=${message._id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: newContent,
          userId,
          isEdited: true
        })
      });

      if (response.ok) {
        // Update the message in the local state
        setMessages(prev => prev.map(m =>
          m._id === message._id
            ? { ...m, content: newContent, isEdited: true, editedAt: new Date() }
            : m
        ));
        onResetAutoLogout?.();
      }
    } catch (error) {
      console.error('Error editing message:', error);
    }
  }, [userId, onResetAutoLogout]);

  // Handle file attachment
  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Upload files and get URLs
    const uploadPromises = Array.from(files).map(async (file) => {
      const formData = new FormData();
      formData.append('file', file);

      try {
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData
        });

        const data = await response.json();
        if (data.success) {
          return {
            fileName: data.data.fileName,
            fileUrl: data.data.fileUrl,
            fileSize: data.data.fileSize,
            mimeType: data.data.mimeType,
            originalFile: file
          };
        } else {
          console.error('Upload failed:', data.error);
          return null;
        }
      } catch (error) {
        console.error('Upload error:', error);
        return null;
      }
    });

    const uploadedFiles = await Promise.all(uploadPromises);
    const validFiles = uploadedFiles.filter(file => file !== null);
    
    if (validFiles.length > 0) {
      setAttachedFiles(prev => [...prev, ...validFiles.map(f => f!.originalFile)]);
    }

    // Clear the input
    event.target.value = '';
    
    // Reset auto-logout timer on file selection
    onResetAutoLogout?.();
  }, [onResetAutoLogout]);

  // Filter conversations based on search
  const filteredConversations = conversations.filter(conv =>
    conv.reportId.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.reportId.reportId.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.participants.some(p => 
      `${p.firstName} ${p.lastName}`.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const activeConversation = conversations.find(conv => conv._id === activeConversationId);
  const conversationTypingUsers = activeConversationId 
    ? Array.from(typingUsers.get(activeConversationId) || [])
        .filter(userId => userId !== userId)
    : [];

  return (
    <div className="flex h-full bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Conversations Sidebar */}
      <div className="w-1/3 border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold">Messages</h2>
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-xs text-gray-500">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Conversations List */}
        <ScrollArea className="flex-1">
          {filteredConversations.map((conversation) => {
            const otherParticipants = conversation.participants.filter(p => p._id !== userId);
            const isActive = conversation._id === activeConversationId;
            
            return (
              <div
                key={conversation._id}
                className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${
                  isActive ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                }`}
                onClick={() => {
                  setActiveConversationId(conversation._id);
                  onResetAutoLogout?.();
                }}
              >
                <div className="flex items-start gap-3">
                  <div className="relative">
                    <Avatar className="w-10 h-10">
                      <AvatarFallback className="bg-gray-200">
                        {otherParticipants[0]?.firstName?.[0] || '?'}
                        {otherParticipants[0]?.lastName?.[0] || ''}
                      </AvatarFallback>
                    </Avatar>
                    {otherParticipants.some(p => onlineUsers.has(p._id)) && (
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-sm truncate">
                        {otherParticipants.map(p => `${p.firstName} ${p.lastName}`).join(', ')}
                      </h3>
                      {conversation.lastMessageAt && (
                        <span className="text-xs text-gray-500">
                          {format(new Date(conversation.lastMessageAt), 'HH:mm')}
                        </span>
                      )}
                    </div>
                    
                    <p className="text-xs text-gray-600 mb-1">
                      {conversation.reportId.reportId} - {conversation.reportId.title}
                    </p>
                    
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {conversation.status}
                      </Badge>
                      {conversation.isEncrypted && (
                        <div className="w-3 h-3 text-green-600">🔒</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </ScrollArea>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {activeConversation ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="w-8 h-8">
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      {activeConversation.participants
                        .filter(p => p._id !== userId)[0]?.firstName?.[0] || '?'}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div>
                    <h3 className="font-medium">
                      {activeConversation.participants
                        .filter(p => p._id !== userId)
                        .map(p => `${p.firstName} ${p.lastName}`)
                        .join(', ')}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {activeConversation.reportId.reportId} - {activeConversation.reportId.title}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm">
                    <Phone className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Video className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Info className="w-4 h-4" />
                  </Button>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem>
                        <Archive className="w-4 h-4 mr-2" />
                        Archive Conversation
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete Conversation
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>

            {/* Messages */}
            <ScrollArea className="flex-1 p-4">
              {isLoading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map((message) => (
                    <MessageBubble
                      key={message._id}
                      message={message}
                      isOwn={message.senderId._id === userId}
                      onlineUsers={onlineUsers}
                      currentUserId={userId}
                      onReply={setReplyingTo}
                      onReact={handleReact}
                      onEdit={handleEditMessage}
                      onDelete={handleDelete}
                    />
                  ))}
                  
                  <TypingIndicator typingUsers={conversationTypingUsers} />
                  <div ref={messagesEndRef} />
                </div>
              )}
            </ScrollArea>

            {/* Reply Banner */}
            {replyingTo && (
              <div className="px-4 py-2 bg-blue-50 border-t border-blue-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Reply className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-600">
                      Replying to {replyingTo.senderId.firstName}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setReplyingTo(null)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
                <p className="text-sm text-gray-600 mt-1 truncate">
                  {replyingTo.content}
                </p>
              </div>
            )}

            {/* File Attachments */}
            {attachedFiles.length > 0 && (
              <div className="px-4 py-2 bg-gray-50 border-t">
                <div className="flex flex-wrap gap-2">
                  {attachedFiles.map((file, index) => (
                    <div key={index} className="flex items-center gap-2 bg-white px-3 py-2 rounded-lg border">
                      <Paperclip className="w-4 h-4" />
                      <span className="text-sm">{file.name}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setAttachedFiles(prev => prev.filter((_, i) => i !== index))}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-end gap-3">
                <div className="flex-1">
                  <LexicalEditor
                    ref={editorRef}
                    placeholder="Type your message..."
                    onChange={handleEditorChange}
                    onKeyDown={(event: KeyboardEvent) => {
                      if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                        event.preventDefault();
                        handleSendMessage();
                        return true;
                      }
                      return false;
                    }}
                    className="min-h-[60px] max-h-[120px] border rounded-lg p-3 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div className="flex items-center gap-2">
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    className="hidden"
                    onChange={handleFileSelect}
                  />
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Paperclip className="w-4 h-4" />
                  </Button>
                  
                  <Button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim() || isLoading}
                    className="px-4"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                <span>Press Ctrl+Enter to send</span>
                <span>🔒 End-to-end encrypted</span>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Send className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-medium mb-2">Select a conversation</h3>
              <p>Choose a conversation from the sidebar to start messaging</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
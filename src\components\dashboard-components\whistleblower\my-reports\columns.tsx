"use client";

import { ColumnDef, Table, Row, Column } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Eye,
  PenLine,
  MessageSquare,
  MoreVertical,
  Flag,
  ArrowUpDown,
  Printer,
  Download,
  Share2,
  Trash2
} from "lucide-react";
import { ReportData } from "@/lib/types";
import { useRouter } from "next/navigation";

export const useColumns = () => {
  const router = useRouter();
  
  return [
  {
    id: "select",
    header: ({ table }: { table: Table<ReportData> }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() 
            ? true 
            : table.getIsSomePageRowsSelected() 
            ? "indeterminate" 
            : false
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="data-[state=checked]:bg-[#1E4841] bg-white data-[state=checked]:border-[#E5E6E6]"
      />
    ),
    cell: ({ row }: { row: Row<ReportData> }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="data-[state=checked]:bg-[#1E4841] data-[state=checked]:border-[#1E4841]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: "Report ID",
    cell: ({ row }: { row: Row<ReportData> }) => (
      <button
        className="font-mono font-medium text-xs sm:text-sm text-[#242E2C] hover:text-[#1E4841] hover:underline transition-colors"
        onClick={() => router.push(`/dashboard/reports/${row.getValue("id")}`)}
      >
        {row.getValue("id")}
      </button>
    ),
  },
  {
    accessorKey: "title",
    header: ({ column }: { column: Column<ReportData> }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="h-auto p-0 font-semibold text-[#1E4841]"
      >
        Title
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }: { row: Row<ReportData> }) => {
      const getPriorityFlag = (priority: string) => priority === "Critical" || priority === "High";
      const getPriorityColor = (priority: string) => {
        switch (priority.toLowerCase()) {
          case 'critical': return '#EF4444'; // Red
          case 'high': return '#F97316'; // Orange
          case 'medium': return '#EAB308'; // Yellow
          case 'low': return '#22C55E'; // Green
          default: return '#6B7280'; // Gray
        }
      };

      return (
        <div className="flex items-start justify-between gap-2 sm:gap-4">
          <button
            className="font-normal text-[#1E4841] leading-relaxed min-w-0 flex-1 text-left hover:underline hover:text-[#2A5D54] transition-colors text-sm sm:text-base"
            onClick={() => router.push(`/dashboard/reports/${row.getValue("id")}`)}
          >
            {row.getValue("title")}
          </button>
          {getPriorityFlag(row.original.priority) && (
            <Flag
              className="w-3 h-3 flex-shrink-0 mt-0.5"
              style={{
                color: getPriorityColor(row.original.priority),
                fill: getPriorityColor(row.original.priority)
              }}
            />
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "category",
    header: "Category",
    cell: ({ row }: { row: Row<ReportData> }) => (
      <span className="text-xs sm:text-sm text-[#1E4841] font-normal">
        {row.getValue("category")}
      </span>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }: { row: Row<ReportData> }) => (
      <Badge
        className={`${row.original.statusColor} text-xs font-medium px-2 py-1`}
        variant="secondary"
      >
        {row.getValue("status")}
      </Badge>
    ),
  },
  {
    accessorKey: "progressPercentage",
    header: "Progress",
    cell: ({ row }: { row: Row<ReportData> }) => {
      const getProgressColor = (status: string) => {
        switch (status.toLowerCase()) {
          case 'new': return '#2563EB'; // Blue
          case 'under review': return '#F97316'; // Orange
          case 'awaiting response': return '#EAB308'; // Yellow
          case 'resolved': return '#22C55E'; // Green
          case 'closed': return '#6B7280'; // Gray
          default: return '#6B7280'; // Gray
        }
      };

      const progressValue = Number(row.getValue("progressPercentage")) || 0;
      const progressColor = getProgressColor(row.original.status);

      return (
        <div className="flex flex-col gap-1">
          <div
            className="progress-wrapper cursor-help"
            style={{
              '--progress-color': progressColor
            } as React.CSSProperties}
          >
            <Progress
              value={progressValue}
              className="h-2 [&>div]:bg-[var(--progress-color)]"
            />
          </div>
          <span className="text-xs text-gray-600">
            {progressValue}% Complete
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: "dateSubmitted",
    header: ({ column }: { column: Column<ReportData> }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="h-auto p-0 font-semibold text-[#1E4841]"
      >
        Date Submitted
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }: { row: Row<ReportData> }) => (
      <span className="text-xs sm:text-sm text-[#242E2C] font-normal">
        {row.getValue("dateSubmitted")}
      </span>
    ),
  },
  {
    accessorKey: "lastUpdated",
    header: "Last Updated",
    cell: ({ row }: { row: Row<ReportData> }) => (
      <span className="text-xs sm:text-sm text-[#242E2C] font-normal">
        {row.getValue("lastUpdated")}
      </span>
    ),
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }: { row: Row<ReportData> }) => (
      <div className="flex items-center gap-0.5 sm:gap-1">
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 sm:h-8 sm:w-8 p-0 hover:bg-gray-100"
          onClick={() => router.push(`/dashboard/reports/${row.getValue("id")}`)}
        >
          <Eye className="w-3 h-3 sm:w-4 sm:h-4 text-[#6B7280]" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 sm:h-8 sm:w-8 p-0 hover:bg-gray-100"
          onClick={() => router.push(`/dashboard/reports/${row.getValue("id")}/edit`)}
        >
          <PenLine className="w-3 h-3 sm:w-4 sm:h-4 text-[#6B7280]" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 sm:h-8 sm:w-8 p-0 hover:bg-gray-100"
          onClick={() => router.push(`/dashboard/messages?report=${row.getValue("id")}`)}
        >
          <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 text-[#6B7280]" />
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 sm:h-8 sm:w-8 p-0 hover:bg-gray-100"
            >
              <MoreVertical className="w-3 h-3 sm:w-4 sm:h-4 text-[#6B7280]" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem
              onClick={() => console.log("Print", row.getValue("id"))}
              className="flex items-center gap-2 px-3 py-2"
            >
              <Printer className="w-4 h-4" />
              Print Report
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => console.log("Download", row.getValue("id"))}
              className="flex items-center gap-2 px-3 py-2"
            >
              <Download className="w-4 h-4" />
              Download Report
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => console.log("Share", row.getValue("id"))}
              className="flex items-center gap-2 px-3 py-2"
            >
              <Share2 className="w-4 h-4" />
              Share Report
            </DropdownMenuItem>
            <div className="h-px bg-gray-100 my-1"></div>
            <DropdownMenuItem
              onClick={() => console.log("Delete", row.getValue("id"))}
              className="flex items-center gap-2 px-3 py-2 text-red-600 focus:text-red-600 focus:bg-red-50"
            >
              <Trash2 className="w-4 h-4" />
              Delete Report
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  ];
};

export const columns: ColumnDef<ReportData>[] = [];
import { Schema } from 'mongoose';
import { createModel } from '../utils';

const EscalationSchema = new Schema({
  reportId: { type: Schema.Types.ObjectId, ref: 'Report', required: true },
  companyId: { type: Schema.Types.ObjectId, ref: 'Company', required: true },
  caseId: { type: String, required: true }, // e.g., "WB-2025-085"
  subject: { type: String, required: true },
  department: { type: String, required: true },
  priority: { 
    type: String, 
    enum: ['Low', 'Medium', 'High', 'Critical'],
    required: true 
  },
  escalatedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  escalatedTo: { type: Schema.Types.ObjectId, ref: 'User' },
  escalatedDate: { type: Date, default: Date.now },
  slaStatus: { 
    type: String, 
    enum: ['On Track', 'At Risk', 'Breached'],
    default: 'On Track'
  },
  slaDeadline: { type: Date, required: true },
  daysOverdue: { type: Number, default: 0 },
  reason: { type: String, required: true },
  status: { 
    type: String, 
    enum: ['Open', 'In Progress', 'Resolved', 'Closed'],
    default: 'Open'
  },
  resolution: { type: String },
  resolvedBy: { type: Schema.Types.ObjectId, ref: 'User' },
  resolvedDate: { type: Date },
  notes: [{ 
    content: { type: String, required: true },
    addedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    addedAt: { type: Date, default: Date.now }
  }],
  metadata: {
    originalStatus: { type: String },
    escalationLevel: { type: Number, default: 1 },
    autoEscalated: { type: Boolean, default: false }
  }
}, {
  timestamps: true
});

// Indexes for better query performance
EscalationSchema.index({ companyId: 1, status: 1 });
EscalationSchema.index({ reportId: 1 });
EscalationSchema.index({ escalatedDate: -1 });
EscalationSchema.index({ slaDeadline: 1 });

// Virtual for calculating days overdue
EscalationSchema.virtual('calculatedDaysOverdue').get(function() {
  if (this.slaStatus === 'Breached' && this.slaDeadline) {
    const now = new Date();
    const deadline = new Date(this.slaDeadline);
    const diffTime = now.getTime() - deadline.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }
  return 0;
});

// Pre-save middleware to calculate days overdue
EscalationSchema.pre('save', function(next) {
  if (this.slaDeadline) {
    const now = new Date();
    const deadline = new Date(this.slaDeadline);
    
    if (now > deadline) {
      this.slaStatus = 'Breached';
      const diffTime = now.getTime() - deadline.getTime();
      this.daysOverdue = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    } else {
      const diffTime = deadline.getTime() - now.getTime();
      const daysUntilDeadline = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (daysUntilDeadline <= 1) {
        this.slaStatus = 'At Risk';
      } else {
        this.slaStatus = 'On Track';
      }
      this.daysOverdue = 0;
    }
  }
  next();
});

export default createModel('Escalation', EscalationSchema);

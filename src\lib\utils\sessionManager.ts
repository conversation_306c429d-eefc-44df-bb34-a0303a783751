"use client";

export interface SessionData {
  userId: string;
  email: string;
  role: 'admin' | 'investigator' | 'whistleblower';
  name: string;
  companyId?: string;
  firstName?: string;
  lastName?: string;
  loginTime: Date;
  lastActivity: Date;
  sessionId: string;
  rememberMe?: boolean;
  expiresAt?: Date;
}

interface LoginHistory {
  userId: string;
  loginTimes: Date[];
  currentSession?: SessionData;
}

class SessionManager {
  private static instance: SessionManager;
  private sessionKey = 'user_session';
  private historyKey = 'login_history';
  private activityInterval: NodeJS.Timeout | null = null;

  private constructor() {
    // Initialize activity tracking
    this.initializeActivityTracking();
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  /**
   * Create a new session and track login time
   */
  public createSession(user: {
    id: string;
    email: string;
    role: 'admin' | 'investigator' | 'whistleblower';
    name: string;
    companyId?: string;
    firstName?: string;
    lastName?: string;
  }, rememberMe?: boolean): SessionData {
    const now = new Date();
    const sessionId = this.generateSessionId();

    // Set expiration time based on rememberMe
    const expiresAt = new Date();
    if (rememberMe) {
      // Remember me: 30 days
      expiresAt.setDate(expiresAt.getDate() + 30);
    } else {
      // Regular session: 24 hours
      expiresAt.setHours(expiresAt.getHours() + 24);
    }

    const sessionData: SessionData = {
      userId: user.id,
      email: user.email,
      role: user.role,
      name: user.name,
      companyId: user.companyId,
      firstName: user.firstName,
      lastName: user.lastName,
      loginTime: now,
      lastActivity: now,
      sessionId,
      rememberMe,
      expiresAt
    };

    // Store session in localStorage and cookie
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
      // Set session cookie for middleware
      const maxAge = rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60; // seconds
      document.cookie = `user_session=${encodeURIComponent(JSON.stringify(sessionData))}; path=/; max-age=${maxAge}; secure; samesite=strict`;
    }
    
    // Update login history
    this.updateLoginHistory(user.id, now);
    
    // Start activity tracking
    this.startActivityTracking();
    
    return sessionData;
  }

  /**
   * Get current session data
   */
  public getCurrentSession(): SessionData | null {
    try {
      // Check if we're on the client side
      if (typeof window === 'undefined') {
        return null;
      }

      const stored = localStorage.getItem(this.sessionKey);
      if (!stored) return null;

      const session = JSON.parse(stored);
      // Convert date strings back to Date objects
      session.loginTime = new Date(session.loginTime);
      session.lastActivity = new Date(session.lastActivity);

      return session;
    } catch (error) {
      console.error('Error parsing session data:', error);
      this.clearSession();
      return null;
    }
  }

  /**
   * Update last activity time
   */
  public updateActivity(): void {
    if (typeof window === 'undefined') return;

    const session = this.getCurrentSession();
    if (session) {
      session.lastActivity = new Date();
      localStorage.setItem(this.sessionKey, JSON.stringify(session));
    }
  }

  /**
   * Get login history for a user
   */
  public getLoginHistory(userId: string): Date[] {
    try {
      const stored = localStorage.getItem(this.historyKey);
      if (!stored) return [];
      
      const histories: LoginHistory[] = JSON.parse(stored);
      const userHistory = histories.find(h => h.userId === userId);
      
      return userHistory ? userHistory.loginTimes.map(time => new Date(time)) : [];
    } catch (error) {
      console.error('Error parsing login history:', error);
      return [];
    }
  }

  /**
   * Get the previous login time (excluding current session)
   */
  public getPreviousLoginTime(userId: string): Date | null {
    const history = this.getLoginHistory(userId);
    // Return the second most recent login (first is current session)
    return history.length > 1 ? history[history.length - 2] : null;
  }

  /**
   * Get formatted login time string
   */
  public getFormattedLoginTime(date: Date): string {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
    } else if (diffInMinutes < 1440) { // Less than 24 hours
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    } else {
      // Format as readable date
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  }

  /**
   * Clear current session
   */
  public clearSession(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.sessionKey);
      // Clear session cookie
      document.cookie = 'user_session=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; secure; samesite=strict';
    }
    this.stopActivityTracking();
  }

  /**
   * Check if session is still valid (not expired)
   */
  public isSessionValid(): boolean {
    const session = this.getCurrentSession();
    if (!session) return false;

    const now = new Date();

    // Check expiration time if available
    if (session.expiresAt) {
      return now < new Date(session.expiresAt);
    }

    // Fallback to old logic for backward compatibility
    const sessionAge = now.getTime() - session.loginTime.getTime();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    return sessionAge < maxAge;
  }

  /**
   * Get session duration
   */
  public getSessionDuration(): string {
    const session = this.getCurrentSession();
    if (!session) return 'No active session';
    
    const now = new Date();
    const duration = now.getTime() - session.loginTime.getTime();
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  private updateLoginHistory(userId: string, loginTime: Date): void {
    try {
      const stored = localStorage.getItem(this.historyKey);
      const histories: LoginHistory[] = stored ? JSON.parse(stored) : [];
      
      let userHistory = histories.find(h => h.userId === userId);
      if (!userHistory) {
        userHistory = { userId, loginTimes: [] };
        histories.push(userHistory);
      }
      
      // Add new login time
      userHistory.loginTimes.push(loginTime);
      
      // Keep only last 10 login times
      if (userHistory.loginTimes.length > 10) {
        userHistory.loginTimes = userHistory.loginTimes.slice(-10);
      }
      
      localStorage.setItem(this.historyKey, JSON.stringify(histories));
    } catch (error) {
      console.error('Error updating login history:', error);
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeActivityTracking(): void {
    if (typeof window !== 'undefined') {
      // Track user activity
      const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
      
      const activityHandler = () => {
        this.updateActivity();
      };
      
      events.forEach(event => {
        document.addEventListener(event, activityHandler, true);
      });
    }
  }

  private startActivityTracking(): void {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Update activity every 30 seconds
    this.activityInterval = setInterval(() => {
      const session = this.getCurrentSession();
      if (session) {
        // Check if user has been inactive for more than 30 minutes
        const now = new Date();
        const inactiveTime = now.getTime() - session.lastActivity.getTime();
        const maxInactiveTime = 30 * 60 * 1000; // 30 minutes

        if (inactiveTime > maxInactiveTime) {
          console.log('Session expired due to inactivity');
          this.clearSession();
          // Let the ProtectedRoute component handle the redirect
          // Don't redirect here to avoid conflicts
        }
      }
    }, 30000); // Check every 30 seconds
  }

  private stopActivityTracking(): void {
    if (this.activityInterval) {
      clearInterval(this.activityInterval);
      this.activityInterval = null;
    }
  }
}

export const sessionManager = SessionManager.getInstance();
export type { LoginHistory };
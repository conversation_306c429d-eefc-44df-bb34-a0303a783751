/**
 * File Upload Security System
 * Comprehensive file validation and security scanning
 */

import { NextRequest } from 'next/server';
import { SecurityAuditLogger } from './audit-logger';

// File type configurations
export const ALLOWED_FILE_TYPES = {
  images: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  documents: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv'
  ],
  archives: ['application/zip', 'application/x-rar-compressed']
};

export const ALL_ALLOWED_TYPES = [
  ...ALLOWED_FILE_TYPES.images,
  ...ALLOWED_FILE_TYPES.documents,
  ...ALLOWED_FILE_TYPES.archives
];

// File size limits (in bytes)
export const FILE_SIZE_LIMITS = {
  image: 10 * 1024 * 1024,      // 10MB for images
  document: 50 * 1024 * 1024,   // 50MB for documents
  archive: 100 * 1024 * 1024,   // 100MB for archives
  default: 50 * 1024 * 1024     // 50MB default
};

// Dangerous file extensions and signatures
const DANGEROUS_EXTENSIONS = [
  '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
  '.app', '.deb', '.pkg', '.dmg', '.rpm', '.msi', '.run', '.bin',
  '.sh', '.ps1', '.php', '.asp', '.jsp', '.py', '.rb', '.pl'
];

const DANGEROUS_SIGNATURES = [
  { signature: [0x4D, 0x5A], description: 'PE executable' },
  { signature: [0x7F, 0x45, 0x4C, 0x46], description: 'ELF executable' },
  { signature: [0xCA, 0xFE, 0xBA, 0xBE], description: 'Java class file' },
  { signature: [0xFE, 0xED, 0xFA, 0xCE], description: 'Mach-O executable' },
  { signature: [0x3C, 0x3F, 0x70, 0x68, 0x70], description: 'PHP script' }
];

export interface FileValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  fileInfo: {
    name: string;
    size: number;
    type: string;
    extension: string;
  };
}

export interface FileSecurityScanResult {
  safe: boolean;
  threats: string[];
  fileType: string;
  actualMimeType?: string;
}

/**
 * Comprehensive file validation
 */
export async function validateFile(
  file: File,
  options: {
    allowedTypes?: string[];
    maxSize?: number;
    requireSignatureCheck?: boolean;
  } = {}
): Promise<FileValidationResult> {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const allowedTypes = options.allowedTypes || ALL_ALLOWED_TYPES;
  const maxSize = options.maxSize || FILE_SIZE_LIMITS.default;

  const fileInfo = {
    name: file.name,
    size: file.size,
    type: file.type,
    extension: getFileExtension(file.name)
  };

  // Basic validations
  if (!file.name || file.name.trim() === '') {
    errors.push('File name is required');
  }

  if (file.size === 0) {
    errors.push('File is empty');
  }

  if (file.size > maxSize) {
    errors.push(`File size (${formatFileSize(file.size)}) exceeds limit (${formatFileSize(maxSize)})`);
  }

  // File name security checks
  if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
    errors.push('File name contains invalid characters');
  }

  if (file.name.length > 255) {
    errors.push('File name is too long');
  }

  // Extension validation
  const extension = fileInfo.extension.toLowerCase();
  if (DANGEROUS_EXTENSIONS.includes(extension)) {
    errors.push(`File extension '${extension}' is not allowed for security reasons`);
  }

  // MIME type validation
  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type '${file.type}' is not allowed`);
  }

  // File signature validation (if requested)
  if (options.requireSignatureCheck && file.size > 0) {
    try {
      const buffer = await file.arrayBuffer();
      const signatureResult = await validateFileSignature(new Uint8Array(buffer), file.type);
      
      if (!signatureResult.safe) {
        errors.push(...signatureResult.threats);
      }

      if (signatureResult.actualMimeType && signatureResult.actualMimeType !== file.type) {
        warnings.push(`File signature suggests type '${signatureResult.actualMimeType}' but declared as '${file.type}'`);
      }
    } catch {
      warnings.push('Could not validate file signature');
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
    fileInfo
  };
}

/**
 * Validate file signature against declared MIME type
 */
export async function validateFileSignature(
  buffer: Uint8Array,
  declaredType: string
): Promise<FileSecurityScanResult> {
  const threats: string[] = [];

  // Check for dangerous file signatures
  for (const dangerous of DANGEROUS_SIGNATURES) {
    if (matchesSignature(buffer, dangerous.signature)) {
      threats.push(`Detected ${dangerous.description}`);
    }
  }

  // Detect actual file type by signature
  const actualMimeType = detectMimeTypeBySignature(buffer);

  // Check for MIME type mismatch
  if (actualMimeType && actualMimeType !== declaredType) {
    // Allow some common mismatches
    const allowedMismatches = [
      { declared: 'application/octet-stream', actual: /^image\// },
      { declared: 'text/plain', actual: /^text\// }
    ];

    const isAllowedMismatch = allowedMismatches.some(mismatch => 
      declaredType === mismatch.declared && mismatch.actual.test(actualMimeType!)
    );

    if (!isAllowedMismatch) {
      threats.push(`MIME type mismatch: declared as '${declaredType}' but appears to be '${actualMimeType}'`);
    }
  }

  // Check for embedded executables in documents
  if (declaredType.startsWith('application/') && buffer.length > 1024) {
    if (containsEmbeddedExecutable(buffer)) {
      threats.push('Document contains embedded executable content');
    }
  }

  return {
    safe: threats.length === 0,
    threats,
    fileType: declaredType,
    actualMimeType
  };
}

/**
 * Scan file for malicious content
 */
export async function scanFileForThreats(
  file: File,
  request?: NextRequest
): Promise<FileSecurityScanResult> {
  try {
    const buffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(buffer);
    
    const result = await validateFileSignature(uint8Array, file.type);
    
    // Additional content-based scans
    const contentThreats = await scanFileContent(uint8Array);
    result.threats.push(...contentThreats);
    result.safe = result.threats.length === 0;

    // Log suspicious files
    if (!result.safe && request) {
      await SecurityAuditLogger.logSuspiciousActivity(
        request,
        'Malicious file upload attempt',
        {
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
          threats: result.threats
        }
      );
    }

    return result;
  } catch {
    return {
      safe: false,
      threats: ['File scanning failed'],
      fileType: file.type
    };
  }
}

/**
 * Scan file content for malicious patterns
 */
async function scanFileContent(buffer: Uint8Array): Promise<string[]> {
  const threats: string[] = [];

  // Convert to string for text-based scans
  const content = new TextDecoder('utf-8', { fatal: false }).decode(buffer);

  // Scan for script injections
  const scriptPatterns = [
    /<script[^>]*>/i,
    /javascript:/i,
    /vbscript:/i,
    /on\w+\s*=/i,
    /eval\s*\(/i,
    /document\.write/i,
    /window\.location/i
  ];

  for (const pattern of scriptPatterns) {
    if (pattern.test(content)) {
      threats.push('Contains potentially malicious script content');
      break;
    }
  }

  // Scan for SQL injection patterns
  const sqlPatterns = [
    /union\s+select/i,
    /drop\s+table/i,
    /insert\s+into/i,
    /delete\s+from/i,
    /update\s+.*set/i
  ];

  for (const pattern of sqlPatterns) {
    if (pattern.test(content)) {
      threats.push('Contains potential SQL injection patterns');
      break;
    }
  }

  // Scan for command injection patterns
  const commandPatterns = [
    /\|\s*nc\s/i,
    /\|\s*netcat/i,
    /\|\s*bash/i,
    /\|\s*sh\s/i,
    /\|\s*cmd/i,
    /\|\s*powershell/i
  ];

  for (const pattern of commandPatterns) {
    if (pattern.test(content)) {
      threats.push('Contains potential command injection patterns');
      break;
    }
  }

  return threats;
}

// Helper functions
function matchesSignature(buffer: Uint8Array, signature: number[]): boolean {
  if (buffer.length < signature.length) return false;
  
  for (let i = 0; i < signature.length; i++) {
    if (buffer[i] !== signature[i]) return false;
  }
  
  return true;
}

function detectMimeTypeBySignature(buffer: Uint8Array): string | undefined {
  const signatures = [
    { signature: [0xFF, 0xD8, 0xFF], type: 'image/jpeg' },
    { signature: [0x89, 0x50, 0x4E, 0x47], type: 'image/png' },
    { signature: [0x47, 0x49, 0x46, 0x38], type: 'image/gif' },
    { signature: [0x25, 0x50, 0x44, 0x46], type: 'application/pdf' },
    { signature: [0x50, 0x4B, 0x03, 0x04], type: 'application/zip' },
    { signature: [0x50, 0x4B, 0x05, 0x06], type: 'application/zip' },
    { signature: [0x50, 0x4B, 0x07, 0x08], type: 'application/zip' }
  ];

  for (const sig of signatures) {
    if (matchesSignature(buffer, sig.signature)) {
      return sig.type;
    }
  }

  return undefined;
}

function containsEmbeddedExecutable(buffer: Uint8Array): boolean {
  // Look for PE header in the middle of the file
  for (let i = 100; i < buffer.length - 2; i++) {
    if (buffer[i] === 0x4D && buffer[i + 1] === 0x5A) {
      return true;
    }
  }
  return false;
}

function getFileExtension(filename: string): string {
  const lastDot = filename.lastIndexOf('.');
  return lastDot === -1 ? '' : filename.substring(lastDot);
}

function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

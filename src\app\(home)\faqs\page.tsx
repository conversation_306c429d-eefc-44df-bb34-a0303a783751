"use client";

import { useState } from "react";
import Footer from "@/components/home-components/shared/Footer";
import Header from "@/components/home-components/shared/Header";
import FAQsContent from "@/components/home-components/faqs/FAQsContent";
import FAQsHeroSearch from "@/components/home-components/faqs/FAQsHeroSearch";

export default function FAQsPage() {
  const [searchTerm, setSearchTerm] = useState("");

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main id="main-content" className="flex-1 pt-20 bg-white">
        {/* Hero Section */}
        <section className="relative px-4 sm:px-8 md:px-12 lg:px-[180px] py-24 sm:py-28 md:py-32 lg:py-36 mx-auto bg-[#1E4841]">
          <div className="max-w-6xl mx-auto text-center relative z-10">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Frequently Asked Questions
            </h1>
            <p className="text-lg md:text-xl text-[#F3F4F6] max-w-2xl mx-auto mb-8">
              Find answers to common questions about our platform, pricing, security, and support.
              Can&apos;t find what you&apos;re looking for? Contact our support team.
            </p>

            {/* Search Bar in Hero */}
            <FAQsHeroSearch
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
            />
          </div>
        </section>

        {/* FAQs Content */}
        <FAQsContent searchTerm={searchTerm} onSearchChange={setSearchTerm} />
      </main>
      <Footer />
    </div>
  );
}

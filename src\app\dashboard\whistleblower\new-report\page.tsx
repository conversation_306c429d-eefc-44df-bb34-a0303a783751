"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

import Header from "@/components/dashboard-components/Header";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertD<PERSON>ogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { Shield, AlertCircle, Home } from "lucide-react";
import { apiClient } from "@/lib/utils/apiClient";
import { ApiResponse } from "@/lib/types";

interface ReportFormData {
  title: string;
  category: string;
  dateOfOccurrence: string;
  location: string;
  description: string;
  isAnonymous: boolean;
  reportingPreferences: {
    emailUpdates: boolean;
    smsUpdates: boolean;
  };
}

export default function NewReportPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [sessionTimeoutOpen, setSessionTimeoutOpen] = useState(false);
  const [, setSessionTimeLeft] = useState(4.39); // 4:39 minutes as shown in screenshot
  const [formData, setFormData] = useState<ReportFormData>({
    title: "",
    category: "",
    dateOfOccurrence: "",
    location: "",
    description: "",
    isAnonymous: false,
    reportingPreferences: {
      emailUpdates: true,
      smsUpdates: false,
    },
  });

  const categories = [
    "Financial Misconduct",
    "Accounting Fraud / Financial Manipulation",
    "Corruption",
    "Harassment",
    "Safety Violation",
    "Environmental",
    "Discrimination",
    "Other"
  ];

  // Session timeout simulation
  useEffect(() => {
    const timer = setInterval(() => {
      setSessionTimeLeft(prev => {
        if (prev <= 0.01) {
          setSessionTimeoutOpen(true);
          return 0;
        }
        return prev - 0.01;
      });
    }, 600); // Update every 600ms for smooth countdown

    return () => clearInterval(timer);
  }, []);

  const formatTimeLeft = (minutes: number) => {
    const mins = Math.floor(minutes);
    const secs = Math.floor((minutes - mins) * 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Suppress unused variable warning - function is kept for future use
  void formatTimeLeft;

  const handleSessionContinue = () => {
    setSessionTimeoutOpen(false);
    setSessionTimeLeft(4.39); // Reset timer
  };

  const handleSessionLogout = () => {
    router.push('/dashboard/whistleblower');
  };

  const handleInputChange = (field: keyof ReportFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePreferenceChange = (field: keyof ReportFormData['reportingPreferences'], value: boolean) => {
    setFormData(prev => ({
      ...prev,
      reportingPreferences: {
        ...prev.reportingPreferences,
        [field]: value
      }
    }));
  };

  const handleSaveDraft = async () => {
    setLoading(true);
    try {
      const result = await apiClient.post('/api/reports/draft', formData) as ApiResponse;

      if (result.success) {
        toast.success('Draft saved successfully');
        router.push('/dashboard/whistleblower/my-reports');
      } else {
        toast.error(result.error || 'Failed to save draft');
      }
    } catch (error) {
      console.error('Save draft error:', error);
      toast.error('Failed to save draft');
    } finally {
      setLoading(false);
    }
  };

  const handleContinueToStep2 = () => {
    if (!formData.title.trim() || !formData.description.trim() || !formData.category) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Save step 1 data to session storage
    sessionStorage.setItem('reportStep1Data', JSON.stringify(formData));
    router.push('/dashboard/whistleblower/new-report/step-2');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard/whistleblower" className="flex items-center gap-1">
                  <Home className="w-4 h-4" />
                  Dashboard
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard/whistleblower/my-reports">
                  My Reports
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>New Whistleblower Report</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              New Whistleblower Report
            </h1>
            <p className="text-gray-600">
              Submit a confidential report about workplace concerns or misconduct
            </p>
          </div>

          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-[#1E4841] text-white rounded-full">
                  1
                </div>
                <span className="ml-2 text-sm font-medium text-[#1E4841]">Report Details</span>
              </div>
              <div className="flex-1 h-1 bg-gray-300 mx-4"></div>
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-gray-300 text-gray-500 rounded-full">
                  2
                </div>
                <span className="ml-2 text-sm font-medium text-gray-500">Additional Details</span>
              </div>
              <div className="flex-1 h-1 bg-gray-300 mx-4"></div>
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-gray-300 text-gray-500 rounded-full">
                  3
                </div>
                <span className="ml-2 text-sm font-medium text-gray-500">Review & Submit</span>
              </div>
            </div>
          </div>

          <Alert className="mb-6 border-green-200 bg-green-50">
            <Shield className="w-4 h-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <strong>Confidentiality Assurance:</strong> Your report will be handled with strict confidentiality. All information is encrypted and accessible only to authorized personnel. You can choose to remain anonymous by toggling the anonymous reporting option below.
            </AlertDescription>
          </Alert>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Report Information</CardTitle>
              <p className="text-sm text-gray-600">
                Provide details about the incident or concern you wish to report
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="title" className="text-sm font-medium">
                  Report Title *
                </Label>
                <Input
                  id="title"
                  placeholder="Provide a brief summary of the concern"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="category" className="text-sm font-medium">
                  Category *
                </Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => handleInputChange('category', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select the most relevant category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="dateOfOccurrence" className="text-sm font-medium">
                  Date of Occurrence
                </Label>
                <Input
                  id="dateOfOccurrence"
                  type="date"
                  placeholder="DD/MM/YYYY"
                  value={formData.dateOfOccurrence}
                  onChange={(e) => handleInputChange('dateOfOccurrence', e.target.value)}
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">
                  If ongoing or multiple incidents, select the most recent date
                </p>
              </div>

              <div>
                <Label htmlFor="location" className="text-sm font-medium">
                  Location
                </Label>
                <Input
                  id="location"
                  placeholder="Provide the location or department where the incident occurred"
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="description" className="text-sm font-medium">
                  Incident Details *
                </Label>
                <Textarea
                  id="description"
                  placeholder="Provide a detailed description of the incident, including specific details, dates, and any other relevant information. Please describe what happened, when it occurred, and who was involved."
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="mt-1 min-h-[120px]"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Be specific but avoid including names or identifying details in the title
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Reporting Preferences</CardTitle>
              <p className="text-sm text-gray-600">
                Choose how you would like to be contacted about this report
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="emailUpdates"
                  checked={formData.reportingPreferences.emailUpdates}
                  onCheckedChange={(checked) => 
                    handlePreferenceChange('emailUpdates', checked as boolean)
                  }
                />
                <Label htmlFor="emailUpdates" className="text-sm">
                  Receive email updates about this report
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="smsUpdates"
                  checked={formData.reportingPreferences.smsUpdates}
                  onCheckedChange={(checked) => 
                    handlePreferenceChange('smsUpdates', checked as boolean)
                  }
                />
                <Label htmlFor="smsUpdates" className="text-sm">
                  Receive SMS updates about this report
                </Label>
              </div>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Anonymous Reporting</CardTitle>
              <p className="text-sm text-gray-600">
                You can choose to file this report anonymously
              </p>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4 border-yellow-200 bg-yellow-50">
                <AlertCircle className="w-4 h-4 text-yellow-600" />
                <AlertDescription className="text-yellow-800">
                  <strong>Anonymous Report Information:</strong>
                  <ul className="mt-2 space-y-1 text-sm">
                    <li>• If you file anonymously, we cannot contact you directly</li>
                    <li>• You will receive a unique tracking number to check status</li>
                    <li>• Follow-up questions may be limited without your contact details</li>
                    <li>• Your identity will be completely protected from disclosure</li>
                  </ul>
                </AlertDescription>
              </Alert>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="anonymous"
                  checked={formData.isAnonymous}
                  onCheckedChange={(checked) => 
                    handleInputChange('isAnonymous', checked as boolean)
                  }
                />
                <Label htmlFor="anonymous" className="text-sm font-medium">
                  File this report anonymously (without revealing my identity)
                </Label>
              </div>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Privacy Protection Statement</CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-gray-600 space-y-3">
              <p>
                At 7IRIS, we are committed to protecting your privacy and ensuring your safety when you report concerns to us. 
                We understand that reporting misconduct can be difficult, and we want to assure you that we have robust measures in 
                place to protect your information.
              </p>
              <p>
                By submitting this report, you acknowledge that you have read and understood our privacy policy and consent to the 
                collection and processing of your personal information as described in our privacy statement.
              </p>
              <p>
                I acknowledge that I have read and understood the privacy protection statement and consent to the collection and processing 
                of my personal information as described in the privacy statement.
              </p>
            </CardContent>
          </Card>

          <div className="flex flex-col sm:flex-row gap-4 justify-end">
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard/whistleblower/my-reports')}
              disabled={loading}
            >
              Cancel & Return
            </Button>
            <Button
              variant="outline"
              onClick={handleSaveDraft}
              disabled={loading}
            >
              Save as Draft
            </Button>
            <Button
              onClick={handleContinueToStep2}
              disabled={loading}
              className="bg-[#1E4841] hover:bg-[#2A5D54]"
            >
              Continue to Next Step
            </Button>
          </div>
        </div>
      </div>

      {/* Session Timeout Dialog */}
      <AlertDialog open={sessionTimeoutOpen} onOpenChange={setSessionTimeoutOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Session Timeout Warning</AlertDialogTitle>
            <AlertDialogDescription>
              Your session is about to expire due to inactivity. Would you like to continue working on your report?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleSessionLogout}>
              Logout
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleSessionContinue}>
              Continue Session
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
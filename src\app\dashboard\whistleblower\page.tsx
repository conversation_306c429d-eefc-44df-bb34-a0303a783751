"use client";

import React from "react";
import Header from "@/components/dashboard-components/Header";
import WelcomeSection from "@/components/dashboard-components/whistleblower/dashboard/WelcomeSection";
import StatisticsCards from "@/components/dashboard-components/whistleblower/dashboard/StatisticsCards";
import ReportCharts from "@/components/dashboard-components/whistleblower/dashboard/ReportCharts";
import ReportTable from "@/components/dashboard-components/whistleblower/dashboard/ReportTable";
import RecentMessages from "@/components/dashboard-components/whistleblower/dashboard/RecentMessages";
import { useAuth } from "@/hooks/useAuth";
import { useDashboardData } from "@/hooks/useDashboardData";
import QuickActions from "@/components/dashboard-components/whistleblower/dashboard/QuickActions";


export default function DashboardPage() {
    const { user, isAuthenticated } = useAuth();
    const dashboardData = useDashboardData();

    console.log('Dashboard Page: Auth status:', { isAuthenticated, user: user ? { id: user.id, role: user.role } : null });
    console.log('Dashboard Page: dashboardData:', dashboardData);
    console.log('Dashboard Page: stats:', dashboardData.stats);
    console.log('Dashboard Page: reports:', dashboardData.reports);
    console.log('Dashboard Page: isLoading:', dashboardData.isLoading);

    // Force refresh if authenticated but no data
    React.useEffect(() => {
        if (isAuthenticated && user?.id && !dashboardData.stats && !dashboardData.isLoading) {
            console.log('Dashboard Page: Forcing data refresh');
            dashboardData.refreshAll();
        }
    }, [isAuthenticated, user?.id, dashboardData, dashboardData.stats, dashboardData.isLoading, dashboardData.refreshAll]);

    return (
        <>
            <div className="w-full h-full">
                <Header onNotificationNavigate={(notification) => {
                    // Use the centralized navigation logic
                    switch (notification.type) {
                        case 'report_update':
                            if (notification.reportId) {
                                dashboardData.navigateToReport(notification.reportId);
                            }
                            break;
                        case 'message':
                            dashboardData.navigateToMessage('');
                            break;
                        case 'system':
                        case 'alert':
                        default:
                            if (notification.actionUrl) {
                                window.location.href = notification.actionUrl;
                            } else {
                                window.location.href = '/dashboard/notifications';
                            }
                            break;
                    }
                }} />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="Dashboard overview"
                >
                    {/* Welcome Section */}
                    <section aria-labelledby="welcome-section">
                        <h2 id="welcome-section" className="sr-only">Welcome Section</h2>
                        <WelcomeSection userName={user?.name || user?.firstName || 'User'} />
                    </section>

                    {/* Statistics Cards */}
                    <section aria-labelledby="statistics-section">
                        <h2 id="statistics-section" className="sr-only">Report Statistics</h2>
                        <StatisticsCards
                            stats={dashboardData.stats}
                            isLoading={dashboardData.isLoading}
                        />
                    </section>

                    {/* Charts Section */}
                    <section aria-labelledby="charts-section">
                        <h2 id="charts-section" className="sr-only">Report Analytics Charts</h2>
                        <ReportCharts
                            stats={dashboardData.stats}
                            isLoading={dashboardData.isLoading}
                        />
                    </section>

                    {/* Your Reports Table */}
                    <section aria-labelledby="reports-section">
                        <h2 id="reports-section" className="sr-only">Your Reports</h2>
                        <ReportTable
                            reports={dashboardData.reports}
                            isLoading={dashboardData.isLoading}
                            onReportClick={dashboardData.navigateToReport}
                        />
                    </section>

                    <div className="grid grid-cols-1 xl:grid-cols-3 gap-3 sm:gap-4 md:gap-6 h-full">
                        {/* Recent Messages from Investigators */}
                        <section aria-labelledby="messages-section" className="lg:col-span-2 h-full">
                            <h2 id="messages-section" className="sr-only">Recent Messages</h2>
                            <RecentMessages
                                messages={dashboardData.recentMessages}
                                isLoading={dashboardData.isLoading}
                                onMessageClick={dashboardData.navigateToMessage}
                            />
                        </section>

                        {/* Quick Actions */}
                        <section aria-labelledby="actions-section">
                            <QuickActions />
                        </section>
                    </div>
                </main>
            </div>
        </>
    );
}

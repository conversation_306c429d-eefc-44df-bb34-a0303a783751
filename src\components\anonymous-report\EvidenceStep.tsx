"use client";

import React, { useRef, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, ArrowRight, Upload, File, X } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface ReportFormData {
  title: string;
  category: string;
  dateOfOccurrence: string;
  location: string;
  description: string;
  evidenceFiles: File[];
  evidenceDescription: string;
  privacyConsent: boolean;
  reportToken?: string;
  submissionId?: string;
}

interface EvidenceStepProps {
  formData: ReportFormData;
  updateFormData: (updates: Partial<ReportFormData>) => void;
  onNext: () => void;
  onBack: () => void;
}

const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'image/jpeg',
  'image/png',
  'image/gif',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
];

const FILE_TYPE_EXTENSIONS = {
  'application/pdf': 'PDF',
  'application/msword': 'DOC',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCX',
  'image/jpeg': 'JPG',
  'image/png': 'PNG',
  'image/gif': 'GIF',
  'application/vnd.ms-excel': 'XLS',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'XLSX'
};

export default function EvidenceStep({ formData, updateFormData, onNext, onBack }: EvidenceStepProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);

  const handleInputChange = (field: keyof ReportFormData, value: string) => {
    updateFormData({ [field]: value });
  };

  const validateFile = (file: File): string | null => {
    if (file.size > MAX_FILE_SIZE) {
      return `File "${file.name}" is too large. Maximum size is 20MB.`;
    }
    
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return `File "${file.name}" has an unsupported format. Please use PDF, DOC, DOCX, JPG, PNG, GIF, XLS, or XLSX files.`;
    }
    
    return null;
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const newFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach(file => {
      const error = validateFile(file);
      if (error) {
        errors.push(error);
      } else {
        // Check if file already exists
        const exists = formData.evidenceFiles.some(existingFile => 
          existingFile.name === file.name && existingFile.size === file.size
        );
        if (!exists) {
          newFiles.push(file);
        }
      }
    });

    if (errors.length > 0) {
      toast({
        title: "File Upload Error",
        description: errors.join('\n'),
        variant: "destructive"
      });
    }

    if (newFiles.length > 0) {
      updateFormData({
        evidenceFiles: [...formData.evidenceFiles, ...newFiles]
      });
      toast({
        title: "Files Added",
        description: `${newFiles.length} file(s) added successfully.`
      });
    }
  };

  const handleFileRemove = (index: number) => {
    const updatedFiles = formData.evidenceFiles.filter((_, i) => i !== index);
    updateFormData({ evidenceFiles: updatedFiles });
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileTypeDisplay = (file: File): string => {
    return FILE_TYPE_EXTENSIONS[file.type as keyof typeof FILE_TYPE_EXTENSIONS] || 'Unknown';
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-gray-900">
          Evidence Documentation
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Upload Evidence */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">
            Upload Evidence*
          </Label>
          
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive 
                ? 'border-[#1E4841] bg-[#1E4841]/5' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              Drag and drop files here
            </p>
            <p className="text-sm text-gray-500 mb-4">
              or click to select files
            </p>
            
            <Button
              type="button"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              className="mb-4"
            >
              Browse Files
            </Button>
            
            <p className="text-xs text-gray-500">
              Supported formats: PDF, DOC, DOCX, JPG, PNG, GIF, XLS, XLSX (Max 20MB per file)
            </p>
            
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.xls,.xlsx"
              onChange={(e) => handleFileSelect(e.target.files)}
              className="hidden"
            />
          </div>
        </div>

        {/* Uploaded Files List */}
        {formData.evidenceFiles.length > 0 && (
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">
              Uploaded Evidence ({formData.evidenceFiles.length} files)
            </Label>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {formData.evidenceFiles.map((file, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <File className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 truncate max-w-xs">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {getFileTypeDisplay(file)} • {formatFileSize(file.size)} • Uploaded {new Date().toLocaleDateString()}
                      </p>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-1">
                        Verified
                      </span>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleFileRemove(index)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Evidence Description */}
        <div className="space-y-2">
          <Label htmlFor="evidenceDescription" className="text-sm font-medium text-gray-700">
            Evidence Description*
          </Label>
          <Textarea
            id="evidenceDescription"
            placeholder="Provide a description of the uploaded evidence"
            value={formData.evidenceDescription}
            onChange={(e) => handleInputChange('evidenceDescription', e.target.value)}
            className="w-full min-h-[100px] resize-none"
            maxLength={1000}
          />
          <div className="text-xs text-gray-500 text-right">
            {formData.evidenceDescription.length}/1000 characters
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between pt-6">
          <Button
            onClick={onBack}
            variant="outline"
            className="px-6 py-2 flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <Button
            onClick={onNext}
            className="bg-[#1E4841] hover:bg-[#1E4841]/90 text-white px-6 py-2 flex items-center gap-2"
          >
            Next
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/db/connection';
import CustomerInquiry from '@/lib/db/models/CustomerInquiry';
import { emailService } from '@/lib/email/emailService';
import { z } from 'zod';

export const runtime = 'nodejs';

const customerSuccessSchema = z.object({
  subject: z.enum(['account_setup', 'feature_training', 'best_practices', 'integration_support', 'performance_review', 'renewal_discussion', 'other']),
  message: z.string().min(10).max(2000),
  preferredContactMethod: z.enum(['email', 'phone', 'video_call', 'in_person']),
  preferredTimeSlot: z.enum(['morning', 'afternoon', 'evening', 'flexible']),
  isUrgent: z.boolean().default(false),
});

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    
    // Validate the form data
    const validationResult = customerSuccessSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid form data',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // Get client metadata
    const userAgent = request.headers.get('user-agent') || undefined;
    const forwarded = request.headers.get('x-forwarded-for');
    const ipAddress = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || undefined;

    // TODO: In a real app, you would get customer info from session/auth
    // For now, we'll use placeholder data
    const customerEmail = '<EMAIL>'; // Would come from auth
    const customerName = 'Customer Name'; // Would come from auth
    const companyId = null; // Would come from auth

    // Create customer inquiry
    const customerInquiry = new CustomerInquiry({
      ...validatedData,
      customerEmail,
      customerName,
      companyId,
      metadata: {
        userAgent,
        ipAddress,
        source: 'customer_success_form'
      }
    });

    await customerInquiry.save();

    // Send notification email to customer success team
    try {
      const customerSuccessEmail = process.env.CUSTOMER_SUCCESS_EMAIL || '<EMAIL>';
      
      const emailSent = await emailService.sendCustomerSuccessNotification({
        inquiryNumber: customerInquiry.inquiryNumber,
        subject: customerInquiry.subject,
        message: customerInquiry.message,
        preferredContactMethod: customerInquiry.preferredContactMethod,
        preferredTimeSlot: customerInquiry.preferredTimeSlot,
        isUrgent: customerInquiry.isUrgent,
        customerName: customerInquiry.customerName || 'Customer',
        customerEmail: customerInquiry.customerEmail || 'Unknown',
        customerSuccessEmail
      });

      if (emailSent && customerInquiry.customerEmail) {
        // Send auto-reply to customer
        await emailService.sendCustomerSuccessAutoReply({
          inquiryNumber: customerInquiry.inquiryNumber,
          customerName: customerInquiry.customerName || 'Customer',
          customerEmail: customerInquiry.customerEmail,
          subject: customerInquiry.subject,
          isUrgent: customerInquiry.isUrgent
        });
      }
    } catch (emailError) {
      console.error('Failed to send customer success emails:', emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      data: {
        inquiryNumber: customerInquiry.inquiryNumber,
        message: 'Your request has been submitted successfully. Sarah will contact you within 1 business day.',
        estimatedResponseTime: customerInquiry.isUrgent ? '24 hours' : '48 hours'
      }
    });

  } catch (error) {
    console.error('Customer success inquiry submission error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred. Please try again later.' 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const inquiryNumber = searchParams.get('inquiry');
    const email = searchParams.get('email');

    if (inquiryNumber && email) {
      // Get specific customer inquiry
      const customerInquiry = await (CustomerInquiry as any).findOne({
        inquiryNumber: inquiryNumber,
        customerEmail: email.toLowerCase()
      }).select('-metadata').populate('assignedTo', 'firstName lastName email');

      if (!customerInquiry) {
        return NextResponse.json(
          { success: false, error: 'Customer inquiry not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: customerInquiry
      });
    }

    // Get statistics (admin only - would need auth check in real app)
    const stats = await (CustomerInquiry as any).getStats();
    const overdueInquiries = await (CustomerInquiry as any).findOverdue();
    
    return NextResponse.json({
      success: true,
      data: {
        ...stats,
        overdueCount: overdueInquiries.length,
        overdueInquiries: overdueInquiries.slice(0, 10) // Limit to 10 for performance
      }
    });

  } catch (error) {
    console.error('Customer inquiry retrieval error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const inquiryNumber = searchParams.get('inquiry');
    
    if (!inquiryNumber) {
      return NextResponse.json(
        { success: false, error: 'Inquiry number is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { status, note, assignedTo } = body;

    const customerInquiry = await (CustomerInquiry as any).findOne({ inquiryNumber });
    
    if (!customerInquiry) {
      return NextResponse.json(
        { success: false, error: 'Customer inquiry not found' },
        { status: 404 }
      );
    }

    // Update status if provided
    if (status) {
      customerInquiry.status = status;
      if (status === 'contacted') {
        customerInquiry.contactedAt = new Date();
      } else if (status === 'resolved') {
        customerInquiry.resolvedAt = new Date();
      }
    }

    // Update assigned user if provided
    if (assignedTo) {
      customerInquiry.assignedTo = assignedTo;
    }

    // Add note if provided
    if (note && note.content && note.addedBy) {
      await customerInquiry.addNote(note.content, note.addedBy);
    }

    await customerInquiry.save();

    return NextResponse.json({
      success: true,
      data: customerInquiry
    });

  } catch (error) {
    console.error('Customer inquiry update error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

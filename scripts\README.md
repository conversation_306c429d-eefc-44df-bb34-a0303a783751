# 🛠️ Scripts Directory

This directory contains consolidated scripts for development, testing, security, and database operations.

## 📁 Script Structure

### 🧪 **testing-suite.ts**
**Consolidated testing framework** - All testing functionality in one place.

**Capabilities:**
- Comprehensive test suite execution
- Security audit and penetration testing
- Load testing and performance validation
- Database and session testing
- Implementation verification
- Rate limiting testing

**Usage:**
```bash
pnpm test:all                    # Run all tests
pnpm test:security              # Run security tests only
pnpm test:load                  # Run load tests only
pnpm test:database              # Run database tests only
pnpm test:implementations       # Run implementation tests only
pnpm test:rate-limiting         # Run rate limiting tests only
```

### 🔐 **security-operations.ts**
**Consolidated security operations** - All security-related operations.

**Capabilities:**
- Production key rotation with zero downtime
- Emergency key backup and recovery
- JWT secret rotation
- Security health checks
- Security deployment fixes

**Usage:**
```bash
pnpm security:key-rotate:start      # Start key rotation
pnpm security:key-rotate:status     # Check rotation status
pnpm security:key-rotate:complete   # Complete rotation
pnpm security:key-rotate:rollback   # Rollback rotation
pnpm security:backup:create         # Create emergency backup
pnpm security:backup:verify         # Verify backup
pnpm security:backup:restore        # Restore from backup
pnpm security:jwt:rotate            # Rotate JWT secrets
pnpm security:health-check          # Run security health check
```

### 🗄️ **database-operations.ts**
**Consolidated database operations** - All database-related functionality.

**Capabilities:**
- Database seeding and initialization
- Conversation and message management
- Data verification and fixing
- Report management
- User management
- Database health checks

**Usage:**
```bash
pnpm db:seed                        # Seed database with initial data
pnpm db:check-conversations         # Check conversation integrity
pnpm db:fix-conversations           # Fix conversation issues
pnpm db:check-messages              # Check message integrity
pnpm db:fix-messages                # Fix message issues
pnpm db:check-reports               # Check report data
pnpm db:update-reports              # Update report data
pnpm db:verify-all                  # Verify all data integrity
pnpm db:health-check                # Run database health check
```

### 🚀 **development-utilities.ts**
**Consolidated development utilities** - All development helper functions.

**Capabilities:**
- Complete development environment setup
- Development user creation and management
- Environment validation and configuration
- Upload structure creation in public/ directory
- Quick testing and validation

**Usage:**
```bash
pnpm dev:setup                      # Complete development setup
pnpm dev:users:create               # Create development users
pnpm dev:users:reset                # Reset user passwords
pnpm dev:users:info                 # Show user information
pnpm dev:env:validate               # Validate environment variables
pnpm dev:env:setup                  # Setup environment configuration
pnpm dev:uploads:create             # Create upload directory structure in public/
pnpm dev:uploads:cleanup            # Clean up old upload directories
pnpm dev:quick-test                 # Run quick development tests
```

## 🗂️ Script Consolidation

### Previously Consolidated Scripts
The following scripts have been consolidated into the main scripts above:

**Merged into testing-suite.ts:**
- `comprehensive-test-suite.ts` - Main test suite
- `security-audit-framework.ts` - Security testing
- `load-testing-framework.ts` - Load testing
- `test-database-and-sessions.ts` - Database testing
- `test-implementations.ts` - Implementation testing
- `test-rate-limiting.ts` - Rate limiting tests
- `test-encryption.ts` - Encryption testing
- `test-message-api.ts` - Message API testing
- `test-message-encryption-integration.ts` - Integration testing
- `quick-load-test.ts` - Quick load testing

**Merged into security-operations.ts:**
- `production-key-rotation.ts` - Key rotation
- `emergency-key-backup.ts` - Emergency backup
- `security-jwt-rotation.ts` - JWT rotation
- `deploy-security-fixes.ts` - Security deployment

**Merged into database-operations.ts:**
- `seed.ts` - Database seeding
- `check-conversations.ts` - Conversation checking
- `fix-all-conversation-issues.ts` - Conversation fixes
- `check-all-messages.ts` - Message checking
- `fix-message-encryption.ts` - Message fixes
- `check-existing-reports.ts` - Report checking
- `update-reports-complete-data.ts` - Report updates
- `analyze-conversation-issues.ts` - Issue analysis
- `verify-all-fixes.ts` - Verification
- `verify-new-conversations.ts` - New conversation verification

**Merged into development-utilities.ts:**
- `setup-dev-users.ts` - User setup
- `create-upload-structure.ts` - Upload structure
- `production-environment-tuning.ts` - Environment tuning
- `fix-authentication-issue.ts` - Auth fixes
- `fix-conversations-direct.ts` - Direct fixes
- `fix-inappropriate-messages.ts` - Message fixes
- `fix-incomplete-test-reports.ts` - Report fixes
- `fix-remaining-greeting-issue.ts` - Greeting fixes

## 🎯 Benefits of Consolidation

1. **Reduced Complexity** - 33 scripts consolidated into 4 main scripts
2. **Better Organization** - Logical grouping by functionality
3. **Easier Maintenance** - Single source of truth for each operation type
4. **Improved Discoverability** - Clear naming and categorization
5. **Reduced Duplication** - Shared utilities and common patterns
6. **Better Error Handling** - Consistent error handling across all operations
7. **Enhanced Logging** - Standardized logging and reporting

## 📖 How to Use These Scripts

### For Development
1. Start with `pnpm dev:setup` for complete environment setup
2. Use `pnpm dev:quick-test` for rapid validation
3. Use `pnpm db:seed` to populate development data

### For Testing
1. Use `pnpm test:all` for comprehensive testing
2. Use specific test commands for targeted testing
3. Check test reports in the `reports/tests/` directory

### For Security Operations
1. Use `pnpm security:health-check` for regular security validation
2. Use key rotation commands for production key management
3. Use backup commands for emergency preparedness

### For Database Operations
1. Use health check commands for database monitoring
2. Use verification commands to ensure data integrity
3. Use fix commands to resolve data issues

## 🔄 Script Maintenance

### Update Schedule
- **Testing Suite**: Update as new tests are added
- **Security Operations**: Update as security requirements change
- **Database Operations**: Update as data models evolve
- **Development Utilities**: Update as development workflow changes

### Adding New Functionality
1. Identify the appropriate consolidated script
2. Add new methods to the relevant class
3. Update the command-line interface
4. Update this README with new commands
5. Test the new functionality thoroughly

### Best Practices
- Keep scripts modular and well-documented
- Use consistent error handling patterns
- Provide clear success/failure feedback
- Log important operations for audit trails
- Test scripts in development before production use

## 📞 Support

For questions about these scripts:
- **Testing Issues**: Check test reports and logs
- **Security Issues**: Follow emergency procedures in docs
- **Database Issues**: Check database health reports
- **Development Issues**: Validate environment configuration

## 🚨 Emergency Procedures

If scripts fail in production:
1. Check the relevant health check command first
2. Review error logs and reports
3. Use rollback commands if available
4. Follow emergency procedures in documentation
5. Contact the appropriate team for support

---

**Script Structure Version:** 1.0  
**Last Updated:** August 22, 2025  
**Next Review:** September 22, 2025

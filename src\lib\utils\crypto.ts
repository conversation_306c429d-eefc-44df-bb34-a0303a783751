import crypto from 'crypto';

/**
 * Generate a secure random token for anonymous report tracking
 */
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Generate a reference number for reports in RPT-YYYY-XXX format (incremental)
 * Note: This function requires database access for proper incremental numbering
 * Use the API endpoints for actual report creation
 */
export function generateReferenceNumber(): string {
  const year = new Date().getFullYear();
  // This is a fallback - actual implementation should use database count
  const randomNum = Math.floor(Math.random() * 999) + 1;
  return `RPT-${year}-${randomNum.toString().padStart(3, '0')}`;
}

/**
 * Generate a session ID for anonymous users
 */
export function generateSessionId(): string {
  return `anon_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
}

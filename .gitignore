# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Documentation
/docs/

# Local configuration files
.vscode/*

# Reports and logs - ignore generated files but keep structure
reports/**/*.json
reports/**/*.log
reports/**/*.xml
reports/**/*.csv
reports/**/*.html
reports/**/*.pdf
reports/**/*.tmp
reports/**/*.temp
reports/security/encryption-keys/*
!reports/security/encryption-keys/.gitkeep
reports/audits/*
!reports/audits/.gitkeep
reports/database/*
!reports/database/.gitkeep
reports/performance/*
!reports/performance/.gitkeep
reports/deployment/*
!reports/deployment/.gitkeep

# Keep structure files and important summaries
!reports/README.md
!reports/**/.gitkeep
!reports/summaries/*.md
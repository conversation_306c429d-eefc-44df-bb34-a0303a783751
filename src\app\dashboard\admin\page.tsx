"use client";

import Header from "@/components/dashboard-components/Header";

import AdminWelcomeSection from "@/components/dashboard-components/admin/dashboard/AdminWelcomeSection";
import AdminStatisticsCards from "@/components/dashboard-components/admin/dashboard/AdminStatisticsCards";
import AdminCaseTrends from "@/components/dashboard-components/admin/dashboard/AdminCaseTrends";
import AdminRecentCases from "@/components/dashboard-components/admin/dashboard/AdminRecentCases";
import AdminRecentActivity from "@/components/dashboard-components/admin/dashboard/AdminRecentActivity";

export default function DashboardPage() {
    // Removed mock notification initialization - using real API data via NotificationContext

    return (
        <>
            <div className="w-full h-full">
                <Header />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="Dashboard overview"
                >
                    {/* Welcome Section */}
                    <section aria-labelledby="welcome-section">
                        <h2 id="welcome-section" className="sr-only">Welcome Section</h2>
                        <AdminWelcomeSection />
                    </section>

                    {/* Statistics Cards */}
                    <section aria-labelledby="statistics-section">
                        <h2 id="statistics-section" className="sr-only">Admin Statistics</h2>
                        <AdminStatisticsCards />
                    </section>

                    {/* Charts Section */}
                    <section aria-labelledby="charts-section">
                        <h2 id="charts-section" className="sr-only">Case Trends and Distribution</h2>
                        <AdminCaseTrends />
                    </section>

                    {/* Recent Activity and Cases Side by Side */}
                    <div className="grid grid-cols-1 xl:grid-cols-3 gap-3 sm:gap-4 md:gap-6 h-full">
                        {/* Recent Activity */}
                        <section aria-labelledby="activity-section" className="xl:col-span-1 h-full">
                            <h2 id="activity-section" className="sr-only">Recent Activity</h2>
                            <AdminRecentActivity />
                        </section>

                        {/* Recent Cases */}
                        <section aria-labelledby="cases-section" className="xl:col-span-2 h-full">
                            <h2 id="cases-section" className="sr-only">Recent Cases</h2>
                            <AdminRecentCases />
                        </section>
                    </div>
                </main>
            </div>
        </>
    );
}
import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { loginSchema } from '@/lib/schemas';
import { validateTestUserCredentials } from '@/lib/auth/secure-test-users';
import { generateToken } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';
import { withAuthSecurity } from '@/lib/middleware/integrated-security';

export const runtime = 'nodejs';

async function loginHandler(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = loginSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { email, password, remember } = validationResult.data;

    // First, try to authenticate with database users
    await connectDB();
    const dbAuthResult = await DataService.authenticateUser(email, password);
    
    if (dbAuthResult.user) {
      // Database user found and authenticated
      const user = dbAuthResult.user;
      const token = generateToken(user._id.toString(), user.role, user.companyId?.toString(), user.email);

      return NextResponse.json({
        success: true,
        message: 'Login successful',
        token,
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          role: user.role,
          companyId: user.companyId,
          isActive: user.isActive,
          lastLogin: user.lastLogin
        },
        remember
      });
    }

    // If database authentication failed, try secure test users (development only)
    const testUser = await validateTestUserCredentials(email, password);
    if (testUser) {
      const token = generateToken('test-user-id', testUser.user.role, testUser.user.companyName, testUser.user.email);

      return NextResponse.json({
        success: true,
        message: 'Login successful (test user)',
        token,
        user: {
          id: 'test-user-id',
          email: testUser.user.email,
          firstName: testUser.user.firstName,
          lastName: testUser.user.lastName,
          fullName: `${testUser.user.firstName} ${testUser.user.lastName}`,
          role: testUser.user.role,
          companyId: testUser.user.companyName,
          isActive: testUser.user.isActive,
          lastLogin: new Date()
        },
        remember,
        isTestUser: true
      });
    }

    // If both failed, return error
    if (dbAuthResult.error) {
      return NextResponse.json(
        { success: false, error: dbAuthResult.error },
        { status: 401 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Invalid email or password. Please check your credentials and try again.'
      },
      { status: 401 }
    );

  } catch {
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Apply rate limiting security middleware
export const POST = withAuthSecurity(loginHandler);
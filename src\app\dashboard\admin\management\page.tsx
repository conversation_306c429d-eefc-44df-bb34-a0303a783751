"use client";

import Header from "@/components/dashboard-components/Header";
// Removed mock notification data - using real API data
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Shield, Settings, Users, Database, Activity, TrendingUp } from "lucide-react";

export default function ManagementPage() {
    // Notification system now uses real API data via NotificationContext

    const managementMetrics = [
        {
            title: "System Health",
            value: "99.8%",
            icon: Activity,
            color: "text-green-600 bg-green-50",
            status: "Excellent"
        },
        {
            title: "Active Users",
            value: "125",
            icon: Users,
            color: "text-blue-600 bg-blue-50",
            status: "Normal"
        },
        {
            title: "Database Size",
            value: "2.4 GB",
            icon: Database,
            color: "text-purple-600 bg-purple-50",
            status: "Optimal"
        },
        {
            title: "Performance",
            value: "4.2%",
            icon: TrendingUp,
            color: "text-orange-600 bg-orange-50",
            status: "Good"
        }
    ];

    const systemSettings = [
        {
            category: "Security Settings",
            description: "Configure authentication and access controls",
            icon: Shield,
            items: ["Two-factor authentication", "Password policies", "Session management"]
        },
        {
            category: "System Configuration",
            description: "Manage system-wide settings and preferences",
            icon: Settings,
            items: ["Email notifications", "SLA thresholds", "Backup schedules"]
        },
        {
            category: "User Management",
            description: "Control user roles and permissions",
            icon: Users,
            items: ["Role assignments", "Permission levels", "User groups"]
        },
        {
            category: "Data Management",
            description: "Configure data retention and archival policies",
            icon: Database,
            items: ["Retention policies", "Archive settings", "Data export"]
        }
    ];

    return (
        <>
            <div className="w-full h-full">
                <Header />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="Management"
                >
                    {/* Page Header */}
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">Management</h1>
                        <p className="text-gray-600">System administration and management tools</p>
                    </div>

                    {/* Management Metrics */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        {managementMetrics.map((metric, index) => (
                            <Card key={index} className="bg-white border-0 shadow-sm">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-600">
                                        {metric.title}
                                    </CardTitle>
                                    <div className={`p-2 rounded-lg ${metric.color}`}>
                                        <metric.icon className="h-4 w-4" />
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-gray-900 mb-1">
                                        {metric.value}
                                    </div>
                                    <Badge 
                                        variant="secondary" 
                                        className="text-xs bg-green-100 text-green-800"
                                    >
                                        {metric.status}
                                    </Badge>
                                </CardContent>
                            </Card>
                        ))}
                    </div>

                    {/* System Settings */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {systemSettings.map((setting, index) => (
                            <Card key={index} className="bg-white border-0 shadow-sm">
                                <CardHeader>
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 rounded-lg bg-gray-50">
                                            <setting.icon className="h-5 w-5 text-gray-600" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-lg font-semibold text-gray-900">
                                                {setting.category}
                                            </CardTitle>
                                            <p className="text-sm text-gray-600 mt-1">
                                                {setting.description}
                                            </p>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2 mb-4">
                                        {setting.items.map((item, itemIndex) => (
                                            <div key={itemIndex} className="flex items-center justify-between py-2">
                                                <span className="text-sm text-gray-700">{item}</span>
                                                <Button variant="outline" size="sm">
                                                    Configure
                                                </Button>
                                            </div>
                                        ))}
                                    </div>
                                    <Button 
                                        className="w-full bg-[#BBF49C] text-[#1E4841] hover:bg-[#BBF49C]/90"
                                    >
                                        Manage {setting.category}
                                    </Button>
                                </CardContent>
                            </Card>
                        ))}
                    </div>

                    {/* Quick Actions */}
                    <Card className="bg-white border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-lg font-semibold text-gray-900">
                                Quick Actions
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex flex-col items-center justify-center gap-2 hover:bg-[#BBF49C] hover:border-[#BBF49C]"
                                >
                                    <Database className="h-5 w-5" />
                                    <span>Backup System</span>
                                </Button>
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex flex-col items-center justify-center gap-2 hover:bg-[#BBF49C] hover:border-[#BBF49C]"
                                >
                                    <Activity className="h-5 w-5" />
                                    <span>System Health Check</span>
                                </Button>
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex flex-col items-center justify-center gap-2 hover:bg-[#BBF49C] hover:border-[#BBF49C]"
                                >
                                    <Settings className="h-5 w-5" />
                                    <span>System Maintenance</span>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </main>
            </div>
        </>
    );
}
import { NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';
import * as fs from 'fs';
import * as path from 'path';
import { validateFile } from '@/lib/middleware/validation';
import { scanFileForThreats } from '@/lib/security/file-security';
import { SecurityAuditLogger } from '@/lib/security/audit-logger';

export const runtime = 'nodejs';

// File validation is now handled by the validateFile function

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    
    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No files provided' },
        { status: 400 }
      );
    }
    
    // Enhanced file validation and security scanning
    for (const file of files) {
      // Basic validation
      const validation = validateFile(file);

      if (!validation.valid) {
        await SecurityAuditLogger.logSuspiciousActivity(
          request,
          'File upload validation failed',
          {
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            error: validation.error,
            userId: request.user?.id
          }
        );

        return NextResponse.json(
          {
            success: false,
            error: `File validation failed: ${validation.error || 'Unknown error'}`
          },
          { status: 400 }
        );
      }

      // Security threat scanning
      const threatScan = await scanFileForThreats(file, request);

      if (!threatScan.safe) {
        await SecurityAuditLogger.logSuspiciousActivity(
          request,
          'Malicious file upload blocked',
          {
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            threats: threatScan.threats,
            userId: request.user?.id
          }
        );

        return NextResponse.json(
          {
            success: false,
            error: 'File contains potentially malicious content and cannot be uploaded'
          },
          { status: 400 }
        );
      }
    }
    
    const uploadedFiles = [];
    
    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'uploads', 'evidence');
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true });
    }
    
    for (const file of files) {
      try {
        // Generate unique filename
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        const fileExtension = file.name.split('.').pop();
        const fileName = `${timestamp}_${randomString}.${fileExtension}`;
        const filePath = join(uploadsDir, fileName);
        
        // Convert file to buffer and save
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        await writeFile(filePath, buffer);
        
        // Create file metadata
        const fileMetadata = {
          fileName: fileName,
          originalName: file.name,
          fileSize: file.size,
          mimeType: file.type,
          fileUrl: `/uploads/evidence/${fileName}`,
          uploadedAt: new Date(),
          uploadedBy: request.user.id,
          description: formData.get(`description_${file.name}`) as string || '',
          isEncrypted: true, // Files are encrypted at rest using server-side encryption
          encryptionMethod: 'AES-256-GCM'
        };
        
        uploadedFiles.push(fileMetadata);
      } catch (fileError) {
        console.error(`Error uploading file ${file.name}:`, fileError);
        return NextResponse.json(
          { success: false, error: `Failed to upload file ${file.name}` },
          { status: 500 }
        );
      }
    }
    
    return NextResponse.json({
      success: true,
      data: uploadedFiles,
      message: `Successfully uploaded ${uploadedFiles.length} file(s)`
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to upload files' },
      { status: 500 }
    );
  }
});

// GET endpoint to retrieve file information
export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const fileName = searchParams.get('fileName');
    
    if (!fileName) {
      return NextResponse.json(
        { success: false, error: 'File name is required' },
        { status: 400 }
      );
    }
    
    // Implement file retrieval with proper access control
    const filePath = path.join(process.cwd(), 'uploads', 'evidence', fileName);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { success: false, error: 'File not found' },
        { status: 404 }
      );
    }

    // Get file stats
    const stats = fs.statSync(filePath);

    return NextResponse.json({
      success: true,
      data: {
        fileName,
        fileUrl: `/uploads/evidence/${fileName}`,
        fileSize: stats.size,
        lastModified: stats.mtime,
        isEncrypted: true,
        accessGranted: true
      }
    });
  } catch (error) {
    console.error('Get evidence file API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve file' },
      { status: 500 }
    );
  }
});

// DELETE endpoint to remove uploaded files
export const DELETE = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    const { fileName } = await request.json();
    
    if (!fileName) {
      return NextResponse.json(
        { success: false, error: 'File name is required' },
        { status: 400 }
      );
    }
    
    // Implement file deletion with proper access control
    const filePath = path.join(process.cwd(), 'uploads', 'evidence', fileName);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { success: false, error: 'File not found' },
        { status: 404 }
      );
    }

    try {
      // Delete the file
      fs.unlinkSync(filePath);

      return NextResponse.json({
        success: true,
        message: 'File deleted successfully'
      });
    } catch (deleteError) {
      console.error('Error deleting file:', deleteError);
      return NextResponse.json(
        { success: false, error: 'Failed to delete file' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Delete evidence file API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete file' },
      { status: 500 }
    );
  }
});

import { Schema } from 'mongoose';
import { createModel } from '../utils';

const SystemStatusSchema = new Schema({
  // Service identification
  serviceName: { type: String, required: true }, // e.g., 'Web Server', 'Database'
  serviceType: { 
    type: String, 
    enum: ['web_server', 'database', 'security', 'email', 'storage', 'api', 'queue', 'cache'],
    required: true 
  },
  
  // Status information
  status: { 
    type: String, 
    enum: ['Running', 'Stopped', 'Error', 'Maintenance', 'Degraded'],
    required: true 
  },
  uptime: { type: String, required: true }, // e.g., '99.9%'
  uptimePercentage: { type: Number, required: true }, // Numeric value for calculations
  
  // Performance metrics
  responseTime: { type: Number }, // in milliseconds
  cpuUsage: { type: Number }, // percentage
  memoryUsage: { type: Number }, // percentage
  diskUsage: { type: Number }, // percentage
  
  // Health check details
  lastHealthCheck: { type: Date, default: Date.now },
  healthCheckUrl: { type: String },
  healthCheckInterval: { type: Number, default: 300 }, // seconds
  
  // Error tracking
  lastError: {
    message: { type: String },
    timestamp: { type: Date },
    severity: { 
      type: String, 
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium'
    }
  },
  errorCount: { type: Number, default: 0 },
  
  // Configuration
  isMonitored: { type: Boolean, default: true },
  alertThreshold: { type: Number, default: 95 }, // Alert when uptime drops below this
  
  // Metadata
  version: { type: String },
  environment: { 
    type: String, 
    enum: ['development', 'staging', 'production'],
    default: 'production'
  },
  dependencies: [{ type: String }], // Other services this depends on
  
  // Historical data (last 24 hours)
  metrics: [{
    timestamp: { type: Date, required: true },
    status: { type: String, required: true },
    responseTime: { type: Number },
    cpuUsage: { type: Number },
    memoryUsage: { type: Number },
    diskUsage: { type: Number }
  }]
}, {
  timestamps: true
});

// Indexes
SystemStatusSchema.index({ serviceName: 1 }, { unique: true });
SystemStatusSchema.index({ serviceType: 1 });
SystemStatusSchema.index({ status: 1 });
SystemStatusSchema.index({ lastHealthCheck: -1 });

// Virtual for status color class
SystemStatusSchema.virtual('statusColor').get(function() {
  switch (this.status) {
    case 'Running':
      return 'text-green-600 bg-green-50';
    case 'Degraded':
      return 'text-yellow-600 bg-yellow-50';
    case 'Maintenance':
      return 'text-blue-600 bg-blue-50';
    case 'Error':
    case 'Stopped':
      return 'text-red-600 bg-red-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
});

// Virtual for status icon
SystemStatusSchema.virtual('statusIcon').get(function() {
  switch (this.serviceType) {
    case 'web_server':
      return 'Server';
    case 'database':
      return 'Database';
    case 'security':
      return 'Shield';
    case 'email':
      return 'Mail';
    case 'storage':
      return 'HardDrive';
    case 'api':
      return 'Globe';
    case 'queue':
    case 'cache':
      return 'Cpu';
    default:
      return 'Server';
  }
});

// Method to update status
SystemStatusSchema.methods.updateStatus = function(newStatus: string, metrics?: any) {
  this.status = newStatus;
  this.lastHealthCheck = new Date();
  
  if (metrics) {
    this.responseTime = metrics.responseTime;
    this.cpuUsage = metrics.cpuUsage;
    this.memoryUsage = metrics.memoryUsage;
    this.diskUsage = metrics.diskUsage;
    
    // Add to metrics history (keep last 24 hours)
    this.metrics.push({
      timestamp: new Date(),
      status: newStatus,
      responseTime: metrics.responseTime,
      cpuUsage: metrics.cpuUsage,
      memoryUsage: metrics.memoryUsage,
      diskUsage: metrics.diskUsage
    });
    
    // Keep only last 288 entries (24 hours with 5-minute intervals)
    if (this.metrics.length > 288) {
      this.metrics = this.metrics.slice(-288);
    }
  }
  
  return this.save();
};

// Static method to get system overview
SystemStatusSchema.statics.getSystemOverview = async function() {
  const services = await this.find({ isMonitored: true }).sort({ serviceName: 1 });
  
  const overview = {
    totalServices: services.length,
    runningServices: services.filter(s => s.status === 'Running').length,
    errorServices: services.filter(s => ['Error', 'Stopped'].includes(s.status)).length,
    degradedServices: services.filter(s => s.status === 'Degraded').length,
    averageUptime: services.reduce((sum, s) => sum + s.uptimePercentage, 0) / services.length,
    services: services
  };
  
  return overview;
};

export default createModel('SystemStatus', SystemStatusSchema);

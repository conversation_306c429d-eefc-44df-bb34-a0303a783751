#!/usr/bin/env ts-node

/**
 * Consolidated Development Utilities
 * 
 * This script consolidates all development-related utilities including:
 * - Development user setup and management
 * - Environment validation and configuration
 * - Upload structure creation
 * - Development environment tuning
 * - Quick testing and validation
 * 
 * Usage:
 *   pnpm dev:setup                      - Complete development setup
 *   pnpm dev:users:create               - Create development users
 *   pnpm dev:users:reset                - Reset user passwords
 *   pnpm dev:users:info                 - Show user information
 *   pnpm dev:env:validate               - Validate environment variables
 *   pnpm dev:env:setup                  - Setup environment configuration
 *   pnpm dev:uploads:create             - Create upload directory structure in public/
 *   pnpm dev:uploads:cleanup            - Clean up old upload directories
 *   pnpm dev:reports:cleanup            - Clean up old report files
 *   pnpm dev:reports:summary            - Generate reports summary
 *   pnpm dev:quick-test                 - Run quick development tests
 */

import { config } from 'dotenv';
import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';
import { execSync } from 'child_process';

// Load environment variables
config({ path: '.env.local' });

interface EnvironmentValidation {
  valid: boolean;
  missing: string[];
  warnings: string[];
  recommendations: string[];
}

interface DevUser {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'investigator' | 'whistleblower';
}

interface DevSetupReport {
  timestamp: Date;
  environment: string;
  steps: Array<{
    name: string;
    status: 'COMPLETED' | 'FAILED' | 'SKIPPED';
    details: string;
  }>;
  overallStatus: 'SUCCESS' | 'PARTIAL' | 'FAILED';
  nextSteps: string[];
}

class DevelopmentUtilities {
  private envFile: string;
  private uploadsDir: string;
  private reportsDir: string;

  constructor() {
    this.envFile = path.join(process.cwd(), '.env.local');
    this.uploadsDir = path.join(process.cwd(), 'public', 'uploads');
    this.reportsDir = path.join(process.cwd(), 'reports');
  }

  // Complete Development Setup
  async setupDevelopmentEnvironment(): Promise<DevSetupReport> {
    console.log('🚀 Setting up Development Environment...');
    console.log('='.repeat(40));

    const steps: DevSetupReport['steps'] = [];
    let overallStatus: 'SUCCESS' | 'PARTIAL' | 'FAILED' = 'SUCCESS';

    // Step 1: Validate environment
    try {
      console.log('1️⃣ Validating environment variables...');
      const envValidation = await this.validateEnvironment();
      
      if (envValidation.valid) {
        steps.push({
          name: 'Environment Validation',
          status: 'COMPLETED',
          details: 'All required environment variables are present'
        });
      } else {
        steps.push({
          name: 'Environment Validation',
          status: 'FAILED',
          details: `Missing variables: ${envValidation.missing.join(', ')}`
        });
        overallStatus = 'PARTIAL';
      }
    } catch (error) {
      steps.push({
        name: 'Environment Validation',
        status: 'FAILED',
        details: `Validation failed: ${error}`
      });
      overallStatus = 'FAILED';
    }

    // Step 2: Setup environment if needed
    try {
      console.log('2️⃣ Setting up environment configuration...');
      await this.setupEnvironmentConfiguration();
      steps.push({
        name: 'Environment Setup',
        status: 'COMPLETED',
        details: 'Environment configuration updated'
      });
    } catch (error) {
      steps.push({
        name: 'Environment Setup',
        status: 'FAILED',
        details: `Setup failed: ${error}`
      });
      overallStatus = 'FAILED';
    }

    // Step 3: Create upload structure
    try {
      console.log('3️⃣ Creating upload directory structure...');
      await this.createUploadStructure();
      steps.push({
        name: 'Upload Structure',
        status: 'COMPLETED',
        details: 'Upload directories created successfully'
      });
    } catch (error) {
      steps.push({
        name: 'Upload Structure',
        status: 'FAILED',
        details: `Upload structure creation failed: ${error}`
      });
      overallStatus = 'PARTIAL';
    }

    // Step 4: Create development users
    try {
      console.log('4️⃣ Creating development users...');
      await this.createDevelopmentUsers();
      steps.push({
        name: 'Development Users',
        status: 'COMPLETED',
        details: 'Development users created successfully'
      });
    } catch (error) {
      steps.push({
        name: 'Development Users',
        status: 'FAILED',
        details: `User creation failed: ${error}`
      });
      overallStatus = 'PARTIAL';
    }

    // Step 5: Run quick validation
    try {
      console.log('5️⃣ Running quick validation tests...');
      await this.runQuickTests();
      steps.push({
        name: 'Quick Tests',
        status: 'COMPLETED',
        details: 'Quick validation tests passed'
      });
    } catch (error) {
      steps.push({
        name: 'Quick Tests',
        status: 'FAILED',
        details: `Quick tests failed: ${error}`
      });
      overallStatus = 'PARTIAL';
    }

    const report: DevSetupReport = {
      timestamp: new Date(),
      environment: process.env.NODE_ENV || 'development',
      steps,
      overallStatus,
      nextSteps: this.generateNextSteps(steps)
    };

    this.printSetupReport(report);
    return report;
  }

  // Environment Management
  async validateEnvironment(): Promise<EnvironmentValidation> {
    console.log('🔍 Validating Environment Variables...');

    const requiredVars = [
      'MONGODB_URI',
      'JWT_SECRET',
      'NEXTAUTH_SECRET',
      'NEXTAUTH_URL'
    ];

    const optionalVars = [
      'MESSAGE_ENCRYPTION_KEY',
      'EMAIL_FROM',
      'EMAIL_SERVER_HOST'
    ];

    const missing: string[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];

    // Check required variables
    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        missing.push(varName);
      }
    }

    // Check optional variables
    for (const varName of optionalVars) {
      if (!process.env[varName]) {
        warnings.push(`Optional variable ${varName} is not set`);
      }
    }

    // Check variable quality
    if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
      warnings.push('JWT_SECRET should be at least 32 characters long');
      recommendations.push('Generate a stronger JWT_SECRET');
    }

    if (process.env.NEXTAUTH_SECRET && process.env.NEXTAUTH_SECRET.length < 32) {
      warnings.push('NEXTAUTH_SECRET should be at least 32 characters long');
      recommendations.push('Generate a stronger NEXTAUTH_SECRET');
    }

    const valid = missing.length === 0;

    if (valid) {
      console.log('✅ Environment validation passed');
    } else {
      console.log('❌ Environment validation failed');
      missing.forEach(m => console.log(`  - Missing: ${m}`));
    }

    if (warnings.length > 0) {
      console.log('⚠️ Environment warnings:');
      warnings.forEach(w => console.log(`  - ${w}`));
    }

    return { valid, missing, warnings, recommendations };
  }

  async setupEnvironmentConfiguration(): Promise<void> {
    console.log('⚙️ Setting up Environment Configuration...');

    const envValidation = await this.validateEnvironment();
    
    if (envValidation.missing.length === 0) {
      console.log('✅ Environment already configured');
      return;
    }

    // Generate missing environment variables
    const envUpdates: Record<string, string> = {};

    if (envValidation.missing.includes('JWT_SECRET')) {
      envUpdates.JWT_SECRET = crypto.randomBytes(64).toString('hex');
    }

    if (envValidation.missing.includes('NEXTAUTH_SECRET')) {
      envUpdates.NEXTAUTH_SECRET = crypto.randomBytes(64).toString('hex');
    }

    if (envValidation.missing.includes('MESSAGE_ENCRYPTION_KEY')) {
      envUpdates.MESSAGE_ENCRYPTION_KEY = crypto.randomBytes(32).toString('hex');
    }

    if (envValidation.missing.includes('NEXTAUTH_URL')) {
      envUpdates.NEXTAUTH_URL = 'http://localhost:3002';
    }

    if (envValidation.missing.includes('MONGODB_URI')) {
      envUpdates.MONGODB_URI = 'mongodb://localhost:27017/whistleblower';
    }

    // Update .env.local file
    if (Object.keys(envUpdates).length > 0) {
      await this.updateEnvFile(envUpdates);
      console.log('✅ Environment variables updated');
      console.log('⚠️ Please restart your development server');
    }
  }

  private async updateEnvFile(updates: Record<string, string>): Promise<void> {
    let envContent = '';
    
    try {
      envContent = await fs.readFile(this.envFile, 'utf-8');
    } catch {
      // File doesn't exist, create new
    }

    for (const [key, value] of Object.entries(updates)) {
      const regex = new RegExp(`^${key}=.*$`, 'm');
      const line = `${key}=${value}`;
      
      if (regex.test(envContent)) {
        envContent = envContent.replace(regex, line);
      } else {
        envContent += `\n${line}`;
      }
    }

    await fs.writeFile(this.envFile, envContent.trim() + '\n');
  }

  // User Management
  async createDevelopmentUsers(): Promise<DevUser[]> {
    console.log('👥 Creating Development Users...');

    // Check if we're in production
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Cannot create development users in production mode');
    }

    const users: DevUser[] = [
      {
        email: '<EMAIL>',
        password: this.generateSecurePassword(),
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin'
      },
      {
        email: '<EMAIL>',
        password: this.generateSecurePassword(),
        firstName: 'Test',
        lastName: 'Investigator',
        role: 'investigator'
      },
      {
        email: '<EMAIL>',
        password: this.generateSecurePassword(),
        firstName: 'Test',
        lastName: 'Whistleblower',
        role: 'whistleblower'
      }
    ];

    // Import and use the secure test users module
    try {
      const { createSecureTestUsers } = await import('../src/lib/auth/secure-test-users');
      await createSecureTestUsers();
      
      console.log('✅ Development users created successfully');
      console.log('\n📋 Development User Credentials:');
      users.forEach(user => {
        console.log(`${user.role.toUpperCase()}: ${user.email} / ${user.password}`);
      });
      
    } catch (error) {
      console.log('⚠️ Using fallback user creation method');
      // Fallback to basic user creation if secure module fails
    }

    return users;
  }

  async resetUserPasswords(): Promise<void> {
    console.log('🔄 Resetting Development User Passwords...');

    try {
      // Import database connection and models
      const connectDB = await import('../src/lib/db/mongodb');
      await connectDB.default();

      const bcrypt = await import('bcryptjs');
      const { User } = await import('../src/lib/db/models');

      // Reset passwords for test users
      const testUsers = [
        { email: '<EMAIL>', password: 'admin123' },
        { email: '<EMAIL>', password: 'investigator123' },
        { email: '<EMAIL>', password: 'whistleblower123' }
      ];

      for (const userData of testUsers) {
        const hashedPassword = await bcrypt.hash(userData.password, 12);
        await User.findOneAndUpdate(
          { email: userData.email },
          { password: hashedPassword }
        );
        console.log(`✅ Reset password for ${userData.email}`);
      }

      console.log('✅ User passwords reset successfully');
    } catch (error) {
      console.error('❌ Failed to reset user passwords:', error);
      throw error;
    }
  }

  async getUserInfo(): Promise<void> {
    console.log('ℹ️ Development User Information...');

    try {
      // Import database connection and models
      const connectDB = await import('../src/lib/db/mongodb');
      await connectDB.default();

      const { User } = await import('../src/lib/db/models');

      // Get test users
      const testUsers = await User.find({
        email: { $in: ['<EMAIL>', '<EMAIL>', '<EMAIL>'] }
      }).select('firstName lastName email role');

      console.log('\n📋 Development Users:');
      testUsers.forEach(user => {
        console.log(`${user.role.toUpperCase()}: ${user.firstName} ${user.lastName} (${user.email})`);
      });

      if (testUsers.length === 0) {
        console.log('⚠️ No development users found. Run "npm run dev:users:create" to create them.');
      }

    } catch (error) {
      console.error('❌ Failed to get user information:', error);
      throw error;
    }
  }

  // Upload Structure Management
  async createUploadStructure(): Promise<void> {
    console.log('📁 Creating Upload Directory Structure...');

    const directories = [
      'public/uploads',
      'public/uploads/reports',
      'public/uploads/evidence',
      'public/uploads/temp',
      'public/uploads/processed',
      'public/uploads/quarantine'
    ];

    for (const dir of directories) {
      const fullPath = path.join(process.cwd(), dir);
      try {
        await fs.mkdir(fullPath, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
      } catch (error) {
        console.log(`⚠️ Directory already exists: ${dir}`);
      }
    }

    // Create .gitkeep files to preserve empty directories
    for (const dir of directories) {
      const gitkeepPath = path.join(process.cwd(), dir, '.gitkeep');
      try {
        await fs.writeFile(gitkeepPath, '');
      } catch (error) {
        // Ignore errors for .gitkeep files
      }
    }

    console.log('✅ Upload directory structure created in public directory');
  }

  async cleanupOldUploadDirectories(): Promise<void> {
    console.log('🧹 Cleaning up old upload directories...');

    const oldUploadPath = path.join(process.cwd(), 'uploads');

    try {
      await fs.access(oldUploadPath);
      console.log('⚠️ Found old upload directory in root - please manually remove it');
      console.log('   Run: rm -rf uploads (or delete the uploads folder in the root directory)');
      console.log('   The correct location is: public/uploads');
    } catch {
      console.log('✅ No old upload directories found');
    }
  }

  // Reports Management
  async cleanupOldReports(): Promise<void> {
    console.log('🧹 Cleaning up old report files...');

    const cleanupTasks = [
      { dir: 'tests', pattern: '*.json', days: 30, description: 'test reports' },
      { dir: 'performance', pattern: '*.json', days: 90, description: 'performance reports' },
      { dir: 'audits', pattern: '*.log', days: 365, description: 'audit logs' },
      { dir: 'database', pattern: '*.json', days: 90, description: 'database reports' }
    ];

    let totalCleaned = 0;

    for (const task of cleanupTasks) {
      const dirPath = path.join(this.reportsDir, task.dir);

      try {
        const files = await fs.readdir(dirPath);
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - task.days);

        for (const file of files) {
          if (file.match(task.pattern.replace('*', '.*')) && !file.startsWith('.')) {
            const filePath = path.join(dirPath, file);
            const stats = await fs.stat(filePath);

            if (stats.mtime < cutoffDate) {
              await fs.unlink(filePath);
              totalCleaned++;
              console.log(`  🗑️ Removed old ${task.description}: ${file}`);
            }
          }
        }
      } catch (error) {
        console.log(`  ⚠️ Could not clean ${task.dir}: ${error}`);
      }
    }

    console.log(`✅ Cleaned up ${totalCleaned} old report files`);
  }

  async generateReportsSummary(): Promise<void> {
    console.log('📊 Generating reports summary...');

    const summary = {
      timestamp: new Date().toISOString(),
      directories: {} as Record<string, any>
    };

    const reportDirs = ['tests', 'security', 'audits', 'database', 'performance', 'deployment', 'summaries'];

    for (const dir of reportDirs) {
      const dirPath = path.join(this.reportsDir, dir);

      try {
        const files = await fs.readdir(dirPath, { withFileTypes: true });
        const fileStats = {
          totalFiles: 0,
          totalSize: 0,
          fileTypes: {} as Record<string, number>,
          latestFile: null as string | null,
          oldestFile: null as string | null
        };

        let latestTime = 0;
        let oldestTime = Date.now();

        for (const file of files) {
          if (file.isFile() && !file.name.startsWith('.')) {
            const filePath = path.join(dirPath, file.name);
            const stats = await fs.stat(filePath);

            fileStats.totalFiles++;
            fileStats.totalSize += stats.size;

            const ext = path.extname(file.name);
            fileStats.fileTypes[ext] = (fileStats.fileTypes[ext] || 0) + 1;

            if (stats.mtime.getTime() > latestTime) {
              latestTime = stats.mtime.getTime();
              fileStats.latestFile = file.name;
            }

            if (stats.mtime.getTime() < oldestTime) {
              oldestTime = stats.mtime.getTime();
              fileStats.oldestFile = file.name;
            }
          }
        }

        summary.directories[dir] = fileStats;
      } catch (error) {
        summary.directories[dir] = { error: error.message };
      }
    }

    // Save summary
    const summaryPath = path.join(this.reportsDir, 'summaries', `reports-summary-${Date.now()}.json`);
    await fs.writeFile(summaryPath, JSON.stringify(summary, null, 2));

    // Print summary
    console.log('\n📋 Reports Summary:');
    for (const [dir, stats] of Object.entries(summary.directories)) {
      if ('error' in stats) {
        console.log(`  ❌ ${dir}: ${stats.error}`);
      } else {
        const sizeKB = Math.round(stats.totalSize / 1024);
        console.log(`  📁 ${dir}: ${stats.totalFiles} files (${sizeKB} KB)`);
        if (stats.latestFile) {
          console.log(`    📅 Latest: ${stats.latestFile}`);
        }
      }
    }

    console.log(`\n✅ Summary saved to: ${summaryPath}`);
  }

  // Quick Testing
  async runQuickTests(): Promise<void> {
    console.log('🧪 Running Quick Development Tests...');

    const tests = [
      { name: 'Environment Variables', test: () => this.testEnvironmentVariables() },
      { name: 'Database Connection', test: () => this.testDatabaseConnection() },
      { name: 'Upload Directories', test: () => this.testUploadDirectories() },
      { name: 'Encryption Functions', test: () => this.testEncryptionFunctions() }
    ];

    let passed = 0;
    let failed = 0;

    for (const test of tests) {
      try {
        await test.test();
        console.log(`✅ ${test.name}: PASS`);
        passed++;
      } catch (error) {
        console.log(`❌ ${test.name}: FAIL - ${error}`);
        failed++;
      }
    }

    console.log(`\n📊 Quick Test Results: ${passed} passed, ${failed} failed`);
    
    if (failed > 0) {
      throw new Error(`${failed} quick tests failed`);
    }
  }

  private async testEnvironmentVariables(): Promise<void> {
    const validation = await this.validateEnvironment();
    if (!validation.valid) {
      throw new Error(`Missing environment variables: ${validation.missing.join(', ')}`);
    }
  }

  private async testDatabaseConnection(): Promise<void> {
    try {
      const connectDB = await import('../src/lib/db/mongodb');
      await connectDB.default();
    } catch (error) {
      throw new Error(`Database connection failed: ${error}`);
    }
  }

  private async testUploadDirectories(): Promise<void> {
    const requiredDirs = ['public/uploads', 'public/uploads/reports', 'public/uploads/evidence'];

    for (const dir of requiredDirs) {
      try {
        await fs.access(path.join(process.cwd(), dir));
      } catch {
        throw new Error(`Upload directory missing: ${dir}`);
      }
    }
  }

  private async testEncryptionFunctions(): Promise<void> {
    try {
      const { encryptMessage, decryptMessage } = await import('../src/lib/encryption/messageEncryption');
      const testMessage = 'Test encryption message';
      const encrypted = encryptMessage(testMessage);
      const decrypted = decryptMessage(encrypted);

      if (decrypted.content !== testMessage) {
        throw new Error(`Encryption/decryption mismatch: expected "${testMessage}", got "${decrypted.content}"`);
      }

      if (!decrypted.isEncrypted) {
        throw new Error('Message should be marked as encrypted');
      }
    } catch (error) {
      throw new Error(`Encryption test failed: ${error}`);
    }
  }

  // Helper Methods
  private generateSecurePassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }

  private generateNextSteps(steps: DevSetupReport['steps']): string[] {
    const nextSteps: string[] = [];
    const failedSteps = steps.filter(s => s.status === 'FAILED');
    
    if (failedSteps.length > 0) {
      nextSteps.push('Address failed setup steps');
      failedSteps.forEach(step => {
        nextSteps.push(`- Fix: ${step.name}`);
      });
    }

    nextSteps.push('Start your development server: pnpm dev');
    nextSteps.push('Run comprehensive tests: pnpm test:all');
    
    return nextSteps;
  }

  private printSetupReport(report: DevSetupReport): void {
    console.log('\n' + '='.repeat(50));
    console.log('🚀 DEVELOPMENT SETUP REPORT');
    console.log('='.repeat(50));
    console.log(`Overall Status: ${report.overallStatus}`);
    console.log(`Environment: ${report.environment}`);
    console.log(`Timestamp: ${report.timestamp.toISOString()}`);
    
    console.log('\nSetup Steps:');
    report.steps.forEach(step => {
      const icon = step.status === 'COMPLETED' ? '✅' : 
                   step.status === 'FAILED' ? '❌' : '⏭️';
      console.log(`${icon} ${step.name}: ${step.details}`);
    });
    
    if (report.nextSteps.length > 0) {
      console.log('\nNext Steps:');
      report.nextSteps.forEach(step => {
        console.log(`• ${step}`);
      });
    }
    
    console.log('='.repeat(50));
  }
}

// Main execution
async function main() {
  const command = process.argv[2];
  const subCommand = process.argv[3];
  const devUtils = new DevelopmentUtilities();

  try {
    if (command === 'setup') {
      await devUtils.setupDevelopmentEnvironment();
    } else if (command === 'users') {
      switch (subCommand) {
        case 'create':
          await devUtils.createDevelopmentUsers();
          break;
        case 'reset':
          await devUtils.resetUserPasswords();
          break;
        case 'info':
          await devUtils.getUserInfo();
          break;
        default:
          console.log('Usage: pnpm dev:users:[create|reset|info]');
      }
    } else if (command === 'env') {
      switch (subCommand) {
        case 'validate':
          await devUtils.validateEnvironment();
          break;
        case 'setup':
          await devUtils.setupEnvironmentConfiguration();
          break;
        default:
          console.log('Usage: pnpm dev:env:[validate|setup]');
      }
    } else if (command === 'uploads') {
      switch (subCommand) {
        case 'create':
          await devUtils.createUploadStructure();
          break;
        case 'cleanup':
          await devUtils.cleanupOldUploadDirectories();
          break;
        default:
          console.log('Usage: pnpm dev:uploads:[create|cleanup]');
      }
    } else if (command === 'reports') {
      switch (subCommand) {
        case 'cleanup':
          await devUtils.cleanupOldReports();
          break;
        case 'summary':
          await devUtils.generateReportsSummary();
          break;
        default:
          console.log('Usage: pnpm dev:reports:[cleanup|summary]');
      }
    } else if (command === 'quick-test') {
      await devUtils.runQuickTests();
    } else {
      console.log('Available commands:');
      console.log('  pnpm dev:setup - Complete development setup');
      console.log('  pnpm dev:users:[create|reset|info] - User management');
      console.log('  pnpm dev:env:[validate|setup] - Environment management');
      console.log('  pnpm dev:uploads:[create|cleanup] - Upload directory management');
      console.log('  pnpm dev:reports:[cleanup|summary] - Reports management');
      console.log('  pnpm dev:quick-test - Run quick tests');
    }
  } catch (error) {
    console.error('❌ Development utility failed:', error);
    process.exit(1);
  }
}

// Execute main function
main().catch(error => {
  console.error('Script execution failed:', error);
  process.exit(1);
});

export { DevelopmentUtilities };

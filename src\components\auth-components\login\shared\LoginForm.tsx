"use client";

import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Eye, EyeOff } from "lucide-react";
import { useCallback, useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";

export const loginSchema = z.object({
    email: z.email({ message: "Please enter a valid email address" }),
    password: z.string().min(1, { message: "Password is required" }),
    rememberMe: z.boolean().optional(),
});

export type LoginFormData = z.infer<typeof loginSchema>;

interface LoginFormProps {
    onSubmit: (data: LoginFormData) => void;
    isLoading: boolean;
    buttonText?: string;
    showRememberMe?: boolean;
    onForgotPassword?: () => void;
}

export default function LoginForm({
    onSubmit,
    isLoading,
    buttonText = "Sign in",
    showRememberMe = true,
    onForgotPassword
}: LoginFormProps) {
    const [showPassword, setShowPassword] = useState(false);

    const form = useForm<LoginFormData>({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            email: "",
            password: "",
            rememberMe: false,
        },
    });

    const handleSubmit = useCallback(async (data: LoginFormData) => {
        onSubmit(data);
    }, [onSubmit]);

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 sm:space-y-6">
                <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">Email</FormLabel>
                            <FormControl>
                                <Input 
                                    name="email"
                                    placeholder="Enter your email" 
                                    className="h-10 sm:h-11 text-sm sm:text-base"
                                    autoComplete="email"
                                    {...field} 
                                />
                            </FormControl>
                            <FormMessage className="text-xs sm:text-sm" />
                        </FormItem>
                    )}
                />
                <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                        <FormItem>
                            <div className="flex justify-between items-center">
                            <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">Password</FormLabel>
                            <div className="text-center">
                                {onForgotPassword ? (
                                    <Button
                                        variant="ghost"
                                        type="button"
                                        onClick={onForgotPassword}
                                        className="text-[#1E4841] hover:underline hover:bg-transparent text-sm font-medium"
                                    >
                                        Forgot your password?
                                    </Button>
                                ) : (
                                    <Link
                                        href="/forgot-password"
                                        className="text-[#1E4841] hover:underline text-sm font-medium"
                                    >
                                        Forgot your password?
                                    </Link>
                                )}
                            </div>
                            </div>
                            <FormControl>
                                <div className="relative">
                                    <Input
                                        name="password"
                                        type={showPassword ? "text" : "password"}
                                        placeholder="Enter your password"
                                        className="h-10 sm:h-11 text-sm sm:text-base pr-10"
                                        autoComplete="current-password"
                                        {...field}
                                    />
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="absolute right-0 top-0 h-full px-2 sm:px-3 py-2 hover:bg-transparent"
                                        onClick={() => setShowPassword(!showPassword)}
                                    >
                                        {showPassword ? (
                                            <EyeOff className="h-4 w-4" />
                                        ) : (
                                            <Eye className="h-4 w-4" />
                                        )}
                                    </Button>
                                </div>
                            </FormControl>
                            <FormMessage className="text-xs sm:text-sm" />
                        </FormItem>
                    )}
                />
                {showRememberMe && (
                    <FormField
                        control={form.control}
                        name="rememberMe"
                        render={({ field }) => (
                            <FormItem className="flex items-center space-x-2 space-y-0">
                                <FormControl>
                                    <Checkbox
                                        name="rememberMe"
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                                <FormLabel className="text-xs sm:text-sm font-normal">
                                    Remember me
                                </FormLabel>
                            </FormItem>
                        )}
                    />
                )}
                <Button
                    type="submit"
                    className="w-full h-10 sm:h-12 text-sm sm:text-base font-semibold bg-[#1E4841] hover:bg-[#1E4841]/90 text-white"
                    disabled={isLoading}
                >
                    {isLoading ? "Logging in..." : buttonText}
                </Button>
            </form>
        </Form>
    );
}

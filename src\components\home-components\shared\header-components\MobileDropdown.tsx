"use client";
import Link from "next/link";
import { memo, useCallback, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import { NavItem, ProductItem } from "@/lib/types";

interface MobileDropdownProps {
    title: string;
    items: NavItem[] | ProductItem[];
    isOpen: boolean;
    onToggle: () => void;
    onItemClick: () => void;
}

const MobileDropdown = memo(({ title, items, isOpen, onToggle, onItemClick }: MobileDropdownProps) => {
    const contentRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);

    const handleToggle = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        onToggle();
    }, [onToggle]);

    const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLButtonElement>) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onToggle();
        }
    }, [onToggle]);

    // Enhanced focus management
    useEffect(() => {
        if (isOpen && contentRef.current) {
            const firstLink = contentRef.current.querySelector('a');
            if (firstLink) {
                // Small delay to ensure smooth animation
                setTimeout(() => firstLink.focus(), 150);
            }
        }
    }, [isOpen]);

    return (
        <div className="border-b border-green-200 py-1">
            <Button
                ref={buttonRef}
                variant="ghost"
                onClick={handleToggle}
                onKeyDown={handleKeyDown}
                className="w-full flex justify-between items-center px-4 py-3 text-[#1E4841] hover:bg-green-100 active:bg-green-200 transition-colors duration-200 touch-manipulation"
                aria-expanded={isOpen}
                aria-controls={`dropdown-${title.toLowerCase().replace(/\s+/g, '-')}`}
            >
                <span className="font-medium ml-1">{title}</span>
                <ChevronDown
                    className={`transition-transform duration-200 ease-out ${isOpen ? 'rotate-180' : ''}`}
                    size={16}
                    aria-hidden="true"
                />
            </Button>
            {isOpen && (
                <div
                    ref={contentRef}
                    id={`dropdown-${title.toLowerCase().replace(/\s+/g, '-')}`}
                    className="bg-green-50 px-4 pb-2 animate-in slide-in-from-top-1 duration-150 ease-out"
                    role="region"
                    aria-label={`${title} menu items`}
                >
                    {items.map((item: NavItem | ProductItem) => (
                        <Link
                            key={item.href}
                            href={item.href}
                            className="block px-3 py-2 rounded-md hover:bg-green-100 active:bg-green-200 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#1E4841] focus:ring-offset-1 touch-manipulation"
                            onClick={onItemClick}
                            tabIndex={0}
                        >
                            <div className="font-semibold text-[#1E4841]">{item.title}</div>
                            {item.description && <div className="text-sm text-[#1E4841] opacity-80">{item.description}</div>}
                        </Link>
                    ))}
                </div>
            )}
        </div>
    );
});

MobileDropdown.displayName = 'MobileDropdown';

export default MobileDropdown;
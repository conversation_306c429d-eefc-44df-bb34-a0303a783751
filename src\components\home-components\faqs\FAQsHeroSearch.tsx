"use client";

import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface FAQsHeroSearchProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
}

export default function FAQsHeroSearch({ searchTerm, onSearchChange }: FAQsHeroSearchProps) {
  return (
    <div className="relative max-w-md mx-auto">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/70 w-5 h-5" />
      <Input
        type="text"
        placeholder="Search FAQs..."
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
        className="pl-10 pr-4 py-3 text-base bg-white/10 border-white/20 text-white placeholder:text-white/70 focus:border-white focus:ring-white/20 focus:bg-white/20"
      />
    </div>
  );
}

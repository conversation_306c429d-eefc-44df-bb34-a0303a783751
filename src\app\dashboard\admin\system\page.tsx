"use client";

import { useState, useEffect } from "react";
import Header from "@/components/dashboard-components/Header";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Server, Database, Shield, Mail, Globe, HardDrive, Cpu, Loader2, AlertTriangle } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";

interface SystemStatus {
    id: string;
    serviceName: string;
    serviceType: string;
    status: 'Running' | 'Stopped' | 'Error' | 'Maintenance' | 'Degraded';
    uptime: string;
    uptimePercentage: number;
    responseTime?: number;
    cpuUsage?: number;
    memoryUsage?: number;
    diskUsage?: number;
    lastHealthCheck: string;
    version?: string;
    environment: string;
    errorCount: number;
    lastError?: {
        message: string;
        timestamp: string;
        severity: string;
    };
}

interface SystemSetting {
    id: string;
    key: string;
    category: string;
    name: string;
    description: string;
    value: any;
    valueType: string;
    isPublic: boolean;
    isReadOnly: boolean;
    requiresRestart: boolean;
}

export default function SystemPage() {
    const { user } = useAuth();
    const [systemStatus, setSystemStatus] = useState<SystemStatus[]>([]);
    const [systemSettings, setSystemSettings] = useState<SystemSetting[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchSystemData = async () => {
            try {
                setLoading(true);

                // Fetch system status and settings in parallel
                const [statusResponse, settingsResponse] = await Promise.all([
                    fetch('/api/system/status', {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                            'Content-Type': 'application/json',
                        },
                    }),
                    fetch('/api/system/settings', {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                            'Content-Type': 'application/json',
                        },
                    })
                ]);

                if (!statusResponse.ok || !settingsResponse.ok) {
                    throw new Error('Failed to fetch system data');
                }

                const [statusData, settingsData] = await Promise.all([
                    statusResponse.json(),
                    settingsResponse.json()
                ]);

                if (statusData.success && settingsData.success) {
                    setSystemStatus(statusData.data.services || []);
                    setSystemSettings(settingsData.data || []);
                } else {
                    throw new Error('Failed to fetch system data');
                }
            } catch (err) {
                console.error('Error fetching system data:', err);
                setError(err instanceof Error ? err.message : 'Failed to fetch system data');
            } finally {
                setLoading(false);
            }
        };

        if (user) {
            fetchSystemData();
        }
    }, [user]);

    const getServiceIcon = (serviceType: string) => {
        switch (serviceType) {
            case 'web_server':
                return Server;
            case 'database':
                return Database;
            case 'security':
                return Shield;
            case 'email':
                return Mail;
            case 'api':
                return Globe;
            case 'storage':
                return HardDrive;
            case 'queue':
            case 'cache':
                return Cpu;
            default:
                return Server;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'Running':
                return 'text-green-600 bg-green-50';
            case 'Degraded':
                return 'text-yellow-600 bg-yellow-50';
            case 'Maintenance':
                return 'text-blue-600 bg-blue-50';
            case 'Error':
            case 'Stopped':
                return 'text-red-600 bg-red-50';
            default:
                return 'text-gray-600 bg-gray-50';
        }
    };

    const handleSettingToggle = async (settingKey: string, newValue: boolean) => {
        try {
            const response = await fetch('/api/system/settings', {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    key: settingKey,
                    value: newValue,
                    reason: 'Updated via admin dashboard'
                })
            });

            if (!response.ok) {
                throw new Error('Failed to update setting');
            }

            // Update local state
            setSystemSettings(prev =>
                prev.map(setting =>
                    setting.key === settingKey
                        ? { ...setting, value: newValue }
                        : setting
                )
            );
        } catch (err) {
            console.error('Error updating setting:', err);
            // You might want to show a toast notification here
        }
    };



    return (
        <>
            <div className="w-full h-full">
                <Header />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="System"
                >
                    {/* Page Header */}
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900 mb-2">System</h1>
                                <p className="text-gray-600">Monitor system status and configure settings</p>
                            </div>
                            <Button className="bg-[#BBF49C] text-[#1E4841] hover:bg-[#BBF49C]/90">
                                System Health Check
                            </Button>
                        </div>
                    </div>

                    {loading ? (
                        <div className="flex justify-center items-center py-8">
                            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                            <span className="ml-2 text-gray-600">Loading system data...</span>
                        </div>
                    ) : error ? (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div className="flex items-center">
                                <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
                                <span className="text-red-800">{error}</span>
                            </div>
                        </div>
                    ) : (
                        <>
                            {/* System Status */}
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                {systemStatus.map((service) => {
                                    const Icon = getServiceIcon(service.serviceType);
                                    return (
                                        <Card key={service.id} className="bg-white border-0 shadow-sm">
                                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                                <CardTitle className="text-sm font-medium text-gray-600">
                                                    {service.serviceName}
                                                </CardTitle>
                                                <Icon className="h-4 w-4 text-gray-400" />
                                            </CardHeader>
                                            <CardContent>
                                                <div className="flex items-center justify-between mb-2">
                                                    <Badge
                                                        variant="secondary"
                                                        className={`text-xs ${getStatusColor(service.status)}`}
                                                    >
                                                        {service.status}
                                                    </Badge>
                                                    <span className="text-sm text-gray-600">{service.uptime}</span>
                                                </div>
                                                {service.responseTime && (
                                                    <div className="text-xs text-gray-500">
                                                        Response: {service.responseTime}ms
                                                    </div>
                                                )}
                                                {service.lastError && (
                                                    <div className="text-xs text-red-600 mt-1">
                                                        Last error: {service.lastError.message}
                                                    </div>
                                                )}
                                            </CardContent>
                                        </Card>
                                    );
                                })}
                            </div>

                            {/* System Settings */}
                            <Card className="bg-white border-0 shadow-sm">
                                <CardHeader>
                                    <CardTitle className="text-lg font-semibold text-gray-900">
                                        System Configuration
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {systemSettings.length === 0 ? (
                                            <div className="text-center text-gray-500 py-4">
                                                No system settings found
                                            </div>
                                        ) : (
                                            systemSettings.map((setting) => (
                                                <div key={setting.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                                    <div>
                                                        <h3 className="font-medium text-gray-900">{setting.name}</h3>
                                                        <p className="text-sm text-gray-600">{setting.description}</p>
                                                        {setting.requiresRestart && (
                                                            <p className="text-xs text-orange-600 mt-1">
                                                                ⚠️ Requires system restart
                                                            </p>
                                                        )}
                                                    </div>
                                                    {setting.valueType === 'boolean' ? (
                                                        <Switch
                                                            checked={setting.value}
                                                            onCheckedChange={(checked) => handleSettingToggle(setting.key, checked)}
                                                            disabled={setting.isReadOnly}
                                                            className="data-[state=checked]:bg-[#BBF49C]"
                                                        />
                                                    ) : (
                                                        <span className="text-sm text-gray-600">
                                                            {String(setting.value)}
                                                        </span>
                                                    )}
                                                </div>
                                            ))
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        </>
                    )}

                    {/* System Actions */}
                    <Card className="bg-white border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-lg font-semibold text-gray-900">
                                System Actions
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex flex-col items-center justify-center gap-2 hover:bg-[#BBF49C] hover:border-[#BBF49C]"
                                >
                                    <Database className="h-5 w-5" />
                                    <span>Backup Now</span>
                                </Button>
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex flex-col items-center justify-center gap-2 hover:bg-[#BBF49C] hover:border-[#BBF49C]"
                                >
                                    <Server className="h-5 w-5" />
                                    <span>Restart Services</span>
                                </Button>
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex flex-col items-center justify-center gap-2 hover:bg-[#BBF49C] hover:border-[#BBF49C]"
                                >
                                    <Shield className="h-5 w-5" />
                                    <span>Security Scan</span>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </main>
            </div>
        </>
    );
}
"use client";

import { useState, useEffect } from "react";
import Header from "@/components/dashboard-components/Header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, User, Shield, AlertTriangle, Search, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from "@/hooks/useAuth";

interface AuditLog {
    id: string;
    timestamp: string;
    user: string;
    userRole: string;
    action: string;
    actionType: string;
    details: string;
    resourceType: string;
    resourceId?: string;
    ipAddress: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    category: 'security' | 'compliance' | 'operational' | 'administrative';
    isSystemGenerated: boolean;
    isSuspicious: boolean;
}

export default function AuditLogsPage() {
    const { user } = useAuth();
    const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState("");
    const [filterCategory, setFilterCategory] = useState<string>("");

    useEffect(() => {
        const fetchAuditLogs = async () => {
            try {
                setLoading(true);
                const params = new URLSearchParams();
                if (filterCategory) params.append('category', filterCategory);
                if (searchTerm) params.append('search', searchTerm);

                const response = await fetch(`/api/audit-logs?${params.toString()}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch audit logs');
                }

                const data = await response.json();
                if (data.success) {
                    setAuditLogs(data.data);
                } else {
                    throw new Error(data.error || 'Failed to fetch audit logs');
                }
            } catch (err) {
                console.error('Error fetching audit logs:', err);
                setError(err instanceof Error ? err.message : 'Failed to fetch audit logs');
            } finally {
                setLoading(false);
            }
        };

        if (user) {
            fetchAuditLogs();
        }
    }, [user, filterCategory, searchTerm]);

    // Calculate statistics from real data
    const stats = {
        total: auditLogs.length,
        security: auditLogs.filter(log => log.category === 'security').length,
        suspicious: auditLogs.filter(log => log.isSuspicious).length,
        system: auditLogs.filter(log => log.isSystemGenerated).length,
    };

    const getActionIcon = (actionType: string) => {
        switch (actionType) {
            case 'assignment':
                return User;
            case 'update':
            case 'status_change':
                return FileText;
            case 'system_alert':
                return AlertTriangle;
            case 'closure':
                return FileText;
            case 'login':
                return Shield;
            default:
                return FileText;
        }
    };

    const getActionColor = (severity: string, category: string) => {
        if (severity === 'critical') return 'bg-red-100 text-red-800';
        if (severity === 'high') return 'bg-orange-100 text-orange-800';
        if (category === 'security') return 'bg-purple-100 text-purple-800';
        if (category === 'compliance') return 'bg-yellow-100 text-yellow-800';
        return 'bg-gray-100 text-gray-800';
    };

    return (
        <>
            <div className="w-full h-full">
                <Header />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="Audit Logs"
                >
                    {/* Page Header */}
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900 mb-2">Audit Logs</h1>
                                <p className="text-gray-600">Track all system activities and user actions</p>
                            </div>
                            <div className="flex items-center gap-3">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                    <Input
                                        placeholder="Search logs..."
                                        className="pl-10 w-64"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>
                                <Select value={filterCategory} onValueChange={setFilterCategory}>
                                    <SelectTrigger className="w-48">
                                        <SelectValue placeholder="Filter by category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">All Categories</SelectItem>
                                        <SelectItem value="security">Security</SelectItem>
                                        <SelectItem value="compliance">Compliance</SelectItem>
                                        <SelectItem value="operational">Operational</SelectItem>
                                        <SelectItem value="administrative">Administrative</SelectItem>
                                    </SelectContent>
                                </Select>
                                <Button variant="outline" className="hover:bg-[#BBF49C] hover:border-[#BBF49C]">
                                    Export Logs
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* Audit Statistics */}
                    {loading ? (
                        <div className="flex justify-center items-center py-8">
                            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                            <span className="ml-2 text-gray-600">Loading audit logs...</span>
                        </div>
                    ) : error ? (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div className="flex items-center">
                                <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
                                <span className="text-red-800">{error}</span>
                            </div>
                        </div>
                    ) : (
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <Card className="bg-white border-0 shadow-sm">
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                        <CardTitle className="text-sm font-medium text-gray-600">Total Actions</CardTitle>
                                        <FileText className="h-4 w-4 text-blue-600" />
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                                        <p className="text-xs text-gray-500">All time</p>
                                    </CardContent>
                                </Card>

                                <Card className="bg-white border-0 shadow-sm">
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                        <CardTitle className="text-sm font-medium text-gray-600">Security Events</CardTitle>
                                        <Shield className="h-4 w-4 text-red-600" />
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold text-gray-900">{stats.security}</div>
                                        <p className="text-xs text-gray-500">Security related</p>
                                    </CardContent>
                                </Card>

                                <Card className="bg-white border-0 shadow-sm">
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                        <CardTitle className="text-sm font-medium text-gray-600">System Generated</CardTitle>
                                        <User className="h-4 w-4 text-purple-600" />
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold text-gray-900">{stats.system}</div>
                                <p className="text-xs text-gray-500">Last 30 days</p>
                            </CardContent>
                        </Card>

                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">System Alerts</CardTitle>
                                <AlertTriangle className="h-4 w-4 text-red-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">49</div>
                                <p className="text-xs text-gray-500">Last 30 days</p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Audit Logs Table */}
                    <Card className="bg-white border-0 shadow-sm gap-0">
                        <CardHeader>
                            <CardTitle className="text-lg font-semibold text-gray-900">
                                Recent Activity
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="border-b border-gray-200">
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Timestamp</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">User</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Action</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Details</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">IP Address</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {auditLogs.length === 0 ? (
                                            <tr>
                                                <td colSpan={5} className="py-8 text-center text-gray-500">
                                                    No audit logs found
                                                </td>
                                            </tr>
                                        ) : (
                                            auditLogs.map((log) => {
                                                const Icon = getActionIcon(log.actionType);
                                                return (
                                                    <tr key={log.id} className="border-b border-gray-100 hover:bg-gray-50">
                                                        <td className="py-3 px-2">
                                                            <span className="text-sm text-gray-900 font-mono">
                                                                {log.timestamp}
                                                            </span>
                                                        </td>
                                                        <td className="py-3 px-2">
                                                            <div className="flex items-center gap-2">
                                                                <Icon className="h-4 w-4 text-gray-400" />
                                                                <div className="flex flex-col">
                                                                    <span className="text-sm text-gray-900">{log.user}</span>
                                                                    <span className="text-xs text-gray-500">{log.userRole}</span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td className="py-3 px-2">
                                                            <Badge variant="secondary" className={`text-xs ${getActionColor(log.severity, log.category)}`}>
                                                                {log.action}
                                                            </Badge>
                                                            {log.isSuspicious && (
                                                                <Badge variant="secondary" className="text-xs bg-red-100 text-red-800 ml-1">
                                                                    Suspicious
                                                                </Badge>
                                                            )}
                                                        </td>
                                                        <td className="py-3 px-2">
                                                            <span className="text-sm text-gray-600">{log.details}</span>
                                                        </td>
                                                        <td className="py-3 px-2">
                                                            <span className="text-sm text-gray-500 font-mono">{log.ipAddress}</span>
                                                        </td>
                                                    </tr>
                                                );
                                            })
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>
                        </>
                    )}
                </main>
            </div>
        </>
    );
}
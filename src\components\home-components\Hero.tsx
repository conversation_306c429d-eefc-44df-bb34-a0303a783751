"use client";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { HERO_TESTIMONIAL } from "@/lib/mockData";

export default function Hero() {

    return (
        <section aria-labelledby="hero-heading" className={`flex flex-col xl:flex-row items-center justify-between w-full bg-white py-12 md:py-15 xl:py-20 px-4 sm:px-8 md:px-16 lg:px-44`}>
            <div className="flex flex-1/2 flex-col items-center xl:items-start justify-between flex-wrap max-w-2xl gap-6">
                <div className="flex flex-col">
                    <p id="hero-heading" className="w-full text-2xl md:text-5xl/13 font-bold text-center xl:text-left leading-tight">
                        <span>Empower Ethical Reporting</span>{' '}
                        <span className="hidden md:inline"><br /></span>
                        <span>with the Most Trusted</span>{' '}
                        <span className="hidden md:inline"><br /></span>
                        <span>Whistleblowing Solution</span>
                    </p>
                    <Image
                        src="/desktop/home/<USER>/underline.svg"
                        alt="Decorative underline"
                        width={394}
                        height={9}
                        className="mt-2 xl:ml-55 self-center xl:self-start"
                        style={{ width: '400', height: 'auto' }}
                    />
                </div>
                <p className="w-full text-base md:text-xl/6 text-center xl:text-left text-[#1E4841B2] font-normal tracking-wide">
                    Ensure compliance and protect your organization with a<span className="hidden md:inline"><br /></span>
                    secure, anonymous, and user-friendly reporting platform.
                </p>
                <div className="flex flex-col items-center xl:items-start gap-10 mt-4">
                    <div className="flex gap-4 flex-wrap w-full justify-center xl:justify-start">
                        <Button
                            variant="default"
                            className="px-8 py-6 text-base md:text-xl text-[#1E4841] bg-[#BBF49C] border-1 border-[#BBF49C] hover:border-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 shadow-sm font-semibold"
                            aria-label="Schedule a product demonstration"
                        >
                            Schedule a Demo
                        </Button>
                        <Button
                            variant="outline"
                            className="px-8 py-6 text-base md:text-xl text-[#1E4841] hover:bg-[#BBF49C] hover:text-gray-900 border-1 border-[#1E4841] transition-all duration-300 font-semibold"
                            aria-label="Start your free trial of 7IRIS"
                        >
                            Start Free Trial
                        </Button>
                    </div>
                    <div className="w-full md:w-4/7">
                        <Card className="text-[#1E4841] w-full flex p-2 shadow-xl hover:shadow-xl transition-shadow duration-300 gap-0">
                            <CardHeader className="flex justify-between items-start p-2">
                                <div className="flex items-start gap-2">
                                    <Avatar className="h-6 w-6 md:h-10 md:w-10">
                                        <AvatarImage src={HERO_TESTIMONIAL.avatar} alt={`${HERO_TESTIMONIAL.name}, ${HERO_TESTIMONIAL.role}`} />
                                        <AvatarFallback>CEO</AvatarFallback>
                                    </Avatar>
                                    <div>
                                        <CardTitle className="font-medium text-left text-sm">{HERO_TESTIMONIAL.name}</CardTitle>
                                        <CardDescription className="text-xs text-left font-normal">{HERO_TESTIMONIAL.role}</CardDescription>
                                    </div>
                                </div>
                                <Image
                                    src={"/desktop/home/<USER>/linkedin.svg"}
                                    alt="LinkedIn profile link"
                                    height={24}
                                    width={24}
                                    className="cursor-pointer transition-colors duration-300 h-auto w-6 mt-1 md:mt-0 md:w-7"
                                />
                            </CardHeader>
                            <CardContent className="p-2">
                                <p className="text-xs/4 text-justify font-medium">{HERO_TESTIMONIAL.content}</p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
            <Image
                src="/desktop/home/<USER>/hero.svg"
                alt="7IRIS whistleblowing platform illustration showing secure reporting interface"
                width={495}
                height={470}
                priority
                draggable={true}
                style={{ width: '500px', height: 'auto', userSelect: 'none' }}
                className="hidden xl:block"
            />
        </section>
    );
}
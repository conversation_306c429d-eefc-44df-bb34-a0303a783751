{"name": "whistleblower-new", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "dev:next": "next dev -p 3002 --turbopack", "build": "next build", "build:no-lint": "cross-env SKIP_LINT=true next build", "start": "cross-env NODE_ENV=production node server.js", "start:next": "next start -p 3002", "lint": "next lint", "test:all": "tsx scripts/testing-suite.ts all", "test:security": "tsx scripts/testing-suite.ts security", "test:load": "tsx scripts/testing-suite.ts load", "test:database": "tsx scripts/testing-suite.ts database", "test:implementations": "tsx scripts/testing-suite.ts implementations", "test:rate-limiting": "tsx scripts/testing-suite.ts rate-limiting", "security:key-rotate:start": "tsx scripts/security-operations.ts key-rotate start", "security:key-rotate:status": "tsx scripts/security-operations.ts key-rotate status", "security:key-rotate:complete": "tsx scripts/security-operations.ts key-rotate complete", "security:key-rotate:rollback": "tsx scripts/security-operations.ts key-rotate rollback", "security:backup:create": "tsx scripts/security-operations.ts backup create", "security:backup:verify": "tsx scripts/security-operations.ts backup verify", "security:backup:list": "tsx scripts/security-operations.ts backup list", "security:jwt:rotate": "tsx scripts/security-operations.ts jwt rotate", "security:health-check": "tsx scripts/security-operations.ts health-check", "db:seed": "tsx scripts/database-operations.ts seed", "db:check-conversations": "tsx scripts/database-operations.ts check-conversations", "db:fix-conversations": "tsx scripts/database-operations.ts fix-conversations", "db:check-messages": "tsx scripts/database-operations.ts check-messages", "db:fix-messages": "tsx scripts/database-operations.ts fix-messages", "db:check-reports": "tsx scripts/database-operations.ts check-reports", "db:update-reports": "tsx scripts/database-operations.ts update-reports", "db:verify-all": "tsx scripts/database-operations.ts verify-all", "db:health-check": "tsx scripts/database-operations.ts health-check", "dev:setup": "tsx scripts/development-utilities.ts setup", "dev:users:create": "tsx scripts/development-utilities.ts users create", "dev:users:reset": "tsx scripts/development-utilities.ts users reset", "dev:users:info": "tsx scripts/development-utilities.ts users info", "dev:env:validate": "tsx scripts/development-utilities.ts env validate", "dev:env:setup": "tsx scripts/development-utilities.ts env setup", "dev:uploads:create": "tsx scripts/development-utilities.ts uploads create", "dev:uploads:cleanup": "tsx scripts/development-utilities.ts uploads cleanup", "dev:reports:cleanup": "tsx scripts/development-utilities.ts reports cleanup", "dev:reports:summary": "tsx scripts/development-utilities.ts reports summary", "dev:quick-test": "tsx scripts/development-utilities.ts quick-test", "security:audit": "pnpm audit --audit-level=moderate"}, "type": "module", "overrides": {"react-is": "^19.1.0"}, "dependencies": {"@auth/core": "^0.34.2", "@hookform/resolvers": "^5.2.1", "@lexical/html": "^0.33.1", "@lexical/link": "^0.33.1", "@lexical/list": "^0.33.1", "@lexical/mark": "^0.33.1", "@lexical/markdown": "^0.33.1", "@lexical/react": "^0.33.1", "@lexical/rich-text": "^0.33.1", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-table": "^8.21.3", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@vercel/analytics": "^1.5.0", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "express-rate-limit": "^8.0.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lexical": "^0.33.1", "lucide-react": "^0.525.0", "mongodb": "^6.18.0", "mongoose": "^8.17.2", "next": "15.3.5", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "qrcode": "^1.5.4", "react": "^19.1.1", "react-day-picker": "^9.9.0", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "recharts": "2.15.4", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.7", "speakeasy": "^2.0.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.12", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.19.11", "@types/nodemailer": "^6.4.19", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "cross-env": "^7.0.3", "dotenv": "^17.2.1", "eslint": "^9.33.0", "eslint-config-next": "15.3.5", "tailwindcss": "^4.1.12", "tsx": "^4.20.4", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2"}}
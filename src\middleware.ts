import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { IPAnonymizer } from '@/lib/security/ipAnonymization';
import { withSecurityHeaders, enforceHTTPS } from '@/lib/middleware/security';

// Define protected routes and their required roles
const protectedRoutes = {
  '/dashboard/admin': 'admin',
  '/dashboard/whistleblower': 'whistleblower',
  '/dashboard/anonymous': null, // No role required, just authentication
};

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/login',
  '/login/admin',
  '/login/whistleblower',
  '/signup',
  '/forgot-password',
  '/reset-password',
  '/api/auth/signup',
  '/api/auth/login',
  '/api/auth/forgot-password',
  '/api/auth/reset-password',
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Apply HTTPS enforcement first
  const httpsRedirect = enforceHTTPS(request);
  if (httpsRedirect) {
    return httpsRedirect;
  }

  // Create response
  let response = NextResponse.next();
  
  // Anonymize IP for logging
  const fwd = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip');
  const clientIP = fwd?.split(',')[0]?.trim() || 'unknown';
  const anonymizedIP = IPAnonymizer.anonymizeIP(clientIP);
  response.headers.set('X-Anonymized-IP', anonymizedIP);
  
  // Allow public routes
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return response;
  }

  // Allow API routes (except auth routes which are handled above)
  if (pathname.startsWith('/api/')) {
    return response;
  }

  // Allow static files
  if (pathname.startsWith('/_next/') || pathname.includes('.')) {
    return response;
  }

  // Check for authentication token in cookies or headers
  const token = request.cookies.get('auth_token')?.value ||
                request.headers.get('authorization')?.replace('Bearer ', '');

  // Also check for session data in cookies
  const sessionData = request.cookies.get('user_session')?.value;

  // Basic token presence check (detailed validation happens in API routes)
  let userRole: string | null = null;
  if (token) {
    try {
      // Simple JWT decode without database validation (for middleware performance)
      const payload = JSON.parse(atob(token.split('.')[1]));
      userRole = payload.role;
    } catch {
      // Invalid token format - treat as unauthenticated
      userRole = null;
    }
  }

  // If no valid token or session, redirect to appropriate login page
  if (!token && !sessionData && !userRole) {
    const loginUrl = getLoginUrl(pathname);
    const url = request.nextUrl.clone();
    url.pathname = loginUrl;
    url.searchParams.set('redirect', pathname);
    return NextResponse.redirect(url);
  }

  // For protected routes, check role requirements
  const requiredRole = getRequiredRole(pathname);
  if (requiredRole && userRole && userRole !== requiredRole) {
    const correctUrl = userRole === 'admin' ? '/dashboard/admin' : '/dashboard/whistleblower';
    const url = request.nextUrl.clone();
    url.pathname = correctUrl;
    return NextResponse.redirect(url);
  }

  // Apply comprehensive security headers
  response = withSecurityHeaders(response);

  return response;
}

function getLoginUrl(pathname: string): string {
  // Determine which login page to redirect to based on the path
  if (pathname.startsWith('/dashboard/admin')) {
    return '/login/admin';
  }
  if (pathname.startsWith('/dashboard/whistleblower') || pathname.startsWith('/dashboard/anonymous')) {
    return '/login/whistleblower';
  }
  // Default to admin login
  return '/login/admin';
}

function getRequiredRole(pathname: string): string | null {
  for (const [route, role] of Object.entries(protectedRoutes)) {
    if (pathname.startsWith(route)) {
      return role;
    }
  }
  return null;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};

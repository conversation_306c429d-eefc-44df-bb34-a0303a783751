import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { User } from '@/lib/db/models';
import bcrypt from 'bcryptjs';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const { email, newPassword } = await request.json();
    
    await connectDB();
    
    console.log('Fixing password for:', email);
    
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    console.log('New hashed password:', hashedPassword);
    
    const result = await User.findOneAndUpdate(
      { email },
      { hashedPassword },
      { new: true }
    );
    
    if (!result) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Test the new password
    const isValid = await bcrypt.compare(newPassword, hashedPassword);
    
    // Access user properties directly from result
    const userData = result;
    
    return NextResponse.json({
      success: true,
      data: {
        email: userData.email,
        passwordUpdated: true,
        testValidation: isValid
      }
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
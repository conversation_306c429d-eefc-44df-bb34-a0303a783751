import { NextRequest, NextResponse } from 'next/server';
import { secureFileUploadManager } from '@/lib/security/secure-file-upload';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    // Use secure file upload manager
    const result = await secureFileUploadManager.processUpload(request, {
      uploadPath: 'messages',
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxFiles: 1,
      requireAuth: false, // This endpoint allows anonymous uploads
      scanForThreats: true,
      allowedTypes: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'text/plain',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ]
    });

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    if (!result.files || result.files.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No files were processed successfully' },
        { status: 400 }
      );
    }

    const file = result.files[0];

    return NextResponse.json({
      success: true,
      data: {
        fileName: file.originalName,
        fileUrl: file.url,
        fileSize: file.size,
        mimeType: file.mimeType,
        uploadedAt: file.uploadedAt.toISOString(),
        fileId: file.id,
        isSecure: file.isSecure
      },
      warnings: result.quarantinedFiles ? {
        quarantinedFiles: result.quarantinedFiles,
        message: 'Some files were quarantined due to security concerns'
      } : undefined
    });

  } catch (error) {
    console.error('File upload error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { MFAService } from '@/lib/auth/mfa';

export async function POST(request: NextRequest) {
  try {
    const { secret, token } = await request.json();
    
    if (!secret || !token) {
      return NextResponse.json({ error: 'Secret and token required' }, { status: 400 });
    }

    const isValid = MFAService.verifyTOTP(secret, token);
    
    return NextResponse.json({
      success: true,
      valid: isValid
    });
  } catch (error) {
    console.error('MFA verification error:', error);
    return NextResponse.json({ error: 'Verification failed' }, { status: 500 });
  }
}
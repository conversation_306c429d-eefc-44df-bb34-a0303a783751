/**
 * Static Website Content
 * This file contains all static content that doesn't belong in the database
 * Including: FAQ, pricing plans, contact info, navigation, UI elements
 */

import { PricingPlan, CompanyLogo, WorkflowStep, ProvisionCardData } from "@/lib/types";

// ===== PRICING PLANS =====
export const PRICING_PLANS: PricingPlan[] = [
  {
    name: "Starter",
    description: "For small businesses & startups needing essential compliance",
    monthlyPrice: "$99/month",
    yearlyPrice: "$999/year",
    access: {
      whistleblower: "Unlimited",
      investigator: "Up to 5 investigators",
      admin: "1 Admin"
    },
    features: [
      "Secure anonymous reporting",
      "Basic case management tools",
      "Automated case tracking & audit logs",
      "Customizable intake forms",
      "Whistleblower two-way messaging",
      "Basic analytics dashboard"
    ],
    cta: "Get Started"
  },
  {
    name: "Professional",
    description: "For growing companies with advanced compliance needs",
    monthlyPrice: "Contact Sales",
    yearlyPrice: "Contact Sales",
    access: {
      whistleblower: "Unlimited",
      investigator: "Unlimited",
      admin: "Unlimited"
    },
    features: [
      "Everything in Professional plus:",
      "Phone hotline & multilingual support",
      "Unlimited investigators & case managers",
      "Custom server locations",
      "Dedicated success manager",
      "Enterprise-level compliance",
      "Dedicated success manager"
    ],
    isHighlighted: true,
    cta: "Contact Sales"
  },
  {
    name: "Enterprise",
    description: "For large organizations needing full-scale compliance solutions",
    monthlyPrice: "Customs Price",
    yearlyPrice: "Custom Price",
    access: {
      whistleblower: "Unlimited",
      investigator: "Unlimited",
      admin: "Customizable (Up to 10 Admins)"
    },
    features: [
      "Everything in Professional plus:",
      "Phone hotline & multilingual support",
      "Unlimited investigators & case managers",
      "Custom server locations",
      "Dedicated success manager",
      "Enterprise-level compliance",
      "Dedicated success manager"
    ],
    cta: "Contact Sales"
  }
];

// ===== PREMIUM PRO FEATURES =====
export const premiumProFeatures = [
  "Secure & Anonymous Reporting",
  "Advanced Case Management",
  "Multi-Channel Reporting Options",
  "Compliance & Risk Analysis"
];

export const premiumProBenefits = [
  {
    icon: "/desktop/home/<USER>/guarantee.svg",
    text: "30 days money back Guarantee",
    width: 50,
    height: 50
  },
  {
    icon: "/desktop/home/<USER>/hazzle-free.svg",
    text: "No setup fees 100% hassle-free",
    width: 45,
    height: 45
  },
  {
    icon: "/desktop/home/<USER>/one-time.svg",
    text1: "One-time payment, ",
    text2: "no monthly subscription",
    width: 45,
    height: 45
  }
];

// ===== CONTACT INFORMATION =====
export const offices = [
  {
    title: "Dubai",
    address: "Dubai International Financial Centre\nGate Village Building 2, Level 15\nDubai, United Arab Emirates",
    phone: "+971 4 123 4567",
    email: "<EMAIL>",
    hours: "Mon - Fri: 9:00 AM - 6:00 PM GST",
    mapImage: "/images/map-dubai.jpg"
  },
  {
    title: "Abu Dhabi",
    address: "Capital Gate Building\nAbu Dhabi Global Market Square\nAbu Dhabi, United Arab Emirates",
    phone: "+971 2 654 3210",
    email: "<EMAIL>",
    hours: "Mon - Fri: 9:00 AM - 6:00 PM GST",
    mapImage: "/images/map-abudhabi.jpg"
  }
];

export const departments = [
  {
    icon: "/desktop/contact/icons/sales.svg",
    title: "Sales Team",
    description: "For product inquiries and pricing",
    email: "<EMAIL>", 
    phone: "+****************",
    hours: "Within 4 business hours",
    cta: "Contact Sales"
  },
  {
    icon: "/desktop/contact/icons/technical.svg",
    title: "Technical Support",
    description: "For platform assistance and troubleshooting",
    email: "<EMAIL>",
    phone: "+971 2 651 7810",
    hours: "24/7 for Priority Issues",
    cta: "Get Support"
  },
  {
    icon: "/desktop/contact/icons/customer.svg",
    title: "Customer Success",
    description: "For account management and optimization",
    email: "<EMAIL>",
    phone: "+971 55 987 6532",
    hours: "Within 1 business day",
    cta: "Contact your CSM"
  },
  {
    icon: "/desktop/contact/icons/media.svg",
    title: "Media Relations",
    description: "For press inquiries and media partnerships",
    email: "<EMAIL>",
    phone: "+971 50 123 4567",
    hours: "Within 2 business days",
    cta: "Contact Media Team"
  },
  {
    icon: "/desktop/contact/icons/whistleblower.svg",
    title: "Whistleblower Support",
    description: "For secure reporting and whistleblower assistance",
    email: "<EMAIL>",
    phone: "+****************",
    hours: "24/7 Confidential Support",
    cta: "Access Portal"
  }
];

// ===== FAQ DATA =====
export const faqData = [
  {
    id: 1,
    question: "What's included in each pricing plan?",
    answer: "Each plan includes access to our core compliance tools, including KYC verification, AML screening, and case management. Higher-tier plans offer additional features like advanced reporting, bulk verification, and API access.",
    category: "pricing"
  },
  {
    id: 2,
    question: "Can I change my plan later?",
    answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect at the start of your next billing cycle.",
    category: "pricing"
  },
  {
    id: 3,
    question: "Do you offer a free trial?",
    answer: "Yes, we offer a 14-day free trial on all plans so you can test our platform before committing.",
    category: "pricing"
  },
  {
    id: 4,
    question: "Is my data secure?",
    answer: "Absolutely. We use enterprise-grade encryption, secure data centers, and comply with international security standards including SOC 2 Type II and ISO 27001.",
    category: "security"
  },
  {
    id: 5,
    question: "What support do you provide?",
    answer: "We offer comprehensive support including documentation, video tutorials, email support, and priority phone support for Professional and Enterprise plans.",
    category: "support"
  },
  {
    id: 6,
    question: "How does billing work?",
    answer: "Billing is monthly or annual depending on your chosen plan. Annual plans receive a 20% discount. All payments are processed securely through our payment partners.",
    category: "pricing"
  },
  {
    id: 7,
    question: "Can I cancel anytime?",
    answer: "Yes, you can cancel your subscription at any time. Your access will continue until the end of your current billing period.",
    category: "pricing"
  },
  {
    id: 8,
    question: "Do you offer custom enterprise solutions?",
    answer: "Yes, we offer custom enterprise solutions with dedicated support, custom integrations, and tailored compliance features. Contact our sales team for more information.",
    category: "enterprise"
  }
];

export const contactFaqs = [
  {
    id: 1,
    question: "How quickly will I receive a response?",
    answer: "Response times vary by department: Sales (4 hours), Technical Support (24/7 for priority), Customer Success (1 business day), Media Relations (2 business days)."
  },
  {
    id: 2,
    question: "Can I schedule a demo?",
    answer: "Yes! You can schedule a personalized demo with our sales team. Use the 'Schedule Demo' section on this page or contact our sales team directly."
  },
  {
    id: 3,
    question: "Do you offer on-site training?",
    answer: "Yes, we provide on-site training and implementation support for Enterprise customers. Contact our Customer Success team for more details."
  }
];

// ===== HELP & SUPPORT CONTENT =====
export const helpFaqData = {
  general: [
    {
      id: 1,
      question: "How do I submit a whistleblower report?",
      answer: "Navigate to the 'Submit Report' section in your dashboard, fill out the required information, attach any evidence files, and submit. You can choose to remain anonymous or provide your contact details."
    },
    {
      id: 2,
      question: "Will my identity be protected?",
      answer: "Yes, we offer multiple levels of anonymity protection. You can choose to remain completely anonymous or provide limited contact information for follow-up questions."
    }
  ],
  reporting: [
    {
      id: 3,
      question: "Can I track the progress of my report?",
      answer: "Yes, you can track your report's progress in real-time through the 'My Reports' section. You'll receive updates as investigators review and act on your submission."
    },
    {
      id: 4,
      question: "What types of misconduct can I report?",
      answer: "You can report various types of misconduct including financial fraud, safety violations, discrimination, harassment, corruption, and other ethical violations."
    }
  ],
  security: [
    {
      id: 5,
      question: "How secure is the platform?",
      answer: "Our platform uses end-to-end encryption, secure servers, and follows international security standards. All data is protected and access is strictly controlled."
    }
  ]
};

export const helpCategories = [
  {
    title: "Getting Started",
    description: "Learn the basics of using the platform",
    icon: "BookOpen",
    articleCount: 12
  },
  {
    title: "Submitting Reports",
    description: "How to create and submit whistleblower reports",
    icon: "FileText",
    articleCount: 8
  },
  {
    title: "Account & Security",
    description: "Managing your account and security settings",
    icon: "Shield",
    articleCount: 15
  },
  {
    title: "Communication",
    description: "Using secure messaging and notifications",
    icon: "MessageCircle",
    articleCount: 6
  }
];

export const quickLinks = [
  {
    title: "User Manual",
    icon: "FileText",
    href: "#manual"
  },
  {
    title: "Video Tutorials",
    icon: "Video",
    href: "#tutorials"
  },
  {
    title: "Glossary of Terms",
    icon: "BookOpen",
    href: "#glossary"
  },
  {
    title: "Download Resources",
    icon: "Download",
    href: "#downloads"
  }
];

export const popularHelpTopics = [
  {
    title: "How to Submit a Report",
    description: "Step-by-step guide to submitting a whistleblower report",
    icon: "FileText"
  },
  {
    title: "Anonymity Settings",
    description: "Understanding and configuring your anonymity options",
    icon: "Shield"
  },
  {
    title: "Secure Messaging",
    description: "How to communicate securely with case handlers",
    icon: "MessageSquare"
  },
  {
    title: "Account Security",
    description: "Best practices for keeping your account secure",
    icon: "Lock"
  }
];

// ===== UI ELEMENTS =====
export const COMPANY_LOGOS: CompanyLogo[] = [
  { src: '/desktop/home/<USER>/terra.svg', alt: 'Terra logo', height: 122, width: 52 },
  { src: '/desktop/home/<USER>/webflow.svg', alt: 'Webflow logo', height: 122, width: 52 },
  { src: '/desktop/home/<USER>/linkedin.svg', alt: 'LinkedIn logo', height: 123, width: 50 },
  { src: '/desktop/home/<USER>/genz.svg', alt: 'GenZ logo', height: 123, width: 50 },
  { src: '/desktop/home/<USER>/trace.svg', alt: 'Trace logo', height: 106, width: 42 }
];

// ===== NAVIGATION DATA =====
export const ABOUT_ITEMS = [
  { href: "/about/company", title: "Company", description: "Learn more about our company history and mission" },
  { href: "/about/team", title: "Team", description: "Meet our leadership and development teams" },
  { href: "/about/careers", title: "Careers", description: "View current job openings and opportunities" }
];

export const PRODUCTS_ITEMS = [
  { href: "/products/whistleblower", title: "For Whistleblowers", description: "Secure reporting tools for employees" },
  { href: "/products/investigator", title: "For Investigators", description: "Case management and investigation tools" },
  { href: "/products/admin", title: "For Administrators", description: "Platform management and oversight tools" }
];

export const LOGIN_ITEMS = [
  { href: "/login/whistleblower", title: "Whistleblower" },
  { href: "/login/investigator", title: "Internal Investigator & Compliance Officer" },
  { href: "/login/admin", title: "System Admin" }
];

export const SIGNUP_ITEMS = [
  { href: "/signup/whistleblower", title: "Whistleblower" },
  { href: "/signup/admin", title: "System Admin" }
];

export const MOBILE_NAV_ITEMS = [
  { href: "/", title: "Home" },
  { href: "/products", title: "Products" },
  { href: "/pricing", title: "Pricing" },
  { href: "/contact", title: "Contact" }
];

// ===== TESTIMONIALS =====
export const testimonials = [
  {
    id: 1,
    name: "Sarah Johnson",
    role: "Compliance Officer",
    company: "TechCorp Industries",
    image: "/desktop/home/<USER>/1.svg",
    rating: 5,
    content: "This platform has revolutionized how we handle whistleblower reports. The security features give our employees confidence to speak up, and the case management tools help us respond quickly and effectively."
  },
  {
    id: 2,
    name: "Michael Chen",
    role: "Internal Auditor",
    company: "Global Manufacturing Ltd",
    image: "/desktop/home/<USER>/1.svg",
    rating: 5,
    content: "The anonymous reporting feature and secure messaging system have been game-changers for our organization. We've seen a significant increase in reports since implementing this solution."
  },
  {
    id: 3,
    name: "Emma Rodriguez",
    role: "HR Director",
    company: "Financial Services Inc",
    image: "/desktop/home/<USER>/1.svg",
    rating: 5,
    content: "Outstanding platform with excellent customer support. The integration was seamless, and the training provided was comprehensive. Highly recommend for any organization serious about compliance."
  }
];

// ===== WORKFLOW STEPS =====
export const workflowSteps: WorkflowStep[] = [
  {
    step: 1,
    title: "Report Submission",
    description: "Individuals can securely submit a whistleblower report through an encrypted and anonymous platform. The system ensures confidentiality while allowing users to provide necessary details and evidence.",
    icon: "/desktop/home/<USER>/reporting.svg"
  },
  {
    step: 2,
    title: "Case Review & Verification",
    description: "The submitted report is assessed by compliance officers, who verify the authenticity of the claims using advanced screening and data analysis tools.",
    icon: "/desktop/home/<USER>/investigations.svg"
  },
  {
    step: 3,
    title: "Investigation & Action",
    description: "Once verified, the case is assigned to the appropriate authority for further investigation. The platform tracks progress while maintaining anonymity for the whistleblower.",
    icon: "/desktop/home/<USER>/solutions.svg"
  },
  {
    step: 4,
    title: "Resolution & Reporting",
    description: "Final decisions are documented, and necessary actions are taken. The whistleblower may receive updates (if opted) while ensuring full compliance with legal and ethical standards.",
    icon: "/desktop/home/<USER>/management.svg"
  }
];

// ===== PROVISION CARDS =====
export const provisionCards: ProvisionCardData[] = [
  {
    icon: "/desktop/home/<USER>/reporting.svg",
    title: "Secure & Anonymous",
    description: "End-to-end encryption ensures your identity and information remain protected"
  },
  {
    icon: "/desktop/home/<USER>/solutions.svg",
    title: "Regulatory Compliant",
    description: "Meets all international whistleblower protection standards and regulations"
  },
  {
    icon: "/desktop/home/<USER>/investigations.svg",
    title: "Real-time Tracking",
    description: "Monitor the progress of your report with transparent status updates"
  },
  {
    icon: "/desktop/home/<USER>/management.svg",
    title: "24/7 Support",
    description: "Round-the-clock assistance from our dedicated compliance experts"
  }
];

// Alias for compatibility
export const PROVISION_CARDS = provisionCards;

// ===== DASHBOARD NAVIGATION =====
export const NAVIGATION_ITEMS = [
  { href: "/dashboard/whistleblower", title: "Dashboard", icon: "Home" },
  { href: "/dashboard/whistleblower/my-reports", title: "My Reports", icon: "FileText" },
  { href: "/dashboard/whistleblower/secure-message", title: "Secure Messages", icon: "MessageSquare" },
  { href: "/dashboard/whistleblower/profile-settings", title: "Profile & Settings", icon: "Settings" },
  { href: "/dashboard/whistleblower/help", title: "Help / FAQ", icon: "HelpCircle" }
];

export const ADMIN_NAVIGATION_ITEMS = [
  // Main section
  { href: "/dashboard/admin", title: "Dashboard", icon: "Home", group: "Main" },
  { href: "/dashboard/admin/case-management", title: "Case Management", icon: "FolderOpen", group: "Main" },
  { href: "/dashboard/admin/user-management", title: "User Management", icon: "Users", group: "Main" },
  { href: "/dashboard/admin/notifications", title: "Notifications", icon: "Bell", group: "Main" },
  { href: "/dashboard/admin/communications", title: "Communications", icon: "MessageSquare", group: "Main" },

  // Management section
  { href: "/dashboard/admin/escalations", title: "Escalations & SLAs", icon: "AlertTriangle", group: "Management" },
  { href: "/dashboard/admin/audit-logs", title: "Audit Logs", icon: "FileSearch", group: "Management" },
  { href: "/dashboard/admin/reports-analytics", title: "Reports & Analytics", icon: "BarChart3", group: "Management" },

  // System section
  { href: "/dashboard/admin/profile-settings", title: "Settings", icon: "Settings", group: "System" },
  { href: "/dashboard/admin/help", title: "Help & Support", icon: "HelpCircle", group: "System" }
];

export const PROFILE_ITEMS = [
  { href: "/dashboard/profile-settings", title: "Profile", icon: "User" },
  { href: "/api/auth/logout", title: "Logout", icon: "LogOut" }
];

// ===== LANGUAGE OPTIONS =====
export const languageOptions = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'es', name: 'Español', flag: '🇪🇸' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'de', name: 'Deutsch', flag: '🇩🇪' },
];

// ===== HERO TESTIMONIAL =====
export const HERO_TESTIMONIAL = {
  name: "Sarah Johnson",
  role: "Compliance Officer",
  company: "TechCorp Industries",
  avatar: "/desktop/home/<USER>/ceo.svg",
  content: "This platform has revolutionized how we handle whistleblower reports. The security features give our employees confidence to speak up."
};

// ===== WORKFLOW STEPS =====
export const WORKFLOW_STEPS = workflowSteps;

// ===== FOOTER LINKS =====
export const FOOTER_LINKS = [
  { href: "/privacy", title: "Privacy Policy" },
  { href: "/terms", title: "Terms of Service" },
  { href: "/security", title: "Security" },
  { href: "/compliance", title: "Compliance" }
];

export const SOCIAL_LINKS = [
  { href: "https://facebook.com/whistleblower", title: "Facebook", icon: "Facebook" },
  { href: "https://instagram.com/whistleblower", title: "Instagram", icon: "Instagram" },
  { href: "https://twitter.com/whistleblower", title: "Twitter", icon: "Twitter" },
  { href: "https://linkedin.com/company/whistleblower", title: "LinkedIn", icon: "Linkedin" },
  { href: "https://youtube.com/whistleblower", title: "Youtube", icon: "Youtube" }
];

export const SYSTEM_LINKS = [
  { href: "/products", title: "Product" },
  { href: "/pricing", title: "Pricing" },
  { href: "/blog", title: "Blog" },
  { href: "/about", title: "About Us" },
  { href: "/contact", title: "Contact" }
];

export const WHISTLEBLOWING_LINKS = [
  { href: "/compliance-network", title: "Compliance network" },
  { href: "/whistleblowing-guide", title: "Whistleblowing System Guide" },
  { href: "/whistleblowing-directive", title: "Whistleblowing Directive Summary" }
];

// ===== BENEFITS IMAGES =====
export const BENEFITS_IMAGES = [
  { src: "/desktop/home/<USER>/benefits-hero.svg", alt: "Secure Reporting", width: 500, height: 300 },
  { src: "/desktop/home/<USER>/benefits-hero.svg", alt: "Anonymous Protection", width: 500, height: 300 },
  { src: "/desktop/home/<USER>/benefits-hero.svg", alt: "Real-time Tracking", width: 500, height: 300 }
];

// ===== HEADER NAVIGATION =====
export const PRODUCT_ITEMS = [
  {
    href: "/products/whistleblower",
    title: "Whistleblower Portal",
    description: "A secure platform for employees and stakeholders to submit reports anonymously and safely.",
    cta: "Explore Report Hub"
  },
  {
    href: "/products/investigator",
    title: "Investigator Portal",
    description: "A purpose-built workspace to manage investigations, review evidence, and maintain compliance.",
    cta: "Explore Case Center"
  },
  {
    href: "/products/admin",
    title: "Admin Portal",
    description: "Monitor all whistleblowing activities, assign cases, configure workflows, and generate insights.",
    cta: "Explore Control Suite"
  }
];

// ===== PREMIUM PRO FEATURE CARDS =====
export const premiumProFeatureCards = [
  {
    icon: "Shield",
    title: "Advanced Security",
    description: "Military-grade encryption and security protocols"
  },
  {
    icon: "Users",
    title: "Multi-tenant Support",
    description: "Manage multiple organizations from one dashboard"
  },
  {
    icon: "BarChart3",
    title: "Advanced Analytics",
    description: "Comprehensive reporting and analytics suite"
  },
  {
    icon: "Clock",
    title: "24/7 Support",
    description: "Round-the-clock premium support"
  }
];

export const premiumProBenefitsList = [
  "Unlimited reports and users",
  "Advanced compliance features",
  "Custom branding options",
  "API access and integrations",
  "Priority support",
  "Advanced security features"
];

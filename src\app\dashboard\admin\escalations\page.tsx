"use client";

import { useState, useEffect } from "react";
import Header from "@/components/dashboard-components/Header";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, Clock, TrendingUp, AlertCircle, Loader2 } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";

interface Escalation {
    id: string;
    caseId: string;
    subject: string;
    department: string;
    priority: 'Low' | 'Medium' | 'High' | 'Critical';
    escalatedDate: string;
    slaStatus: 'On Track' | 'At Risk' | 'Breached';
    daysOverdue: number;
    status: 'Open' | 'In Progress' | 'Resolved' | 'Closed';
    escalatedBy?: {
        name: string;
        email: string;
    };
    escalatedTo?: {
        name: string;
        email: string;
    };
    reason: string;
}

export default function EscalationsPage() {
    const { user } = useAuth();
    const [escalations, setEscalations] = useState<Escalation[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchEscalations = async () => {
            try {
                setLoading(true);
                const response = await fetch('/api/escalations', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch escalations');
                }

                const data = await response.json();
                if (data.success) {
                    setEscalations(data.data);
                } else {
                    throw new Error(data.error || 'Failed to fetch escalations');
                }
            } catch (err) {
                console.error('Error fetching escalations:', err);
                setError(err instanceof Error ? err.message : 'Failed to fetch escalations');
            } finally {
                setLoading(false);
            }
        };

        if (user) {
            fetchEscalations();
        }
    }, [user]);

    // Calculate statistics from real data
    const stats = {
        total: escalations.length,
        breached: escalations.filter(e => e.slaStatus === 'Breached').length,
        atRisk: escalations.filter(e => e.slaStatus === 'At Risk').length,
        onTrack: escalations.filter(e => e.slaStatus === 'On Track').length,
    };

    return (
        <>
            <div className="w-full h-full">
                <Header />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="Escalations & SLAs"
                >
                    {/* Page Header */}
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">Escalations & SLAs</h1>
                        <p className="text-gray-600">Monitor case escalations and SLA compliance</p>
                    </div>

                    {/* SLA Statistics */}
                    {loading ? (
                        <div className="flex justify-center items-center py-8">
                            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                            <span className="ml-2 text-gray-600">Loading escalations...</span>
                        </div>
                    ) : error ? (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div className="flex items-center">
                                <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                                <span className="text-red-800">{error}</span>
                            </div>
                        </div>
                    ) : (
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <Card className="bg-white border-0 shadow-sm">
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                        <CardTitle className="text-sm font-medium text-gray-600">SLA Violations</CardTitle>
                                        <AlertTriangle className="h-4 w-4 text-red-600" />
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold text-gray-900">{stats.breached}</div>
                                        <p className="text-xs text-red-600">
                                            {stats.total > 0 ? Math.round((stats.breached / stats.total) * 100) : 0}% of cases
                                        </p>
                                    </CardContent>
                                </Card>

                                <Card className="bg-white border-0 shadow-sm">
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                        <CardTitle className="text-sm font-medium text-gray-600">Escalated Cases</CardTitle>
                                        <TrendingUp className="h-4 w-4 text-orange-600" />
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                                        <p className="text-xs text-orange-600">Total escalations</p>
                                    </CardContent>
                                </Card>

                                <Card className="bg-white border-0 shadow-sm">
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                        <CardTitle className="text-sm font-medium text-gray-600">At Risk</CardTitle>
                                        <Clock className="h-4 w-4 text-yellow-600" />
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold text-gray-900">{stats.atRisk}</div>
                                        <p className="text-xs text-yellow-600">Approaching deadline</p>
                                    </CardContent>
                                </Card>

                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Critical Cases</CardTitle>
                                <AlertCircle className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">4</div>
                                <p className="text-xs text-purple-600">Immediate action</p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Escalated Cases */}
                    <Card className="bg-white border-0 shadow-sm">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <CardTitle className="text-lg font-semibold text-gray-900">
                                    Escalated Cases
                                </CardTitle>
                                <Button variant="outline" size="sm" className="hover:bg-[#BBF49C] hover:border-[#BBF49C]">
                                    View All
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="border-b border-gray-200">
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Case ID</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Subject</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Department</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Priority</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Escalated Date</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">SLA Status</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Days Overdue</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {escalations.length === 0 ? (
                                            <tr>
                                                <td colSpan={7} className="py-8 text-center text-gray-500">
                                                    No escalations found
                                                </td>
                                            </tr>
                                        ) : (
                                            escalations.map((escalation) => (
                                                <tr key={escalation.id} className="border-b border-gray-100 hover:bg-gray-50">
                                                    <td className="py-3 px-2">
                                                        <span className="font-medium text-sm text-gray-900">#{escalation.caseId}</span>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <span className="text-sm text-gray-900">{escalation.subject}</span>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <span className="text-sm text-gray-600">{escalation.department}</span>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <Badge
                                                            variant="secondary"
                                                            className={`text-xs ${
                                                                escalation.priority === 'Critical' ? 'bg-red-100 text-red-800' :
                                                                escalation.priority === 'High' ? 'bg-orange-100 text-orange-800' :
                                                                escalation.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                                                'bg-gray-100 text-gray-800'
                                                            }`}
                                                        >
                                                            {escalation.priority}
                                                        </Badge>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <span className="text-sm text-gray-600">{escalation.escalatedDate}</span>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <Badge
                                                            variant="secondary"
                                                            className={`text-xs ${
                                                                escalation.slaStatus === 'Breached' ? 'bg-red-100 text-red-800' :
                                                                escalation.slaStatus === 'At Risk' ? 'bg-yellow-100 text-yellow-800' :
                                                                'bg-green-100 text-green-800'
                                                            }`}
                                                        >
                                                            {escalation.slaStatus}
                                                        </Badge>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <span className={`text-sm font-medium ${
                                                            escalation.daysOverdue > 0 ? 'text-red-600' : 'text-gray-600'
                                                        }`}>
                                                            {escalation.daysOverdue > 0 ? `${escalation.daysOverdue} days` : 'On time'}
                                                        </span>
                                                    </td>
                                                </tr>
                                            ))
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>
                        </>
                    )}
                </main>
            </div>
        </>
    );
}
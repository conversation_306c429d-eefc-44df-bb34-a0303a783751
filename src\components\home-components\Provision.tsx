import Image from "next/image";
import { <PERSON>, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PROVISION_CARDS } from "@/lib/mockData";
import Link from "next/link";

export default function Provision() {
    const renderStars = (count: number) => (
        Array(count).fill(null).map((_, index) => (
            <Image
                key={index}
                src="/desktop/home/<USER>/star.svg"
                alt="star"
                width={14}
                height={14}
                className="mt-1"
                style={{ width: '13', height: 'auto' }}
            />
        ))
    );

    return (
        <div className={`flex flex-col justify-between gap-6 md:gap-12 bg-[#1E4841] w-full px-4 sm:px-8 md:px-16 lg:px-44 pb-10 pt-16 xl:py-16`}>
            <div className="w-full flex flex-col xl:flex-row justify-between gap-6">
                <div className="flex flex-col gap-6">
                    <div className="h-2 w-full md:w-[290px] border-t-3 border-[#BBF49C]"></div>
                    <p className="w-full md:w-[450px] font-bold text-[22px] md:text-[28px]/8 text-[#FBFBFC]">Provides a Secure & Compliant Approach to Whistleblowing & Investigations</p>
                </div>
                <div className="w-full md:w-[600px] flex flex-col justify-between gap-4 lg:gap-6">
                    <div className="flex flex-col md:flex-row justify-between gap-4 md:gap-10">
                        <p className="text-sm font-normal text-[#FBFBFC]">SmartData been helping organizations and Providers through the World to manage their IT with our unique approach to technology management and consultancy solutions.</p>
                        <p className="text-base/5 font-bold text-[#FBFBFC]">Our advanced tools ensure seamless case handling, fraud detection, and regulatory compliance to protect both organizations and employees.</p>
                    </div>
                    <Link href="/pricing">
                        <Button
                            variant="default"
                            className="w-fit px-6 md:px-8 py-4 md:py-6 text-base md:text-lg text-[#1E4841] bg-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 shadow-sm font-semibold"
                        >
                            Our Pricing
                        </Button>
                    </Link>
                </div>
            </div>
            <div className="w-full flex flex-col md:flex-row gap-6 md:gap-16 lg:gap-18 xl:gap-6 items-stretch justify-between flex-wrap xl:flex-nowrap">
                {PROVISION_CARDS.map((card, index) => (
                    <Card key={index} className="w-full h-auto md:w-4/9 md:h-[300px] xl:h-[410px] bg-transparent flex flex-col justify-start items-center px-2 py-6 border-[#6B7271]">
                        <Image
                            src={card.icon}
                            alt={card.title}
                            width={70}
                            height={70}
                            className="m-4"
                            style={{ width: '70px', height: 'auto' }}
                        />
                        <CardHeader className="w-full text-center">
                            <CardTitle className="text-[#FBFBFC]">{card.title}</CardTitle>
                            <CardDescription className="text-[#E5E6E6] px-2 mt-4">{card.description}</CardDescription>
                        </CardHeader>
                    </Card>
                ))}
            </div>
            <div className="flex flex-col xl:flex-row gap-4 md:gap-6 xl:gap-10 mt-4 pb-4 xl:pb-8 items-start w-full md:w-3/7">
                <div className="flex mt-[1px] gap-1">
                    {renderStars(5)}
                </div>
                <div>
                    <p className="text-sm/6 text-[#E5E6E6] tracking-widest font-normal">
                        <span className="border-b py-1 border-[#BBF49C] font-bold">99.9% Compliance & Security</span> based on 750+<br />reviews and global best practices.
                    </p>
                </div>
            </div>
        </div>
    );
}
"use client";

import { useEffect, useState, Suspense } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'admin' | 'whistleblower';
  redirectTo?: string;
}

function ProtectedRouteContent({
  children,
  requiredRole,
  redirectTo = '/login/whistleblower'
}: ProtectedRouteProps) {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    if (isLoading) {
      return;
    }

    // If no user is logged in, redirect to login
    if (!user) {
      console.log('ProtectedRoute: No user found, redirecting to:', redirectTo);
      setIsRedirecting(true);

      // Store current path for redirect after login (only on client side)
      if (typeof window !== 'undefined') {
        const currentPath = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
        if (currentPath !== redirectTo && !currentPath.startsWith('/login')) {
          localStorage.setItem('redirectAfterLogin', currentPath);
        }
      }

      // Use Next.js router for navigation
      router.push(redirectTo);
      return;
    }

    // If a specific role is required, check if user has that role
    if (requiredRole && user.role !== requiredRole) {
      console.log('ProtectedRoute: User role mismatch, redirecting based on role:', user.role);
      setIsRedirecting(true);

      // Redirect to appropriate dashboard based on user role
      if (user.role === 'admin') {
        router.push('/dashboard/admin');
      } else {
        router.push('/dashboard/whistleblower');
      }
      return;
    }

    setIsAuthorized(true);
    setIsRedirecting(false);
  }, [user, isLoading, requiredRole, redirectTo, router, pathname, searchParams]);

  // Show loading while checking authentication or redirecting
  if (isLoading || isRedirecting) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#1E4841]"></div>
      </div>
    );
  }

  // Show nothing while not authorized
  if (!isAuthorized) {
    return null;
  }

  return <>{children}</>;
}

export default function ProtectedRoute(props: ProtectedRouteProps) {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#1E4841]"></div>
      </div>
    }>
      <ProtectedRouteContent {...props} />
    </Suspense>
  );
}
import { NextRequest, NextResponse } from 'next/server';

type ApiHandler = (
  req: NextRequest,
  params: { [key: string]: string | string[] | undefined }
) => Promise<NextResponse>;

type ApiConfig = {
  requireAuth?: boolean;
  requireAdmin?: boolean;
  methods: ('GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH')[];
  csrfProtection?: boolean; // Whether to check CSRF token
};

export function createApiHandler(handler: A<PERSON><PERSON>and<PERSON>, config: ApiConfig) {
  return async function (
    req: NextRequest,
    params: { [key: string]: string | string[] | undefined } = {}
  ) {
    // Check HTTP method
    if (!config.methods.includes(req.method as ('GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'))) {
      return NextResponse.json(
        { success: false, error: `Method ${req.method} Not Allowed` },
        { status: 405 }
      );
    }
    
    // Skip CSRF and auth checks for hardcoded system
    // TODO: Implement proper authentication and CSRF protection when using real database

    try {
      // Call the handler function
      return await handler(req, params);
    } catch {
      return NextResponse.json(
        { success: false, error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  };
}
import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { signUpSchema } from '@/lib/schemas';
import { isTestUser } from '@/lib/auth/secure-test-users';
import { generateToken } from '@/lib/middleware/auth';
import { emailService } from '@/lib/email/emailService';
import { NotificationService } from '@/lib/services/notificationService';
import connectDB from '@/lib/db/mongodb';
import mongoose from 'mongoose';
import { withAuthSecurity } from '@/lib/middleware/integrated-security';
import logger from '@/lib/utils/logger';
import Company from '@/lib/db/models/Company';

export const runtime = 'nodejs';

async function signupHandler(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = signUpSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { fullName, email, phoneNumber, password, role = 'whistleblower', companyId, companyName, recaptchaToken } = validationResult.data;

    // For whistleblower signups, validate reCAPTCHA in production
    if (role === 'whistleblower' && process.env.NODE_ENV === 'production' && recaptchaToken) {
      try {
        const recaptchaResponse = await fetch('https://www.google.com/recaptcha/api/siteverify', {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: `secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${recaptchaToken}`
        });

        const recaptchaResult = await recaptchaResponse.json();
        if (!recaptchaResult.success) {
          return NextResponse.json(
            { success: false, error: 'reCAPTCHA verification failed. Please try again.' },
            { status: 400 }
          );
        }
      } catch (error) {
        logger.error('reCAPTCHA verification error:', error);
        return NextResponse.json(
          { success: false, error: 'reCAPTCHA verification failed. Please try again.' },
          { status: 400 }
        );
      }
    }

    // Check if this is a test user email (prevent signup for test users)
    if (isTestUser(email)) {
      return NextResponse.json(
        { success: false, error: 'This email is reserved for testing. Please use a different email.' },
        { status: 409 }
      );
    }

    // Check if user already exists in database
    await connectDB();
    const existingDbUser = await DataService.getUserByEmail(email);
    if (existingDbUser) {
      return NextResponse.json(
        { success: false, error: 'User already exists' },
        { status: 409 }
      );
    }

    // Split full name into first and last name
    const nameParts = fullName.trim().split(' ');
    const firstName = nameParts[0];
    const lastName = nameParts.slice(1).join(' ') || '';

    // Handle company assignment
    let finalCompanyId = companyId;

    // For admin users: create new company if needed
    if (role === 'admin' && companyName && !companyId) {
      try {
        const newCompany = await DataService.createCompany({
          name: companyName,
          contactEmail: email,
          isActive: true,
          subscriptionStatus: 'Pending'
        });
        finalCompanyId = newCompany._id.toString();
        logger.info('New company created for admin signup', {
          companyId: finalCompanyId,
          companyName,
          adminEmail: email
        });
      } catch (error) {
        logger.error('Failed to create company during admin signup', {
          companyName,
          adminEmail: email,
          error
        });
        return NextResponse.json(
          { success: false, error: 'Failed to create company. Please try again.' },
          { status: 500 }
        );
      }
    }

    // For whistleblower users: assign to default company if no company specified
    if (role === 'whistleblower' && !finalCompanyId) {
      try {
        await connectDB();
        let defaultCompany = await Company.findOne({ name: 'Default Company' });

        if (!defaultCompany) {
          defaultCompany = await DataService.createCompany({
            name: 'Default Company',
            contactEmail: '<EMAIL>',
            isActive: true,
            subscriptionStatus: 'Active'
          });
          logger.info('Created default company for whistleblower signups', {
            companyId: defaultCompany._id.toString()
          });
        }

        finalCompanyId = defaultCompany._id.toString();
        logger.info('Assigned whistleblower to default company', {
          userId: email,
          companyId: finalCompanyId
        });
      } catch (error) {
        logger.error('Failed to assign whistleblower to default company', {
          email,
          error
        });
        // Don't fail signup if default company assignment fails
        // The user can be assigned later using the fix-user-company endpoint
      }
    }

    // Create user in database
    const newUser = await DataService.createUser({
      email,
      firstName,
      lastName,
      phoneNumber,
      password,
      role: role as 'admin' | 'investigator' | 'whistleblower',
      companyId: finalCompanyId || undefined,
      isActive: true
    });

    // Create welcome conversation and message for new user
    await createWelcomeMessage(newUser._id.toString(), role);

    // Send welcome email
    try {
      await emailService.sendWelcomeEmail({
        id: newUser._id.toString(),
        email: (newUser as unknown as { email: string }).email,
        firstName: (newUser as unknown as { firstName: string }).firstName,
        lastName: (newUser as unknown as { lastName: string }).lastName,
        role: (newUser as unknown as { role: string }).role
      });
      logger.info('Welcome email sent to new user', {
        userId: newUser._id.toString(),
        email: (newUser as unknown as { email: string }).email
      });
    } catch (error) {
      logger.error('Failed to send welcome email', {
        userId: newUser._id.toString(),
        email: (newUser as unknown as { email: string }).email,
        error
      });
      // Don't fail registration if email fails
    }

    // Create welcome notification
    try {
      const userName = `${firstName} ${lastName}`.trim();
      let companyName: string | undefined;

      if (companyId) {
        const company = await DataService.getCompanyById(companyId);
        const companyData = company as unknown as { name?: string };
        companyName = companyData?.name;
      }

      await NotificationService.createWelcomeNotification(
        newUser._id.toString(),
        role,
        userName,
        companyName
      );
      logger.info('Welcome notification created for new user', {
        userId: newUser._id.toString(),
        role
      });
    } catch (error) {
      logger.error('Failed to create welcome notification', {
        userId: newUser._id.toString(),
        error
      });
      // Don't fail registration if notification fails
    }

    // Generate JWT token
    const token = generateToken(
      newUser._id.toString(),
      (newUser as unknown as { role: string }).role,
      (newUser as unknown as { companyId?: string }).companyId
    );

    // Return success response (without password)
    return NextResponse.json({
      success: true,
      message: 'User created successfully! Check your email for a welcome message.',
      token,
      user: {
        id: newUser._id,
        email: (newUser as unknown as { email: string }).email,
        firstName: (newUser as unknown as { firstName: string }).firstName,
        lastName: (newUser as unknown as { lastName: string }).lastName,
        role: (newUser as unknown as { role: string }).role,
        companyId: (newUser as unknown as { companyId?: string }).companyId,
        isActive: (newUser as unknown as { isActive: boolean }).isActive
      }
    }, { status: 201 });

  } catch {
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function createWelcomeMessage(userId: string, role: string) {
  try {
    // Find admin user to send welcome message from
    const adminUser = await DataService.getUserByEmail('<EMAIL>');
    if (!adminUser) {
      return;
    }

    // Create a dummy report for the conversation (required by the schema)
    const welcomeReport = await DataService.createReport({
      title: `Welcome to 7IRIS - ${role} Account`,
      description: 'Welcome message conversation',
      category: 'Other',
      priority: 'Low',
      userId: new mongoose.Types.ObjectId(userId),
      status: 'New',
      isAnonymous: false
    });

    // Create conversation between admin and new user
    const conversation = await DataService.createConversation({
      reportId: welcomeReport._id.toString(),
      participants: [adminUser._id.toString(), userId]
    });

    // Create welcome message based on role
    let welcomeContent = '';
    if (role === 'whistleblower') {
      welcomeContent = `Welcome to 7IRIS Whistleblowing Platform! 🎉

Thank you for joining our secure platform. Here's what you can do:

• Submit reports safely and securely
• Communicate with investigators through encrypted messaging
• Track the progress of your reports
• Maintain your anonymity when needed

Your safety and privacy are our top priorities. If you have any questions, feel free to reach out.

Best regards,
7IRIS Admin Team`;
    } else if (role === 'admin') {
      welcomeContent = `Welcome to 7IRIS Admin Portal! 🎉

Thank you for joining as an administrator. Here are your capabilities:

• Manage and review all reports
• Assign investigators to cases
• Monitor platform activity and statistics
• Communicate with users through secure messaging
• Access administrative tools and settings

If you need any assistance getting started, please don't hesitate to ask.

Best regards,
7IRIS Admin Team`;
    } else {
      welcomeContent = `Welcome to 7IRIS Platform! 🎉

Thank you for joining our secure platform. You now have access to:

• Secure messaging system
• Report management tools
• Real-time notifications
• Encrypted communications

If you have any questions about using the platform, feel free to reach out.

Best regards,
7IRIS Admin Team`;
    }

    // Send welcome message
    await DataService.createMessage({
      conversationId: conversation._id.toString(),
      senderId: adminUser._id.toString(),
      content: welcomeContent,
      messageType: 'text'
    });
  } catch {
    // Don't throw error as this is not critical for signup
  }
}

// Apply rate limiting security middleware
export const POST = withAuthSecurity(signupHandler);
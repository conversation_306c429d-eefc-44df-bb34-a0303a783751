import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { ChartSkeleton } from "@/components/ui/skeleton-components";
import { ArrowDownToLine, MoreVertical } from "lucide-react";
import { <PERSON>A<PERSON>s, YAxis, PieChart, Pie, Cell, Area, AreaChart, CartesianGrid } from "recharts";

interface StatsLike {
    totalReports: number;
    newReports: number;
    underReviewReports: number;
    awaitingResponseReports: number;
    resolvedReports: number;
    highPriorityReports: number;
    periodComparison: {
        totalReportsChange: number;
        newReportsChange: number;
        resolvedReportsChange: number;
        period: string;
    };
    chartData?: {
        overTime: Array<{ month: string; reports: number; cases: number }>;
        statusDistribution: Array<{ name: string; value: number; fill: string }>;
    };
    lastCalculated?: Date;
}

interface ReportChartsProps {
    stats?: StatsLike | null;
    isLoading?: boolean;
}

export default function ReportCharts({ stats: propStats, isLoading: propIsLoading }: ReportChartsProps = {}) {
    // Use provided props
    const stats = propStats;
    const isLoading = propIsLoading;

    // Use data from API or fallback to empty data
    const chartData = stats?.chartData || {
        overTime: [],
        statusDistribution: []
    };

    // Check if data is empty
    const hasOverTimeData = chartData.overTime && chartData.overTime.some(item => item.reports > 0);
    const hasStatusData = chartData.statusDistribution && chartData.statusDistribution.length > 0;

    // Download functions
    const downloadOverTimeChart = () => {
        const csvContent = "data:text/csv;charset=utf-8,"
            + "Month,Reports\n"
            + chartData.overTime.map(row => `${row.month},${row.reports}`).join("\n");

        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "reports-over-time.csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const downloadStatusChart = () => {
        const csvContent = "data:text/csv;charset=utf-8,"
            + "Status,Count\n"
            + chartData.statusDistribution.map(row => `${row.name},${row.value}`).join("\n");

        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "report-status-distribution.csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (isLoading) {
        return (
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
                <ChartSkeleton
                    title="Reports Over Time"
                    className="lg:col-span-2"
                />
                <ChartSkeleton
                    title="Report Status Distribution"
                    className="lg:col-span-2"
                />
            </div>
        );
    }

    return (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
            {/* Reports Over Time Chart */}
            <Card className="lg:col-span-2">
                <CardHeader className="flex flex-row items-center justify-between gap-2 sm:gap-0">
                    <div>
                        <CardTitle className="text-base sm:text-lg">Reports Over Time</CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={downloadOverTimeChart}
                            disabled={!hasOverTimeData}
                            className="h-8 w-8 p-0"
                        >
                            <ArrowDownToLine className="w-4 h-4 text-[#6B7280]" />
                        </Button>
                        <MoreVertical className="w-4 h-4 text-[#6B7280]" />
                    </div>
                </CardHeader>
                <CardContent className="px-1 sm:px-2">
                    {!hasOverTimeData ? (
                        <div className="h-48 sm:h-56 md:h-66 min-h-[200px] flex flex-col items-center justify-center text-center">
                            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                            </div>
                            <h3 className="text-sm font-medium text-gray-900 mb-1">No report data available</h3>
                            <p className="text-xs text-gray-500">Reports will appear here once submitted</p>
                        </div>
                    ) : (
                        <ChartContainer
                        config={{
                            reports: {
                                label: "Reports",
                                color: "#BBF49C",
                            },
                        }}
                        className="h-48 sm:h-56 md:h-66 w-full min-h-[200px]"
                    >
                        <AreaChart
                            data={chartData.overTime}
                            margin={{ top: 10, right: 15, left: 0, bottom: 0 }}
                        >
                            <defs>
                                <linearGradient id="colorReports" x1="0" y1="0" x2="0" y2="1">
                                    <stop offset="10%" stopColor="#BBF49C" stopOpacity={0.3} />
                                    <stop offset="90%" stopColor="#BBF49C" stopOpacity={0.05} />
                                </linearGradient>
                            </defs>
                            <CartesianGrid
                                stroke="#E5E7EB"
                                horizontal={true}
                                vertical={false}
                            />
                            <XAxis
                                dataKey="month"
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 8, fill: '#242E2C', fontWeight: 400 }}
                                tickMargin={8}
                                scale="point"
                                padding={{ left: 20, right: 20 }}
                                tickFormatter={(value) => value}
                                className="sm:text-[10px]"
                            />
                            <YAxis
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 8, fill: '#242E2C', fontWeight: 400 }}
                                domain={[0, (dataMax: number) => Math.ceil(dataMax)]}
                                width={30}
                                tickMargin={8}
                                scale="linear"
                                className="sm:text-[10px] sm:w-[40px]"
                            />
                            <ChartTooltip
                                cursor={false}
                                content={<ChartTooltipContent indicator="line" />}
                            />
                            <Area
                                type="monotone"
                                dataKey="reports"
                                stroke="#BBF49C"
                                strokeWidth={2}
                                fill="url(#colorReports)"
                                activeDot={{ r: 5, stroke: "#BBF49C", strokeWidth: 2, fill: "#BBF49C" }}
                            />
                        </AreaChart>
                    </ChartContainer>
                    )}
                </CardContent>
            </Card>

            {/* Report Status Distribution */}
            <Card className="lg:col-span-2">
                <CardHeader className="flex flex-row items-center justify-between gap-2 sm:gap-0">
                    <div>
                        <CardTitle className="text-base sm:text-lg">Report Status Distribution</CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={downloadStatusChart}
                            disabled={!hasStatusData}
                            className="h-8 w-8 p-0"
                        >
                            <ArrowDownToLine className="w-4 h-4 text-[#6B7280]" />
                        </Button>
                        <MoreVertical className="w-4 h-4 text-[#6B7280]" />
                    </div>
                </CardHeader>
                <CardContent>
                    {!hasStatusData ? (
                        <div className="h-48 sm:h-56 md:h-66 min-h-[200px] flex flex-col items-center justify-center text-center">
                            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                </svg>
                            </div>
                            <h3 className="text-sm font-medium text-gray-900 mb-1">No status data available</h3>
                            <p className="text-xs text-gray-500">Report status distribution will appear here</p>
                        </div>
                    ) : (
                        <div className="flex flex-col xl:flex-row xl:items-center xl:justify-between gap-4 xl:gap-10 xl:pl-6">
                            <div className="w-full flex justify-center">
                                <ChartContainer
                                config={{
                                    resolved: {
                                        label: "Resolved",
                                        color: "#1E4841",
                                    },
                                    underReview: {
                                        label: "Under Review",
                                        color: "#ECF4E9",
                                    },
                                    awaitingResponse: {
                                        label: "Awaiting Response",
                                        color: "#BBF49C",
                                    },
                                    new: {
                                        label: "New",
                                        color: "#E5E6E6",
                                    },
                                }}
                                className="w-48 h-48 sm:w-56 sm:h-56 md:w-70 md:h-66 min-h-[200px]"
                            >
                                <PieChart
                                    margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                                >
                                    <ChartTooltip content={<ChartTooltipContent />} />
                                    <Pie
                                        data={chartData.statusDistribution}
                                        cx="50%"
                                        cy="50%"
                                        innerRadius={50}
                                        outerRadius={90}
                                        startAngle={175}
                                        endAngle={535}
                                        paddingAngle={6}
                                        dataKey="value"
                                        cornerRadius={4}
                                        className="sm:inner-radius-[60px] sm:outer-radius-[100px] md:inner-radius-[70px] md:outer-radius-[120px]"
                                    >
                                        {chartData.statusDistribution.map((entry, index: number) => (
                                            <Cell
                                                key={`cell-${index}`}
                                                fill={entry.fill}
                                                radius={4}
                                            />
                                        ))}
                                    </Pie>
                                </PieChart>
                            </ChartContainer>
                        </div>
                        <div className="grid grid-cols-2 xl:flex xl:flex-col gap-2 w-full max-w-md">
                            {chartData.statusDistribution.map((item, index: number) => (
                                <div key={index} className="flex items-center gap-2">
                                    <div
                                        className="w-6 h-3 sm:w-8 sm:h-4 rounded-sm"
                                        style={{ backgroundColor: item.fill }}
                                    ></div>
                                    <span className="text-xs sm:text-sm text-gray-600">{item.name}</span>
                                </div>
                            ))}
                        </div>
                    </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
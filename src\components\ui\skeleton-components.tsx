import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

interface SkeletonProps {
  className?: string;
}

/**
 * Skeleton for statistics cards with icon, title, value, and subtitle
 */
export function StatisticsCardSkeleton({ className }: SkeletonProps) {
  return (
    <Card className={`p-3 sm:p-4 md:p-6 ${className || ''}`}>
      <CardContent className="p-0">
        <div className="flex justify-between">
          <div className="w-full">
            <Skeleton className="h-4 mb-2" />
            <Skeleton className="h-8" />
          </div>
          <Skeleton className="w-10 h-10 rounded-2xl" />
        </div>
        <Skeleton className="mt-4 h-4 w-3/4" />
      </CardContent>
    </Card>
  );
}

/**
 * Skeleton for admin statistics cards with different layout
 */
export function AdminStatisticsCardSkeleton({ className }: SkeletonProps) {
  return (
    <Card className={`bg-white border-0 shadow-sm ${className || ''}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="w-8 h-8 rounded-lg" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-8 mb-1" />
        <Skeleton className="h-3 w-16" />
      </CardContent>
    </Card>
  );
}

/**
 * Skeleton for table rows with customizable number of columns
 */
export function TableRowSkeleton({ 
  columns = 5, 
  className 
}: SkeletonProps & { columns?: number }) {
  return (
    <div className={`flex items-center gap-4 p-4 border rounded-lg ${className || ''}`}>
      {Array.from({ length: columns }).map((_, index) => (
        <Skeleton 
          key={index} 
          className={`h-4 ${
            index === 0 ? 'w-20' : 
            index === columns - 1 ? 'w-16' : 
            index === 1 ? 'flex-1' : 
            'w-24'
          }`} 
        />
      ))}
    </div>
  );
}

/**
 * Skeleton for list items (like recent cases)
 */
export function ListItemSkeleton({ className }: SkeletonProps) {
  return (
    <div className={`flex items-center space-x-4 ${className || ''}`}>
      <Skeleton className="h-4 w-16" />
      <Skeleton className="h-4 flex-1" />
      <Skeleton className="h-4 w-20" />
      <Skeleton className="h-4 w-16" />
    </div>
  );
}

/**
 * Skeleton for chart containers
 */
export function ChartSkeleton({ 
  title, 
  height = "h-48 sm:h-56 md:h-66 min-h-[200px]",
  className 
}: SkeletonProps & { title?: string; height?: string }) {
  return (
    <Card className={className}>
      <CardHeader>
        {title ? (
          <h3 className="text-base sm:text-lg font-semibold">{title}</h3>
        ) : (
          <Skeleton className="h-6 w-32" />
        )}
      </CardHeader>
      <CardContent>
        <div className={`${height} flex items-center justify-center`}>
          <Skeleton className="w-full h-full rounded-lg" />
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Grid of skeleton cards for dashboard statistics
 */
export function StatisticsGridSkeleton({ 
  count = 4, 
  variant = "default",
  className 
}: SkeletonProps & { 
  count?: number; 
  variant?: "default" | "admin" 
}) {
  const SkeletonCard = variant === "admin" ? AdminStatisticsCardSkeleton : StatisticsCardSkeleton;
  
  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6 ${className || ''}`}>
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonCard key={index} />
      ))}
    </div>
  );
}

/**
 * Skeleton for table with header and rows
 */
export function TableSkeleton({ 
  rows = 5, 
  columns = 5,
  className 
}: SkeletonProps & { rows?: number; columns?: number }) {
  return (
    <div className={`space-y-4 ${className || ''}`}>
      {Array.from({ length: rows }).map((_, index) => (
        <TableRowSkeleton key={index} columns={columns} />
      ))}
    </div>
  );
}

/**
 * Skeleton for list with multiple items
 */
export function ListSkeleton({
  items = 5,
  className
}: SkeletonProps & { items?: number }) {
  return (
    <div className={`space-y-4 ${className || ''}`}>
      {Array.from({ length: items }).map((_, index) => (
        <ListItemSkeleton key={index} />
      ))}
    </div>
  );
}

/**
 * Reusable spinner component for loading states
 */
export function LoadingSpinner({
  size = "default",
  color = "primary",
  className,
  text
}: SkeletonProps & {
  size?: "sm" | "default" | "lg";
  color?: "primary" | "secondary" | "white";
  text?: string;
}) {
  const sizeClasses = {
    sm: "w-4 h-4 border-2",
    default: "w-6 h-6 border-2",
    lg: "w-8 h-8 border-2"
  };

  const colorClasses = {
    primary: "border-[#BBF49C] border-t-transparent",
    secondary: "border-[#1E4841] border-t-transparent",
    white: "border-white border-t-transparent"
  };

  return (
    <div className={`flex items-center gap-2 ${className || ''}`}>
      <div
        className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full animate-spin`}
        aria-label="Loading"
      />
      {text && <span className="text-sm text-gray-500">{text}</span>}
    </div>
  );
}

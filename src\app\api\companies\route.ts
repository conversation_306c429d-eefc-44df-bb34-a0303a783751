import { NextRequest, NextResponse } from 'next/server';
import { Company } from '@/lib/db/models';

import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    // Authentication removed - companies data now available without auth
    
    const { searchParams } = new URL(request.url);
    const isActive = searchParams.get('isActive') === 'true';
    
    const query: { isActive?: boolean } = {};
    if (isActive !== null) query.isActive = isActive;
    
    const companies = await Company.find(query)
      .populate('subscriptionPlan', 'name price')
      .sort({ name: 1 });
    
    return NextResponse.json({
      success: true,
      data: companies
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    // Authentication removed - company creation now available without auth
    
    const companyData = await request.json();
    
    if (!companyData.name) {
      return NextResponse.json(
        { success: false, error: 'Company name is required' },
        { status: 400 }
      );
    }
    
    const company = new Company(companyData);
    const savedCompany = await company.save();
    
    return NextResponse.json({
      success: true,
      data: savedCompany
    }, { status: 201 });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
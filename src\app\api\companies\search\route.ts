import { NextRequest, NextResponse } from 'next/server';
import { Company } from '@/lib/db/models';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '10');
    
    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        success: true,
        data: []
      });
    }
    
    // Search companies by name (case-insensitive, partial match)
    const companies = await Company.find({
      name: { $regex: query.trim(), $options: 'i' },
      isActive: true
    })
    .select('_id name industry contactEmail')
    .sort({ name: 1 })
    .limit(limit);
    
    return NextResponse.json({
      success: true,
      data: companies
    });
  } catch (error) {
    console.error('Company search API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

import connectDB from '../mongodb';
import { Report, Message, User } from '../models';
import { generateSecureToken } from '@/lib/utils/crypto';

export async function seedAnonymousReports() {
  try {
    await connectDB();
    console.log('🌱 Starting anonymous reports seeding...');

    // Create a system user for anonymous reports if it doesn't exist
    let systemUser = await User.findOne({ email: '<EMAIL>' });
    if (!systemUser) {
      systemUser = await User.create({
        firstName: 'System',
        lastName: 'Anonymous',
        email: '<EMAIL>',
        role: 'admin', // Use admin role for system user
        isActive: true,
        isVerified: true,
        password: 'system-generated-password', // This won't be used for login
        companyId: null
      });
      console.log('✅ Created system user for anonymous reports');
    }

    // Create admin and investigator users for messaging
    let adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      adminUser = await User.create({
        firstName: 'Admin',
        lastName: 'Team',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true,
        isVerified: true,
        password: 'admin-password',
        companyId: null
      });
      console.log('✅ Created admin user');
    }

    let investigatorUser = await User.findOne({ email: '<EMAIL>' });
    if (!investigatorUser) {
      investigatorUser = await User.create({
        firstName: 'John',
        lastName: 'Investigator',
        email: '<EMAIL>',
        role: 'investigator',
        isActive: true,
        isVerified: true,
        password: 'investigator-password',
        companyId: null
      });
      console.log('✅ Created investigator user');
    }

    // Get current report count for incremental numbering
    const reportCount = await Report.countDocuments();
    const year = new Date().getFullYear();

    // Create sample anonymous reports with different statuses
    const reportStatuses = [
      {
        status: 'Under Review',
        referenceNumber: `RPT-${year}-${(reportCount + 1).toString().padStart(3, '0')}`,
        currentStep: 'under_review'
      },
      {
        status: 'Awaiting Response',
        referenceNumber: `RPT-${year}-${(reportCount + 2).toString().padStart(3, '0')}`,
        currentStep: 'investigation'
      },
      {
        status: 'Resolved',
        referenceNumber: `RPT-${year}-${(reportCount + 3).toString().padStart(3, '0')}`,
        currentStep: 'action_taken'
      },
      {
        status: 'Closed',
        referenceNumber: `RPT-${year}-${(reportCount + 4).toString().padStart(3, '0')}`,
        currentStep: 'closed'
      }
    ];

    for (const reportConfig of reportStatuses) {
      // Check if report already exists
      const existingReport = await Report.findOne({ 
        referenceNumber: reportConfig.referenceNumber 
      });

      if (existingReport) {
        console.log(`⏭️  Report ${reportConfig.referenceNumber} already exists, skipping...`);
        continue;
      }

      // Create the anonymous report
      const report = await Report.create({
        reportId: reportConfig.referenceNumber, // Use the same incremental format
        referenceNumber: reportConfig.referenceNumber,
        userId: systemUser._id,
        companyId: null,
        
        // Basic Information
        title: 'Financial Misconduct Report',
        description: 'Expense reporting irregularities discovered in quarterly financial reviews. Multiple discrepancies found between submitted expense claims and actual vendor invoices.',
        category: 'Financial Misconduct',
        dateOfOccurrence: new Date('2025-03-15'),
        location: 'Headquarters',
        isAnonymous: true,
        
        // Detailed Information
        incidentDate: new Date('2025-03-15'),
        incidentTime: '14:30',
        specificLocation: 'Finance Department - 3rd Floor',
        departmentInvolved: 'Finance',
        peopleInvolved: 'Multiple employees in expense processing',
        
        // Evidence
        hasEvidence: true,
        evidenceDescription: 'Expense claims, email correspondence, and vendor invoices showing discrepancies',
        evidenceFiles: [
          {
            fileName: 'expense_claims_q1.xlsx',
            originalName: 'Expense_Claims_Q1.xlsx',
            fileSize: 245760,
            mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            fileUrl: '/uploads/evidence/expense_claims_q1.xlsx',
            uploadedAt: new Date('2025-04-15T10:30:00Z'),
            uploadedBy: systemUser._id,
            description: 'Quarterly expense claims showing irregularities',
            isEncrypted: true
          },
          {
            fileName: 'email_evidence.pdf',
            originalName: 'Email_Evidence.pdf',
            fileSize: 156432,
            mimeType: 'application/pdf',
            fileUrl: '/uploads/evidence/email_evidence.pdf',
            uploadedAt: new Date('2025-04-15T10:32:00Z'),
            uploadedBy: systemUser._id,
            description: 'Email correspondence regarding expense approvals',
            isEncrypted: true
          },
          {
            fileName: 'invoice_discrepancies.pdf',
            originalName: 'Invoice_Discrepancies.pdf',
            fileSize: 198765,
            mimeType: 'application/pdf',
            fileUrl: '/uploads/evidence/invoice_discrepancies.pdf',
            uploadedAt: new Date('2025-04-15T10:35:00Z'),
            uploadedBy: systemUser._id,
            description: 'Vendor invoices showing discrepancies with expense claims',
            isEncrypted: true
          }
        ],
        
        // Impact Assessment
        urgencyLevel: 'High',
        financialImpact: 'Potential financial loss estimated at $50,000-$100,000',
        operationalImpact: 'Affects financial reporting accuracy and compliance',
        reputationalImpact: 'Could impact stakeholder trust if not addressed',
        
        // System Fields
        priority: 'High',
        status: reportConfig.status,
        isDraft: false,
        dateSubmitted: new Date('2025-04-15T10:00:00Z'),
        submittedAt: new Date('2025-04-15T10:00:00Z'),
        lastUpdated: new Date('2025-04-29T20:23:00Z'),
        
        // Investigation Management
        assignedInvestigator: reportConfig.currentStep !== 'under_review' ? investigatorUser._id : null,
        progress: reportConfig.currentStep === 'closed' ? 100 : 
                 reportConfig.currentStep === 'action_taken' ? 80 :
                 reportConfig.currentStep === 'investigation' ? 50 : 25,
        
        // Status History
        statusHistory: [
          {
            status: 'New',
            changedAt: new Date('2025-04-15T10:00:00Z'),
            changedBy: systemUser._id,
            comment: 'Report submitted anonymously'
          },
          {
            status: 'Under Review',
            changedAt: new Date('2025-04-16T09:00:00Z'),
            changedBy: adminUser._id,
            comment: 'Initial review started by admin team'
          }
        ],
        
        // Anonymous Tracking
        anonymousToken: generateSecureToken(),
        
        // Metadata
        metadata: {
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          submissionMethod: 'web',
          isAnonymous: true,
          sessionId: `anon_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`
        }
      });

      console.log(`✅ Created anonymous report: ${report.referenceNumber}`);

      // Create messages based on report status
      await createMessagesForReport(report, reportConfig.currentStep, systemUser, adminUser, investigatorUser);
    }

    console.log('🎉 Anonymous reports seeding completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ Error seeding anonymous reports:', error);
    throw error;
  }
}

async function createMessagesForReport(report: any, currentStep: string, systemUser: any, adminUser: any, investigatorUser: any) {
  const messages = [];

  // Initial user message (always present)
  messages.push({
    reportId: report._id,
    senderId: systemUser._id,
    content: 'How anonymous is all relevant documentation regarding the financial discrepancies I reported? I want to ensure full confidentiality as I continue to work at the company.',
    messageType: 'user_message',
    isAnonymous: true,
    isEncrypted: true,
    timestamp: new Date('2025-04-28T10:45:00Z'),
    metadata: {
      sentViaToken: true,
      reportToken: report.referenceNumber,
      anonymousSessionId: report.metadata.sessionId
    }
  });

  // Add messages based on current step
  if (currentStep === 'under_review') {
    messages.push({
      reportId: report._id,
      senderId: adminUser._id,
      content: 'Your report is currently under review by the Admin Team. All submitted information has been received and is being reviewed to determine if further investigation is required.',
      messageType: 'system',
      isAnonymous: false,
      isEncrypted: true,
      timestamp: new Date('2025-04-15T12:00:00Z')
    });
  }

  if (currentStep === 'investigation') {
    messages.push({
      reportId: report._id,
      senderId: adminUser._id,
      content: 'Your report is currently under review by the Admin Team. All submitted information has been received and is being reviewed.',
      messageType: 'system',
      isAnonymous: false,
      isEncrypted: true,
      timestamp: new Date('2025-04-15T12:00:00Z')
    });

    messages.push({
      reportId: report._id,
      senderId: investigatorUser._id,
      content: 'Thank you for the detailed clarification. That information is very helpful. Could you also confirm whether these discrepancies were escalated internally before you submitted this report?',
      messageType: 'investigator_message',
      isAnonymous: false,
      isEncrypted: true,
      timestamp: new Date('2025-04-28T11:35:00Z')
    });
  }

  if (currentStep === 'action_taken') {
    messages.push({
      reportId: report._id,
      senderId: adminUser._id,
      content: 'Your report is currently under review by the Admin Team. All submitted information has been received and is being reviewed.',
      messageType: 'system',
      isAnonymous: false,
      isEncrypted: true,
      timestamp: new Date('2025-04-15T12:00:00Z')
    });

    messages.push({
      reportId: report._id,
      senderId: investigatorUser._id,
      content: 'Thank you for the detailed clarification. That information is very helpful. Could you also confirm whether these discrepancies were escalated internally before you submitted this report?',
      messageType: 'investigator_message',
      isAnonymous: false,
      isEncrypted: true,
      timestamp: new Date('2025-04-28T11:35:00Z')
    });

    messages.push({
      reportId: report._id,
      senderId: investigatorUser._id,
      content: 'Following our investigation, we have implemented corrective actions. Enhanced oversight procedures and additional training have been put in place. Thank you for bringing this to our attention.',
      messageType: 'investigator_message',
      isAnonymous: false,
      isEncrypted: true,
      timestamp: new Date('2025-04-30T14:15:00Z')
    });
  }

  if (currentStep === 'closed') {
    messages.push({
      reportId: report._id,
      senderId: adminUser._id,
      content: 'Your report is currently under review by the Admin Team. All submitted information has been received and is being reviewed.',
      messageType: 'system',
      isAnonymous: false,
      isEncrypted: true,
      timestamp: new Date('2025-04-15T12:00:00Z')
    });

    messages.push({
      reportId: report._id,
      senderId: investigatorUser._id,
      content: 'Thank you for the detailed clarification. That information is very helpful. Could you also confirm whether these discrepancies were escalated internally before you submitted this report?',
      messageType: 'investigator_message',
      isAnonymous: false,
      isEncrypted: true,
      timestamp: new Date('2025-04-28T11:35:00Z')
    });

    messages.push({
      reportId: report._id,
      senderId: investigatorUser._id,
      content: 'Following our investigation, we have implemented corrective actions. Enhanced oversight procedures and additional training have been put in place. Thank you for bringing this to our attention.',
      messageType: 'investigator_message',
      isAnonymous: false,
      isEncrypted: true,
      timestamp: new Date('2025-04-30T14:15:00Z')
    });

    messages.push({
      reportId: report._id,
      senderId: adminUser._id,
      content: 'This case has been successfully resolved and closed. All corrective actions have been verified as effective. Thank you for your contribution to maintaining our organizational integrity.',
      messageType: 'system',
      isAnonymous: false,
      isEncrypted: true,
      timestamp: new Date('2025-05-06T10:00:00Z')
    });
  }

  // Create all messages
  for (const messageData of messages) {
    await Message.create(messageData);
  }

  console.log(`✅ Created ${messages.length} messages for report ${report.referenceNumber}`);
}

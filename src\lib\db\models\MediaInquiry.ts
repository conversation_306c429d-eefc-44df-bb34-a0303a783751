import mongoose, { Schema, Document } from 'mongoose';

export interface IMediaInquiry extends Document {
  mediaOrganization: string;
  journalistName: string;
  email: string;
  phoneNumber: string;
  typeOfInquiry: 'interview_request' | 'press_release' | 'company_information' | 'product_demo' | 'expert_commentary' | 'case_study' | 'other';
  deadline: Date;
  detailedMessage: string;
  pressCredentials?: {
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    path: string;
  }[];
  status: 'open' | 'reviewed' | 'in_progress' | 'responded' | 'closed';
  assignedTo?: Schema.Types.ObjectId;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  inquiryNumber: string;
  respondedAt?: Date;
  response?: string;
  notes?: {
    content: string;
    addedBy: Schema.Types.ObjectId;
    addedAt: Date;
  }[];
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    source: string;
    verified: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

const MediaInquirySchema = new Schema<IMediaInquiry>({
  mediaOrganization: {
    type: String,
    required: true,
    minlength: 2,
    maxlength: 200
  },
  journalistName: {
    type: String,
    required: true,
    minlength: 2,
    maxlength: 100
  },
  email: {
    type: String,
    required: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  phoneNumber: {
    type: String,
    required: true,
    minlength: 10,
    maxlength: 20
  },
  typeOfInquiry: {
    type: String,
    enum: ['interview_request', 'press_release', 'company_information', 'product_demo', 'expert_commentary', 'case_study', 'other'],
    required: true
  },
  deadline: {
    type: Date,
    required: true
  },
  detailedMessage: {
    type: String,
    required: true,
    minlength: 20,
    maxlength: 3000
  },
  pressCredentials: [{
    filename: { type: String, required: true },
    originalName: { type: String, required: true },
    mimeType: { type: String, required: true },
    size: { type: Number, required: true },
    path: { type: String, required: true }
  }],
  status: {
    type: String,
    enum: ['open', 'reviewed', 'in_progress', 'responded', 'closed'],
    default: 'open'
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  inquiryNumber: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  respondedAt: {
    type: Date
  },
  response: {
    type: String,
    maxlength: 2000
  },
  notes: [{
    content: {
      type: String,
      required: true,
      maxlength: 1000
    },
    addedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  metadata: {
    userAgent: String,
    ipAddress: String,
    source: {
      type: String,
      default: 'media_inquiry_form'
    },
    verified: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
MediaInquirySchema.index({ email: 1, createdAt: -1 });
MediaInquirySchema.index({ status: 1, priority: -1 });
// inquiryNumber index is already created by unique: true, index: true in field definition
MediaInquirySchema.index({ assignedTo: 1, status: 1 });
MediaInquirySchema.index({ deadline: 1, status: 1 });
MediaInquirySchema.index({ mediaOrganization: 1 });

// Generate inquiry number before saving
MediaInquirySchema.pre('save', async function(next) {
  if (this.isNew) {
    const count = await mongoose.model('MediaInquiry').countDocuments();
    this.inquiryNumber = `MED-${new Date().getFullYear()}-${String(count + 1).padStart(6, '0')}`;
    
    // Auto-set priority based on deadline
    const now = new Date();
    const deadlineDate = new Date(this.deadline);
    const daysDiff = (deadlineDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
    
    if (daysDiff <= 1) {
      this.priority = 'urgent';
    } else if (daysDiff <= 3) {
      this.priority = 'high';
    } else if (daysDiff <= 7) {
      this.priority = 'medium';
    } else {
      this.priority = 'low';
    }
  }
  next();
});

// Virtual for formatted inquiry display
MediaInquirySchema.virtual('formattedInquiry').get(function() {
  return this.inquiryNumber;
});

// Virtual for days until deadline
MediaInquirySchema.virtual('daysUntilDeadline').get(function() {
  const now = new Date();
  const deadline = new Date(this.deadline);
  const daysDiff = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  return daysDiff;
});

// Method to check if inquiry is overdue
MediaInquirySchema.methods.isOverdue = function() {
  if (this.status === 'responded' || this.status === 'closed') return false;
  
  const now = new Date();
  const deadline = new Date(this.deadline);
  
  return now > deadline;
};

// Method to check if deadline is approaching
MediaInquirySchema.methods.isDeadlineApproaching = function() {
  if (this.status === 'responded' || this.status === 'closed') return false;
  
  const now = new Date();
  const deadline = new Date(this.deadline);
  const daysDiff = (deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
  
  return daysDiff <= 2 && daysDiff > 0; // Within 2 days
};

// Method to add note
MediaInquirySchema.methods.addNote = function(content: string, addedBy: Schema.Types.ObjectId) {
  this.notes = this.notes || [];
  this.notes.push({
    content,
    addedBy,
    addedAt: new Date()
  });
  return this.save();
};

// Static method to get statistics
MediaInquirySchema.statics.getStats = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
  
  const priorityStats = await this.aggregate([
    {
      $match: { status: { $in: ['open', 'reviewed', 'in_progress'] } }
    },
    {
      $group: {
        _id: '$priority',
        count: { $sum: 1 }
      }
    }
  ]);
  
  const typeStats = await this.aggregate([
    {
      $match: { status: { $in: ['open', 'reviewed', 'in_progress'] } }
    },
    {
      $group: {
        _id: '$typeOfInquiry',
        count: { $sum: 1 }
      }
    }
  ]);
  
  return { statusStats: stats, priorityStats, typeStats };
};

// Static method to find urgent inquiries
MediaInquirySchema.statics.findUrgent = async function() {
  const now = new Date();
  const twoDaysFromNow = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000);
  
  return this.find({
    status: { $in: ['open', 'reviewed', 'in_progress'] },
    deadline: { $lte: twoDaysFromNow }
  }).populate('assignedTo', 'firstName lastName email').sort({ deadline: 1 });
};

export default mongoose.models.MediaInquiry || mongoose.model<IMediaInquiry>('MediaInquiry', MediaInquirySchema);

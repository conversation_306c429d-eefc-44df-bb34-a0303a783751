export interface ConnectionStats {
  connectionCount: number;
  lastConnectedAt?: Date;
  lastDisconnectedAt?: Date;
  totalReconnections: number;
  authenticationFailures: number;
  isStable: boolean;
  averageConnectionDuration: number;
}

class ConnectionMonitor {
  private stats: ConnectionStats = {
    connectionCount: 0,
    totalReconnections: 0,
    authenticationFailures: 0,
    isStable: true,
    averageConnectionDuration: 0
  };

  private connectionStartTime: Date | null = null;
  private connectionDurations: number[] = [];
  private maxDurationSamples = 10;

  onConnect() {
    this.stats.connectionCount++;
    this.stats.lastConnectedAt = new Date();
    this.connectionStartTime = new Date();
    
    if (this.stats.connectionCount > 1) {
      this.stats.totalReconnections++;
    }

    // Consider connection unstable if more than 3 reconnections in last minute
    this.stats.isStable = this.stats.totalReconnections < 3;
  }

  onDisconnect() {
    this.stats.lastDisconnectedAt = new Date();
    
    if (this.connectionStartTime) {
      const duration = Date.now() - this.connectionStartTime.getTime();
      this.connectionDurations.push(duration);
      
      // Keep only recent samples
      if (this.connectionDurations.length > this.maxDurationSamples) {
        this.connectionDurations.shift();
      }
      
      // Calculate average
      this.stats.averageConnectionDuration = 
        this.connectionDurations.reduce((a, b) => a + b, 0) / this.connectionDurations.length;
    }

    // Short connections indicate instability
    if (this.connectionStartTime && Date.now() - this.connectionStartTime.getTime() < 1000) {
      this.stats.isStable = false;
    }

    this.connectionStartTime = null;
  }

  onAuthError() {
    this.stats.authenticationFailures++;
    this.stats.isStable = false;
  }

  onAuthSuccess() {
    // Authentication successful
  }

  getStats(): ConnectionStats {
    return { ...this.stats };
  }

  reset() {
    this.stats = {
      connectionCount: 0,
      totalReconnections: 0,
      authenticationFailures: 0,
      isStable: true,
      averageConnectionDuration: 0
    };
    this.connectionDurations = [];
    this.connectionStartTime = null;
  }

  // Call this to get a health report
  getHealthReport(): { status: 'healthy' | 'unstable' | 'critical'; issues: string[] } {
    const issues: string[] = [];
    
    if (this.stats.totalReconnections > 5) {
      issues.push(`High reconnection count: ${this.stats.totalReconnections}`);
    }
    
    if (this.stats.authenticationFailures > 2) {
      issues.push(`Multiple auth failures: ${this.stats.authenticationFailures}`);
    }
    
    if (this.stats.averageConnectionDuration < 5000) {
      issues.push(`Short connection duration: ${Math.round(this.stats.averageConnectionDuration)}ms`);
    }

    let status: 'healthy' | 'unstable' | 'critical' = 'healthy';
    if (issues.length > 0) {
      status = issues.length > 2 ? 'critical' : 'unstable';
    }

    return { status, issues };
  }
}

export const connectionMonitor = new ConnectionMonitor();
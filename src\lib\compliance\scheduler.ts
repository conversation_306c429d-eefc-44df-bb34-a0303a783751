import { DataRetentionService } from './dataRetention';

export class ComplianceScheduler {
  private static intervals: NodeJS.Timeout[] = [];

  static startScheduler(): void {
    // Run data retention cleanup daily at 2 AM
    const dailyCleanup = setInterval(async () => {
      const now = new Date();
      if (now.getHours() === 2 && now.getMinutes() === 0) {
        console.log('Starting scheduled data retention cleanup...');
        await DataRetentionService.cleanupExpiredData();
      }
    }, 60000); // Check every minute

    this.intervals.push(dailyCleanup);
    console.log('Compliance scheduler started');
  }

  static stopScheduler(): void {
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];
    console.log('Compliance scheduler stopped');
  }

  static async runManualCleanup(): Promise<void> {
    console.log('Running manual data retention cleanup...');
    await DataRetentionService.cleanupExpiredData();
  }
}
/**
 * Enhanced JWT Manager with Multi-Secret Support
 * 
 * This module provides production-safe JWT token management with:
 * 1. Multiple active JWT secrets simultaneously
 * 2. Automatic fallback for token validation
 * 3. Zero-downtime secret rotation
 * 4. Backward compatibility with existing tokens
 */

import jwt from 'jsonwebtoken';
import crypto from 'crypto';

interface JWTSecretVersion {
  version: string;
  secret: string;
  createdAt: Date;
  isActive: boolean;
  isRetired: boolean;
  description?: string;
}

interface JWTPayload {
  userId: string;
  id: string;
  role: string;
  companyId?: string;
  email: string;
  tokenId?: string;
  iat: number;
  exp: number;
  __jwtVersion?: string; // Internal version tracking
}

class EnhancedJWTManager {
  private secrets: Map<string, JWTSecretVersion> = new Map();
  private currentVersion: string = 'v1';
  private readonly DEFAULT_EXPIRY = '24h';

  constructor() {
    this.initializeSecrets();
  }

  /**
   * Initialize JWT secrets from environment
   */
  private initializeSecrets(): void {
    // Load primary secret
    const primarySecret = process.env.JWT_SECRET;
    if (primarySecret) {
      if (primarySecret === 'your-secret-key-change-in-production') {
        if (process.env.NODE_ENV === 'production') {
          throw new Error('Default JWT_SECRET detected in production - must be changed');
        }
        console.warn('⚠️ Using default JWT_SECRET in development');
      }
      
      this.addSecret('v1', primarySecret, 'Primary JWT secret', true);
    } else if (process.env.NODE_ENV === 'production') {
      throw new Error('JWT_SECRET is required in production');
    } else {
      // Development fallback
      const devSecret = crypto.randomBytes(64).toString('hex');
      this.addSecret('v1', devSecret, 'Development fallback secret', true);
      console.log('🔧 Generated development JWT secret');
    }

    // Load versioned secrets
    this.loadVersionedSecrets();
  }

  /**
   * Load additional versioned secrets from environment
   */
  private loadVersionedSecrets(): void {
    const secretPattern = /^JWT_SECRET_V(\d+)$/;
    
    Object.keys(process.env).forEach(envVar => {
      const match = envVar.match(secretPattern);
      if (match) {
        const version = `v${match[1]}`;
        const secret = process.env[envVar];
        
        if (secret && secret.length >= 32) {
          this.addSecret(version, secret, `Environment secret ${version}`, false);
          console.log(`✅ Loaded JWT secret version ${version}`);
        } else {
          console.warn(`⚠️ Invalid JWT secret for version ${version} - too short`);
        }
      }
    });
  }

  /**
   * Add a new secret version
   */
  private addSecret(version: string, secret: string, description: string = '', isActive: boolean = false): void {
    if (secret.length < 32) {
      throw new Error(`JWT secret must be at least 32 characters long`);
    }

    const secretVersion: JWTSecretVersion = {
      version,
      secret,
      createdAt: new Date(),
      isActive,
      isRetired: false,
      description
    };

    this.secrets.set(version, secretVersion);
    
    if (isActive) {
      // Deactivate previous active secret
      for (const [, existingSecret] of this.secrets) {
        if (existingSecret.isActive && existingSecret.version !== version) {
          existingSecret.isActive = false;
        }
      }
      this.currentVersion = version;
    }
  }

  /**
   * Sign JWT token with current active secret
   */
  signToken(payload: Omit<JWTPayload, 'iat' | 'exp' | '__jwtVersion'>, options: jwt.SignOptions = {}): string {
    const currentSecret = this.getCurrentSecret();
    
    // Add version tracking to payload
    const versionedPayload = {
      ...payload,
      __jwtVersion: this.currentVersion,
      iat: Math.floor(Date.now() / 1000)
    };
    
    return jwt.sign(versionedPayload, currentSecret, {
      expiresIn: this.DEFAULT_EXPIRY,
      ...options
    });
  }

  /**
   * Verify JWT token with automatic fallback
   */
  verifyToken(token: string): { payload: JWTPayload; version: string; isLegacy: boolean } {
    if (!token) {
      throw new Error('Token is required');
    }

    // Try to decode without verification to get version hint
    let tokenVersion = 'v1'; // Default for legacy tokens
    let isLegacy = true;
    
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.__jwtVersion) {
        tokenVersion = decoded.__jwtVersion;
        isLegacy = false;
      }
    } catch {
      // Continue with default version
    }

    // Try to verify with the specific version first
    if (!isLegacy) {
      try {
        const secret = this.getSecretByVersion(tokenVersion);
        const payload = jwt.verify(token, secret) as JWTPayload;
        return { payload, version: tokenVersion, isLegacy: false };
      } catch {
        // Fall through to fallback verification
      }
    }

    // Fallback: try all available secrets
    return this.fallbackTokenVerification(token);
  }

  /**
   * Fallback token verification - try all available secrets
   */
  private fallbackTokenVerification(token: string): { payload: JWTPayload; version: string; isLegacy: boolean } {
    const errors: string[] = [];
    
    // Try secrets in order: current first, then others
    const secretsToTry = Array.from(this.secrets.entries()).sort(([, a], [, b]) => {
      if (a.isActive) return -1;
      if (b.isActive) return 1;
      return b.createdAt.getTime() - a.createdAt.getTime();
    });

    for (const [version, secretVersion] of secretsToTry) {
      if (secretVersion.isRetired) continue;
      
      try {
        const payload = jwt.verify(token, secretVersion.secret) as JWTPayload;
        
        // Log successful fallback for monitoring
        if (version !== this.currentVersion) {
          console.log(`🔄 JWT fallback verification successful with version ${version}`);
        }
        
        return { 
          payload, 
          version, 
          isLegacy: !payload.__jwtVersion 
        };
      } catch (error) {
        errors.push(`${version}: ${error.message}`);
        continue;
      }
    }
    
    throw new Error(`Token verification failed with all available secrets: ${errors.join(', ')}`);
  }

  /**
   * Refresh token with current secret (for migration)
   */
  refreshToken(oldToken: string): { newToken: string; wasLegacy: boolean } {
    const { payload, isLegacy } = this.verifyToken(oldToken);

    // Create new token with current secret and updated timestamp
    const newPayload = {
      userId: payload.userId,
      id: payload.id,
      role: payload.role,
      companyId: payload.companyId,
      email: payload.email,
      tokenId: payload.tokenId
    };

    // Add a small delay to ensure different timestamp
    const newToken = this.signToken(newPayload, {
      expiresIn: '24h',
      // Force new timestamp by adding current time
      jwtid: `refresh_${Date.now()}_${Math.random().toString(36).substring(2)}`
    });

    return { newToken, wasLegacy: isLegacy };
  }

  /**
   * Get current active secret
   */
  private getCurrentSecret(): string {
    const currentSecret = this.secrets.get(this.currentVersion);
    if (!currentSecret || !currentSecret.isActive) {
      throw new Error('No active JWT secret found');
    }
    return currentSecret.secret;
  }

  /**
   * Get secret by version
   */
  private getSecretByVersion(version: string): string {
    const secretVersion = this.secrets.get(version);
    if (!secretVersion) {
      throw new Error(`JWT secret version ${version} not found`);
    }
    if (secretVersion.isRetired) {
      throw new Error(`JWT secret version ${version} is retired`);
    }
    return secretVersion.secret;
  }

  /**
   * Activate a specific secret version
   */
  activateSecret(version: string): void {
    const secretVersion = this.secrets.get(version);
    if (!secretVersion) {
      throw new Error(`JWT secret version ${version} not found`);
    }

    if (secretVersion.isRetired) {
      throw new Error(`JWT secret version ${version} is retired and cannot be activated`);
    }

    // Deactivate current secret
    const currentSecret = this.secrets.get(this.currentVersion);
    if (currentSecret) {
      currentSecret.isActive = false;
    }

    // Activate new secret
    secretVersion.isActive = true;
    this.currentVersion = version;
    
    console.log(`✅ Activated JWT secret version ${version}`);
  }

  /**
   * List available secret versions (without exposing actual secrets)
   */
  listSecrets(): Array<Omit<JWTSecretVersion, 'secret'>> {
    return Array.from(this.secrets.values()).map(secret => ({
      version: secret.version,
      createdAt: secret.createdAt,
      isActive: secret.isActive,
      isRetired: secret.isRetired,
      description: secret.description
    }));
  }

  /**
   * Health check - verify JWT system is working
   */
  healthCheck(): { healthy: boolean; issues: string[]; activeVersion: string } {
    const issues: string[] = [];
    
    // Check if we have an active secret
    const currentSecret = this.secrets.get(this.currentVersion);
    if (!currentSecret || !currentSecret.isActive) {
      issues.push('No active JWT secret found');
    }
    
    // Test token signing and verification
    try {
      const testPayload = {
        userId: 'health-check',
        id: 'health-check',
        role: 'test',
        email: '<EMAIL>'
      };
      
      const testToken = this.signToken(testPayload);
      const verification = this.verifyToken(testToken);
      
      if (verification.payload.userId !== testPayload.userId) {
        issues.push('Token verification test failed');
      }
    } catch (error) {
      issues.push(`Token test failed: ${error.message}`);
    }
    
    return {
      healthy: issues.length === 0,
      issues,
      activeVersion: this.currentVersion
    };
  }

  /**
   * Get current version
   */
  getCurrentVersion(): string {
    return this.currentVersion;
  }
}

// Singleton instance
export const enhancedJWTManager = new EnhancedJWTManager();

// Legacy compatibility functions
export function generateToken(userId: string, userRole?: string, companyId?: string, email?: string): string {
  return enhancedJWTManager.signToken({
    userId,
    id: userId,
    role: userRole || 'whistleblower',
    companyId,
    email: email || ''
  });
}

export function verifyJWTToken(token: string): JWTPayload {
  const { payload } = enhancedJWTManager.verifyToken(token);
  return payload;
}

export { EnhancedJWTManager };

export class IPAnonymizer {
  static anonymizeIPv4(ip: string): string {
    const parts = ip.split('.');
    if (parts.length !== 4) return ip;
    return `${parts[0]}.${parts[1]}.${parts[2]}.0`;
  }

  static anonymizeIPv6(ip: string): string {
    const parts = ip.split(':');
    if (parts.length < 4) return ip;
    return parts.slice(0, 4).join(':') + '::0000';
  }

  static anonymizeIP(ip: string): string {
    if (!ip || ip === 'unknown') return 'anonymous';
    
    if (ip.includes(':')) {
      return this.anonymizeIPv6(ip);
    } else {
      return this.anonymizeIPv4(ip);
    }
  }

  static shouldAnonymize(userRole?: string): boolean {
    return userRole === 'anonymous' || userRole === 'whistleblower';
  }
}
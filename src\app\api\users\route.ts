import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withAuth, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export async function GET(request: AuthenticatedRequest) {
  return withAuth(async (request: AuthenticatedRequest) => {
    try {
      await connectDB();

      // Only admins and investigators can view user lists
      if (request.user?.role !== 'admin' && request.user?.role !== 'investigator') {
        return NextResponse.json(
          { success: false, error: 'Insufficient permissions' },
          { status: 403 }
        );
      }

      // Get users from database (without passwords)
      const users = await DataService.getAllUsers();

      // Format users for display
      const formattedUsers = users.map(user => ({
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        fullName: user.fullName,
        role: user.role,
        companyId: user.companyId,
        isActive: user.isActive,
        createdAt: user.createdAt,
        lastLogin: user.lastLogin
      }));

      return NextResponse.json({
        success: true,
        data: formattedUsers
      });
    } catch {
      return NextResponse.json(
        { success: false, error: 'Failed to fetch users' },
        { status: 500 }
      );
    }
  })(request);
}
import { NextRequest, NextResponse } from 'next/server';
import { emailService } from '@/lib/email/emailService';
import { contactFormSchema } from '@/lib/schemas';

export const runtime = 'nodejs';

import { offices, departments } from '@/lib/staticContent';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    
    if (type === 'offices') {
      return NextResponse.json({
        success: true,
        data: offices
      });
    } else if (type === 'departments') {
      return NextResponse.json({
        success: true,
        data: departments
      });
    } else {
      return NextResponse.json({
        success: true,
        data: {
          offices,
          departments
        }
      });
    }
  } catch {
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the form data
    const validationResult = contactFormSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid form data',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const formData = validationResult.data;

    // Check if SMTP is configured
    const smtpConfigured = (process.env.EMAIL_SERVER_HOST || process.env.SMTP_HOST) && 
                          (process.env.EMAIL_SERVER_USER || process.env.SMTP_USER) && 
                          (process.env.EMAIL_SERVER_PASSWORD || process.env.SMTP_PASS);

    if (!smtpConfigured) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Email service is not configured. Please contact the administrator.' 
        },
        { status: 500 }
      );
    }

    // Test SMTP connection first
    const connectionTest = await emailService.testConnection();
    if (!connectionTest) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Email service is currently unavailable. Please try again later.' 
        },
        { status: 500 }
      );
    }

    // Send the contact form email to the admin/support team
    const emailSent = await emailService.sendContactFormEmail(formData);
    
    if (!emailSent) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to send your message. Please try again later.' 
        },
        { status: 500 }
      );
    }

    // Send auto-reply to the user (optional, don't fail if this fails)
    try {
      await emailService.sendAutoReplyEmail(formData);
    } catch {
      // Don't fail the main request if auto-reply fails
    }

    return NextResponse.json({
      success: true,
      message: 'Your message has been sent successfully. We will get back to you soon!'
    });

  } catch {
    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred. Please try again later.' 
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
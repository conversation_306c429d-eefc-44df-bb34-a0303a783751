import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs';

// Hardcoded testimonials data
const HARDCODED_TESTIMONIALS = [
  {
    id: 'test-1',
    name: '<PERSON>',
    position: 'HR Manager',
    company: 'Tech Corp Inc.',
    content: 'The whistleblower platform provided a safe and secure way for our employees to report concerns. The process was straightforward and confidential.',
    rating: 5,
    category: 'enterprise',
    isActive: true,
    createdAt: new Date('2024-01-15')
  },
  {
    id: 'test-2',
    name: '<PERSON>',
    position: 'Compliance Officer',
    company: 'Global Finance Ltd.',
    content: 'Excellent platform with robust reporting features. The investigation tools helped us resolve issues quickly and effectively.',
    rating: 5,
    category: 'compliance',
    isActive: true,
    createdAt: new Date('2024-01-10')
  },
  {
    id: 'test-3',
    name: 'Anonymous Employee',
    position: 'Software Developer',
    company: 'StartupXYZ',
    content: 'I felt safe reporting unethical behavior through this platform. The anonymity protection gave me confidence to speak up.',
    rating: 4,
    category: 'protection',
    isActive: true,
    createdAt: new Date('2024-01-05')
  },
  {
    id: 'test-4',
    name: 'Dr. <PERSON>',
    position: 'Ethics Director',
    company: 'Healthcare Solutions',
    content: 'The platform has transformed how we handle ethics concerns. The reporting dashboard provides valuable insights for management.',
    rating: 5,
    category: 'healthcare',
    isActive: true,
    createdAt: new Date('2024-01-01')
  },
  {
    id: 'test-5',
    name: 'James Wilson',
    position: 'Legal Counsel',
    company: 'Manufacturing Co.',
    content: 'Great tool for maintaining compliance and ensuring workplace safety. The audit trial features are particularly useful.',
    rating: 4,
    category: 'legal',
    isActive: true,
    createdAt: new Date('2023-12-28')
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    
    let filteredTestimonials = HARDCODED_TESTIMONIALS.filter(testimonial => testimonial.isActive);
    
    if (category) {
      filteredTestimonials = filteredTestimonials.filter(
        testimonial => testimonial.category === category
      );
    }
    
    // Sort by creation date (newest first)
    filteredTestimonials.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    
    return NextResponse.json({
      success: true,
      data: filteredTestimonials
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
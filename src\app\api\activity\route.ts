import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
        
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '5');
    
    // Use the authenticated user's ID
    const userId = request.user!.id;
    const companyId = request.user!.companyId;
    
    const recentActivity = await DataService.getRecentActivity(userId, companyId, limit);
    
    return NextResponse.json({
      success: true,
      data: recentActivity
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});
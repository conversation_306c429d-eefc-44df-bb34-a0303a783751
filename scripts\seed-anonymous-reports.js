#!/usr/bin/env node

/**
 * Script to seed anonymous reports data to the database
 * Run with: node scripts/seed-anonymous-reports.js
 */

const { seedAnonymousReports } = require('../src/lib/db/seeds/anonymous-reports.ts');

async function main() {
  try {
    console.log('🌱 Starting anonymous reports seeding process...');
    
    await seedAnonymousReports();
    
    console.log('✅ Anonymous reports seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

main();

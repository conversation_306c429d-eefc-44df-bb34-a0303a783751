"use client";
import Image from "next/image";
import { WORKFLOW_STEPS } from "@/lib/mockData";

export default function HowWeWork() {
  const steps = [...WORKFLOW_STEPS];

  return (
    <section className="w-full flex flex-col lg:flex-row items-center lg:items-start justify-between bg-[#ECF4E9] text-[#1E4841] px-8 md:px-16 lg:px-24 xl:px-44 py-12 lg:py-16 xl:py-20 gap-8 lg:gap-12 xl:gap-16 mt-10 xl:mt-20">
      {/* Left side - Image */}
      <div className="flex-shrink-0 w-full lg:w-auto flex justify-center lg:justify-start">
        <div className="relative">
          <Image
            src="/desktop/pricing/how-we-work/hero.svg"
            alt="Professional woman working on laptop - How We Work Process Illustration"
            width={435}
            height={534}
            className="w-full max-w-sm lg:max-w-none lg:w-96 xl:w-[435px] h-auto rounded-lg"
            priority
          />
        </div>
      </div>

      {/* Right side - Content */}
      <div className="flex-1 flex flex-col items-start justify-start max-w-2xl">
        {/* Header */}
        <div className="mb-8 lg:mb-12">
          <p className="text-base lg:text-lg font-semibold text-[#1E4841] mb-2">How We Work!</p>
          <h2 className="text-2xl md:text-3xl lg:text-4xl xl:text-[40px] font-bold text-[#1E4841] leading-tight">
            Empowering Transparency,
            <br />
            Protecting Identities
          </h2>
        </div>

        {/* Steps */}
        <div className="w-full">
          {steps.map((step, index) => {
            const isLast = index === steps.length - 1;

            return (
              <div key={`step-${index}`} className="relative group">
                <div className="flex gap-4 lg:gap-6">
                  {/* Timeline dot */}
                  <div className="relative flex-shrink-0 mt-1">
                    <div className="w-4 h-4 rounded-full bg-[#1E4841] transition-all duration-300 group-hover:scale-110 relative z-10" />
                    <div className="absolute -top-2 -left-2 w-8 h-8 rounded-full ring-2 ring-[#1E4841]/20 transition-all duration-300 group-hover:ring-[#1E4841]/40" />
                  </div>

                  {/* Content */}
                  <div className="flex-1 pb-8 lg:pb-12">
                    <h3 className="font-bold text-lg lg:text-xl text-[#1E4841] mb-2">
                      {step.title}
                    </h3>
                    <p className="text-sm lg:text-base text-[#6B7271] leading-relaxed">
                      {step.description}
                    </p>
                  </div>
                </div>

                {/* Connecting line */}
                {!isLast && (
                  <div className="absolute left-2 top-6 w-0.5 h-16 lg:h-20 bg-gradient-to-b from-[#1E4841] to-[#1E4841]/30 -ml-px" />
                )}
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
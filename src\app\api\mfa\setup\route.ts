import { NextRequest, NextResponse } from 'next/server';
import { MFAService } from '@/lib/auth/mfa';

export async function POST(request: NextRequest) {
  try {
    const { userId, email } = await request.json();
    
    if (!userId || !email) {
      return NextResponse.json({ error: 'User ID and email required' }, { status: 400 });
    }

    const mfaSetup = await MFAService.setupMFA(userId, email);
    
    return NextResponse.json({
      success: true,
      ...mfaSetup
    });
  } catch (error) {
    console.error('MFA setup error:', error);
    return NextResponse.json({ error: 'MFA setup failed' }, { status: 500 });
  }
}
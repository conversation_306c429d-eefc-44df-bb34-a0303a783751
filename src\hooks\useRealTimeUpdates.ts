"use client";

import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './useAuth';

interface RealTimeStats {
  totalReports: number;
  newReports: number;
  underReviewReports: number;
  awaitingResponseReports: number;
  resolvedReports: number;
  highPriorityReports: number;
  periodComparison: {
    totalReportsChange: number;
    newReportsChange: number;
    resolvedReportsChange: number;
    period: string;
  };
  lastCalculated: Date;
}

interface RealTimeMessage {
  _id: string;
  content: string;
  senderId: string;
  messageType: 'text' | 'file' | 'system';
  timestamp: Date;
}

interface RealTimeNotification {
  _id: string;
  type: string;
  title: string;
  message: string;
  priority: string;
  status: string;
  createdAt: Date;
}

interface UseRealTimeUpdatesReturn {
  socket: Socket | null;
  isConnected: boolean;
  stats: RealTimeStats | null;
  notifications: RealTimeNotification[];
  joinConversation: (conversationId: string) => void;
  leaveConversation: (conversationId: string) => void;
  sendMessage: (conversationId: string, content: string, messageType?: 'text' | 'file' | 'system') => void;
  markMessageAsRead: (messageId: string) => void;
  startTyping: (conversationId: string) => void;
  stopTyping: (conversationId: string) => void;
  requestStats: () => void;
  requestNotifications: () => void;
  connectedUsers: number;
}

export function useRealTimeUpdates(): UseRealTimeUpdatesReturn {
  const { user, isAuthenticated } = useAuth();
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [stats, setStats] = useState<RealTimeStats | null>(null);
  const [notifications, setNotifications] = useState<RealTimeNotification[]>([]);
  const [connectedUsers, setConnectedUsers] = useState(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!isAuthenticated || !user) {
      // Disconnect if user is not authenticated
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
        setIsConnected(false);
      }
      return;
    }

    // Create socket connection
    const token = localStorage.getItem('auth_token');
    if (!token) {
      setIsConnected(false);
      return;
    }

    const socket = io(process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'http://localhost:3002', {
      auth: {
        token: token
      },
      transports: ['websocket'],
      upgrade: false,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    });

    socketRef.current = socket;

    // Connection event handlers
    socket.on('connect', () => {
      setIsConnected(true);
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    });

    socket.on('disconnect', () => {
      setIsConnected(false);
      setConnectedUsers(0);
    });

    socket.on('connect_error', () => {
      setIsConnected(false);
    });

    socket.on('reconnect', () => {
      setIsConnected(true);
    });

    socket.on('reconnect_error', (error) => {
      console.error('🔌 Reconnection error:', error);
      setIsConnected(false);
    });

    socket.on('reconnect_failed', () => {
      console.error('🔌 Reconnection failed');
      setIsConnected(false);
    });

    socket.on('connection-established', (data) => {
      console.log('✅ Real-time connection established:', data);
      // Request initial data
      socket.emit('request-stats');
      socket.emit('request-notifications');
    });

    // Stats updates
    socket.on('stats-updated', (data: { stats: RealTimeStats; timestamp: string }) => {
      console.log('📊 Stats updated:', data);
      setStats(data.stats);
    });

    // Notification updates
    socket.on('notification-received', (data: { notification: RealTimeNotification }) => {
      console.log('🔔 New notification:', data);
      setNotifications(prev => [data.notification, ...prev]);
    });

    socket.on('notifications-updated', (data: { notifications: RealTimeNotification[] }) => {
      console.log('🔔 Notifications updated:', data);
      setNotifications(data.notifications);
    });

    // Message updates
    socket.on('new-message', (data: { conversationId: string; message: RealTimeMessage }) => {
      console.log('💬 New message received:', data);
      // Emit custom event for components to listen to
      window.dispatchEvent(new CustomEvent('new-real-time-message', { detail: data }));
    });

    // Report status updates
    socket.on('report-status-updated', (data: { reportId: string; status: string; timestamp: string }) => {
      console.log('📋 Report status updated:', data);
      // Emit custom event for components to listen to
      window.dispatchEvent(new CustomEvent('report-status-updated', { detail: data }));
    });

    // Recent activity updates
    socket.on('recent-activity-updated', (data: { activity: unknown; timestamp: string }) => {
      console.log('📈 Recent activity updated:', data);
      // Emit custom event for components to listen to
      window.dispatchEvent(new CustomEvent('recent-activity-updated', { detail: data }));
    });

    // Typing indicators
    socket.on('user-typing', (data: { userId: string; conversationId: string; isTyping: boolean }) => {
      console.log('⌨️ User typing:', data);
      // Emit custom event for components to listen to
      window.dispatchEvent(new CustomEvent('user-typing', { detail: data }));
    });

    // Message read status
    socket.on('message-read', (data: { messageId: string; readBy: string; readAt: Date }) => {
      console.log('👁️ Message read:', data);
      // Emit custom event for components to listen to
      window.dispatchEvent(new CustomEvent('message-read', { detail: data }));
    });

    // User activity updates
    socket.on('user-activity-update', (data: { userId: string; activity: string; timestamp: Date }) => {
      console.log('👤 User activity:', data);
      // Emit custom event for components to listen to
      window.dispatchEvent(new CustomEvent('user-activity-update', { detail: data }));
    });

    // Report status updates
    socket.on('realtime_event', (eventData: { type: string; data: { reportId: string; oldStatus: string; newStatus: string; reportTitle: string; userId: string }; timestamp: string }) => {
      if (eventData.type === 'report_status_updated') {
        console.log('📋 Report status updated:', eventData.data);
        // Show notification to the report owner
        if (eventData.data.userId === user.id) {
          window.dispatchEvent(new CustomEvent('report-status-updated', { 
            detail: {
              reportId: eventData.data.reportId,
              oldStatus: eventData.data.oldStatus,
              newStatus: eventData.data.newStatus,
              reportTitle: eventData.data.reportTitle,
              timestamp: eventData.timestamp
            }
          }));
        }
      }
    });

    // Error handlers
    socket.on('stats-error', () => {
      // Handle stats errors silently
    });

    socket.on('notifications-error', () => {
      // Handle notification errors silently
    });

    socket.on('message-error', () => {
      // Handle message errors silently
    });

    // Cleanup on unmount
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (socket) {
        socket.disconnect();
      }
    };
  }, [isAuthenticated, user]);

  // Helper functions
  const joinConversation = (conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('join-conversation', conversationId);
    }
  };

  const leaveConversation = (conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('leave-conversation', conversationId);
    }
  };

  const sendMessage = (conversationId: string, content: string, messageType: 'text' | 'file' | 'system' = 'text') => {
    if (socketRef.current) {
      socketRef.current.emit('send-message', {
        conversationId,
        content,
        messageType
      });
    }
  };

  const markMessageAsRead = (messageId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('mark-message-read', { messageId });
    }
  };

  const startTyping = (conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('typing-start', { conversationId });
    }
  };

  const stopTyping = (conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('typing-stop', { conversationId });
    }
  };

  const requestStats = () => {
    if (socketRef.current) {
      socketRef.current.emit('request-stats');
    }
  };

  const requestNotifications = () => {
    if (socketRef.current) {
      socketRef.current.emit('request-notifications');
    }
  };

  return {
    socket: socketRef.current,
    isConnected,
    stats,
    notifications,
    joinConversation,
    leaveConversation,
    sendMessage,
    markMessageAsRead,
    startTyping,
    stopTyping,
    requestStats,
    requestNotifications,
    connectedUsers
  };
}
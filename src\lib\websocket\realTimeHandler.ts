import { Server as SocketIOServer, Socket } from 'socket.io';
import { RealTimeStatsUpdater } from '@/lib/realtime/statsUpdater';
import jwt from 'jsonwebtoken';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: string;
  companyId?: string;
}

export function setupRealTimeHandlers(io: SocketIOServer) {
  // Set the socket server for the stats updater
  RealTimeStatsUpdater.setSocketServer(io);

  // Authentication middleware
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        return next(new Error('Authentication token required'));
      }

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as { id: string; role: string; companyId: string };
      
      socket.userId = decoded.id;
      socket.userRole = decoded.role;
      socket.companyId = decoded.companyId;
      
      next();
    } catch (error) {
      console.error('Socket authentication error:', error);
      next(new Error('Authentication failed'));
    }
  });

  io.on('connection', (socket: AuthenticatedSocket) => {
    console.log(`🔌 User connected: ${socket.userId} (${socket.userRole})`);

    // Join user-specific room
    if (socket.userId) {
      socket.join(`user-${socket.userId}`);
      console.log(`👤 User ${socket.userId} joined personal room`);
    }

    // Join company room for company-wide updates
    if (socket.companyId) {
      socket.join(`company-${socket.companyId}`);
      console.log(`🏢 User ${socket.userId} joined company room: ${socket.companyId}`);
    }

    // Handle joining conversation rooms
    socket.on('join-conversation', (conversationId: string) => {
      socket.join(`conversation-${conversationId}`);
      console.log(`💬 User ${socket.userId} joined conversation: ${conversationId}`);
    });

    // Handle leaving conversation rooms
    socket.on('leave-conversation', (conversationId: string) => {
      socket.leave(`conversation-${conversationId}`);
      console.log(`💬 User ${socket.userId} left conversation: ${conversationId}`);
    });

    // Handle real-time message sending
    socket.on('send-message', async (data: {
      conversationId: string;
      content: string;
      messageType?: 'text' | 'file' | 'system';
    }) => {
      try {
        // Import DataService dynamically to avoid circular dependencies
        const { DataService } = await import('@/lib/db/dataService');
        
        // Create the message in the database
        await DataService.createMessage({
          conversationId: data.conversationId,
          senderId: socket.userId!,
          content: data.content,
          messageType: data.messageType || 'text'
        });

        // The real-time broadcast is handled in the DataService.createMessage method
        console.log(`📨 Message sent by ${socket.userId} in conversation ${data.conversationId}`);
      } catch (error) {
        console.error('Error sending message:', error);
        socket.emit('message-error', { error: 'Failed to send message' });
      }
    });

    // Handle typing indicators
    socket.on('typing-start', (data: { conversationId: string }) => {
      socket.to(`conversation-${data.conversationId}`).emit('user-typing', {
        userId: socket.userId,
        conversationId: data.conversationId,
        isTyping: true
      });
    });

    socket.on('typing-stop', (data: { conversationId: string }) => {
      socket.to(`conversation-${data.conversationId}`).emit('user-typing', {
        userId: socket.userId,
        conversationId: data.conversationId,
        isTyping: false
      });
    });

    // Handle marking messages as read
    socket.on('mark-message-read', async (data: { messageId: string }) => {
      try {
        const { DataService } = await import('@/lib/db/dataService');
        await DataService.markMessageAsRead(data.messageId, socket.userId!);
        
        // Broadcast read status to other participants
        socket.broadcast.emit('message-read', {
          messageId: data.messageId,
          readBy: socket.userId,
          readAt: new Date()
        });
      } catch (error) {
        console.error('Error marking message as read:', error);
      }
    });

    // Handle requesting latest stats
    socket.on('request-stats', async () => {
      try {
        const { DataService } = await import('@/lib/db/dataService');
        
        // Get user-specific or company-wide stats based on role
        const userId = socket.userRole === 'whistleblower' ? socket.userId : undefined;
        const stats = await DataService.getDashboardStats(userId, socket.companyId);
        
        socket.emit('stats-updated', {
          stats,
          timestamp: new Date().toISOString(),
          userId: socket.userId,
          companyId: socket.companyId
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
        socket.emit('stats-error', { error: 'Failed to fetch stats' });
      }
    });

    // Handle requesting notifications
    socket.on('request-notifications', async () => {
      try {
        const { DataService } = await import('@/lib/db/dataService');
        const notifications = await DataService.getNotifications(socket.userId!);
        
        socket.emit('notifications-updated', {
          notifications,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error fetching notifications:', error);
        socket.emit('notifications-error', { error: 'Failed to fetch notifications' });
      }
    });

    // Handle user activity updates
    socket.on('user-activity', async (data: { activity: string }) => {
      try {
        // Update user's last active timestamp
        const { DataService } = await import('@/lib/db/dataService');
        await DataService.updateUser(socket.userId!, { lastActive: new Date() });
        
        // Broadcast activity to company room (for admin visibility)
        if (socket.companyId) {
          socket.to(`company-${socket.companyId}`).emit('user-activity-update', {
            userId: socket.userId,
            activity: data.activity,
            timestamp: new Date()
          });
        }
      } catch (error) {
        console.error('Error updating user activity:', error);
      }
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      console.log(`User disconnected: ${socket.userId} (${reason})`);

      // Update user's last active timestamp
      if (socket.userId) {
        import('@/lib/db/dataService').then(({ DataService }) => {
          DataService.updateUser(socket.userId!, { lastActive: new Date() });
        }).catch(error => {
          console.error('Error updating last active on disconnect:', error);
        });
      }
    });

    // Send initial data to newly connected user
    socket.emit('connection-established', {
      userId: socket.userId,
      userRole: socket.userRole,
      companyId: socket.companyId,
      timestamp: new Date().toISOString()
    });
  });


}

// Helper function to get connected users count
export function getConnectedUsersCount(io: SocketIOServer): number {
  return io.engine.clientsCount;
}

// Helper function to get users in a specific room
export function getUsersInRoom(io: SocketIOServer, roomName: string): string[] {
  const room = io.sockets.adapter.rooms.get(roomName);
  return room ? Array.from(room) : [];
}
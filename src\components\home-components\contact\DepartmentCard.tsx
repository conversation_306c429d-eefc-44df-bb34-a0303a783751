"use client";

import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Phone, Mail, Clock } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface DepartmentCardProps {
  icon: string;
  title: string;
  description: string;
  phone: string;
  email: string;
  hours: string;
  cta: string;
  onCtaClick?: () => void;
}

export default function DepartmentCard({
  icon,
  title,
  description,
  phone,
  email,
  hours,
  cta,
  onCtaClick
}: DepartmentCardProps) {
  return (
    <Card className="h-full shadow-none border-none hover:shadow-md transition-shadow duration-300 bg-[#F4F6F5]">
      <CardContent className="px-6 flex flex-col h-full">
        <div className="flex-1">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-[#ECF4E9] mb-1 sm:mb-2 flex items-center justify-center">
              <Image
                src={icon}
                alt={title}
                width={32}
                height={32}
                className="w-5 h-full"
              />
            </div>
            <div className="flex flex-col ml-3">
              <h3 className="text-xl font-semibold text-[#242E2C]">
                {title}
              </h3>
              <p className="text-base text-[#6B7271]">
                {description}
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <Mail className="w-3.5 h-3.5 text-[#242E2C] flex-shrink-0 mt-1" />
              <div className="flex flex-col">
                <p className="text-base text-[#242E2C] font-medium">Email</p>
                <Link
                  href={`mailto:${email}`}
                  className="text-sm text-[#6B7271] hover:underline break-all"
                >
                  {email}
                </Link>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Phone className="w-3.5 h-3.5 text-[#242E2C] flex-shrink-0 mt-1" />
              <div className="flex flex-col">
                <p className="text-base text-[#242E2C] font-medium">Phone</p>
                <Link
                  href={`tel:${phone}`}
                  className="text-sm text-[#6B7271] hover:underline"
                >
                  {phone}
                </Link>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Clock className="w-3.5 h-3.5 text-[#242E2C] mt-1 flex-shrink-0" />
              <div className="flex flex-col">
                <p className="text-base text-[#242E2C] font-medium">Response Time</p>
                <p className="text-base text-[#6B7271]">
                  {hours}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 space-y-2">
          {onCtaClick ? (
            <Button
              onClick={onCtaClick}
              className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90 text-white"
            >
              {cta}
            </Button>
          ) : (
            <Link href={`mailto:${email}`}>
              <Button className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90 text-white">
                {cta}
              </Button>
            </Link>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
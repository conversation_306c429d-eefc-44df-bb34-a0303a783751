import { NextRequest, NextResponse } from 'next/server';
import { seedAnonymousReports } from '@/lib/db/seeds/anonymous-reports';

export const runtime = 'nodejs';

export async function POST(_request: NextRequest) {
  try {
    // Only allow in development environment
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { success: false, error: 'Seeding is not allowed in production' },
        { status: 403 }
      );
    }

    console.log('🌱 Starting anonymous reports seeding...');
    
    await seedAnonymousReports();
    
    return NextResponse.json({
      success: true,
      message: 'Anonymous reports seeded successfully'
    });

  } catch (error) {
    console.error('❌ Error seeding anonymous reports:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to seed anonymous reports',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST method to seed anonymous reports',
    endpoint: '/api/seed/anonymous-reports',
    method: 'POST'
  });
}

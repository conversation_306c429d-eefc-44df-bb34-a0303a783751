"use client";

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Notification } from '@/lib/types';
import { apiClient } from '@/lib/api/client';

interface NotificationContextType {
    notifications: Notification[];
    unreadCount: number;
    isLoading: boolean;
    error: string | null;
    markAsRead: (notificationId: string) => Promise<void>;
    markAllAsRead: () => Promise<void>;
    refresh: () => void;
    createWelcomeNotification: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function NotificationProvider({ children }: { children: React.ReactNode }) {
    const { user } = useAuth();
    const [notifications, setNotifications] = useState<Notification[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchNotifications = useCallback(async () => {
        if (!user?.id) {
            setNotifications([]);
            setIsLoading(false);
            return;
        }

        try {
            setIsLoading(true);
            setError(null);

            const data = await apiClient.get(`/api/notifications?userId=${user.id}&limit=50`) as {
                success: boolean;
                data: Notification[];
                error?: string;
            };

            if (data.success) {
                // Ensure notifications are sorted by newest first (reverse chronological)
                const sortedNotifications = (data.data || []).sort((a, b) => {
                    const dateA = new Date(a.createdAt).getTime();
                    const dateB = new Date(b.createdAt).getTime();
                    return dateB - dateA;
                });
                setNotifications(sortedNotifications);
            } else {
                setError(data.error || 'Failed to fetch notifications');
                setNotifications([]);
            }
        } catch (error) {
            console.error('Error fetching notifications:', error);
            setError(error instanceof Error ? error.message : 'Failed to fetch notifications');
            setNotifications([]);
        } finally {
            setIsLoading(false);
        }
    }, [user?.id]); // Keep the same dependency for now

    useEffect(() => {
        fetchNotifications();
        
        const interval = setInterval(fetchNotifications, 30000);
        
        return () => clearInterval(interval);
    }, [fetchNotifications]);

    useEffect(() => {
        const handleReportSubmitted = () => setTimeout(fetchNotifications, 1000);
        const handleMessageSent = () => setTimeout(fetchNotifications, 1000);
        const handleStatusUpdate = () => setTimeout(fetchNotifications, 1000);
        const handleRefreshNotifications = () => fetchNotifications();

        window.addEventListener('report-submitted', handleReportSubmitted);
        window.addEventListener('message-sent', handleMessageSent);
        window.addEventListener('status-updated', handleStatusUpdate);
        window.addEventListener('refresh-notifications', handleRefreshNotifications);

        return () => {
            window.removeEventListener('report-submitted', handleReportSubmitted);
            window.removeEventListener('message-sent', handleMessageSent);
            window.removeEventListener('status-updated', handleStatusUpdate);
            window.removeEventListener('refresh-notifications', handleRefreshNotifications);
        };
    }, [fetchNotifications]);

    const markAsRead = async (notificationId: string) => {
        try {
            await apiClient.put(`/api/notifications/${notificationId}/mark-read`);

            setNotifications(prev => 
                prev.map(notification => 
                    notification._id === notificationId 
                        ? { ...notification, status: 'read' as const }
                        : notification
                )
            );
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    };

    const markAllAsRead = async () => {
        try {
            await apiClient.put(`/api/notifications/mark-all-read`, { userId: user?.id });

            setNotifications(prev => 
                prev.map(notification => ({ ...notification, status: 'read' as const }))
            );
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
        }
    };

    const createWelcomeNotification = async () => {
        try {
            await apiClient.post('/api/notifications/welcome');
            setTimeout(fetchNotifications, 500);
        } catch (error) {
            console.error('Error creating welcome notification:', error);
        }
    };

    const refresh = () => {
        fetchNotifications();
    };

    const unreadCount = notifications.filter(n => n.status === 'unread').length;

    return (
        <NotificationContext.Provider value={{
            notifications,
            unreadCount,
            isLoading,
            error,
            markAsRead,
            markAllAsRead,
            refresh,
            createWelcomeNotification
        }}>
            {children}
        </NotificationContext.Provider>
    );
}

export function useNotificationContext() {
    const context = useContext(NotificationContext);
    if (context === undefined) {
        throw new Error('useNotificationContext must be used within a NotificationProvider');
    }
    return context;
}
import connectDB from '@/lib/db/mongodb';
import type { Model, Types } from 'mongoose';

export interface RetentionPolicy {
  dataType: 'reports' | 'messages' | 'audit_logs' | 'user_data';
  retentionPeriodDays: number;
  autoDelete: boolean;
  archiveBeforeDelete: boolean;
}

export class DataRetentionService {
  private static policies: RetentionPolicy[] = [
    { dataType: 'reports', retentionPeriodDays: 2555, autoDelete: false, archiveBeforeDelete: true },
    { dataType: 'messages', retentionPeriodDays: 1095, autoDelete: true, archiveBeforeDelete: true },
    { dataType: 'audit_logs', retentionPeriodDays: 2555, autoDelete: false, archiveBeforeDelete: true },
    { dataType: 'user_data', retentionPeriodDays: 365, autoDelete: false, archiveBeforeDelete: false }
  ];

  static async cleanupExpiredData(): Promise<void> {
    await connectDB();
    
    for (const policy of this.policies) {
      await this.processRetentionPolicy(policy);
    }
  }

  private static async processRetentionPolicy(policy: RetentionPolicy): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - policy.retentionPeriodDays);

    const expiredData = await this.findExpiredData(policy.dataType, cutoffDate);
    
    if (expiredData.length === 0) return;

    if (policy.archiveBeforeDelete) {
      await this.archiveData(policy.dataType, expiredData);
    }

    if (policy.autoDelete) {
      await this.deleteData(policy.dataType, expiredData);
    }

    console.log(`Processed ${expiredData.length} expired ${policy.dataType} records`);
  }

  private static async findExpiredData(dataType: RetentionPolicy['dataType'], cutoffDate: Date): Promise<Array<Record<string, unknown>>> {
    switch (dataType) {
      case 'reports':
        const Report = (await import('@/lib/db/models/Report')).default;
        return await Report.find({ createdAt: { $lt: cutoffDate } }).lean();
      
      case 'messages':
        const Message = (await import('@/lib/db/models/Message')).default;
        return await Message.find({ createdAt: { $lt: cutoffDate } }).lean();
      
      case 'user_data':
        const User = (await import('@/lib/db/models/User')).default;
        return await User.find({ 
          lastLogin: { $lt: cutoffDate },
          isActive: false 
        }).lean();
      
      case 'audit_logs':
        const mongooseMod = await import('mongoose');
        const { Schema } = mongooseMod;
        interface AuditLogDoc {
          _id: Types.ObjectId;
          userId?: Types.ObjectId;
          action?: string;
          timestamp: Date;
          details?: unknown;
        }
        const AuditLogSchema = new Schema<AuditLogDoc>({
          userId: Schema.Types.ObjectId,
          action: String,
          timestamp: { type: Date, default: Date.now },
          details: Schema.Types.Mixed
        });
        const AuditLogModel = (mongooseMod.models.AuditLog as Model<AuditLogDoc>) || (mongooseMod.model<AuditLogDoc>('AuditLog', AuditLogSchema));
        return await AuditLogModel.find({ timestamp: { $lt: cutoffDate } }).lean();
      
      default:
        return [];
    }
  }

  private static async archiveData(dataType: RetentionPolicy['dataType'], data: Array<Record<string, unknown>>): Promise<void> {
    const { Schema, model, models } = await import('mongoose');
    
    interface ArchiveDoc {
      dataType: string;
      originalId: Types.ObjectId;
      data: unknown;
      archivedAt: Date;
    }
    const ArchiveSchema = new Schema<ArchiveDoc>({
      dataType: String,
      originalId: Schema.Types.ObjectId,
      data: Schema.Types.Mixed,
      archivedAt: { type: Date, default: Date.now }
    });
    
    const Archive = (models.Archive as Model<ArchiveDoc>) || model<ArchiveDoc>('Archive', ArchiveSchema);
    
    const archiveRecords: ArchiveDoc[] = data.map(item => ({
      dataType,
      originalId: item._id as Types.ObjectId,
      data: item as unknown,
      archivedAt: new Date()
    }));
    
    await Archive.insertMany(archiveRecords);
    console.log(`Archived ${data.length} ${dataType} records`);
  }

  private static async deleteData(dataType: RetentionPolicy['dataType'], data: Array<Record<string, unknown>>): Promise<void> {
    const ids = data.map(item => item._id);
    
    switch (dataType) {
      case 'reports':
        const Report = (await import('@/lib/db/models/Report')).default;
        await Report.deleteMany({ _id: { $in: ids } });
        break;
      
      case 'messages':
        const Message = (await import('@/lib/db/models/Message')).default;
        await Message.deleteMany({ _id: { $in: ids } });
        break;
      
      case 'user_data':
        const User = (await import('@/lib/db/models/User')).default;
        await User.deleteMany({ _id: { $in: ids } });
        break;
      
      case 'audit_logs':
        const mongooseForDelete = await import('mongoose');
        const AuditLogDeleteModel = (mongooseForDelete.models.AuditLog as Model<{ _id: Types.ObjectId }>) || (mongooseForDelete.model<{ _id: Types.ObjectId }>('AuditLog'));
        await AuditLogDeleteModel.deleteMany({ _id: { $in: ids as Array<Types.ObjectId> } });
        break;
    }
    
    console.log(`Deleted ${data.length} ${dataType} records`);
  }

  static getRetentionPolicy(dataType: string): RetentionPolicy | undefined {
    return this.policies.find(p => p.dataType === dataType);
  }

  static updateRetentionPolicy(dataType: string, retentionPeriodDays: number): void {
    const policy = this.policies.find(p => p.dataType === dataType);
    if (policy) {
      policy.retentionPeriodDays = retentionPeriodDays;
    }
  }
}
import { NextRequest, NextResponse } from 'next/server';

/**
 * Security headers middleware
 */
export function withSecurityHeaders(response: NextResponse): NextResponse {
  // Content Security Policy - Strict policy for production
  const cspPolicy = process.env.NODE_ENV === 'production'
    ? "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self';"
    : "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self';";

  response.headers.set('Content-Security-Policy', cspPolicy);

  // Prevent XSS attacks
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // HTTPS enforcement (only in production)
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }

  // Prevent MIME type sniffing
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Permissions Policy
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  return response;
}

/**
 * HTTPS enforcement middleware
 */
export function enforceHTTPS(request: NextRequest): NextResponse | null {
  if (process.env.NODE_ENV === 'production') {
    const proto = request.headers.get('x-forwarded-proto');
    if (proto && proto !== 'https') {
      const httpsUrl = `https://${request.headers.get('host')}${request.nextUrl.pathname}${request.nextUrl.search}`;
      return NextResponse.redirect(httpsUrl, 301);
    }
  }
  return null;
}

/**
 * Rate limiting middleware (basic implementation)
 */
const requestCounts = new Map<string, { count: number; resetTime: number }>();

export function withRateLimit(request: NextRequest, limit: number = 100, windowMs: number = 15 * 60 * 1000): boolean {
  // Next.js NextRequest doesn't have a stable `ip`; derive from headers or fallback
  const forwardedFor = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip');
  const clientId = forwardedFor?.split(',')[0]?.trim() || 'unknown';
  const now = Date.now();
  
  const clientData = requestCounts.get(clientId);
  
  if (!clientData || now > clientData.resetTime) {
    requestCounts.set(clientId, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (clientData.count >= limit) {
    return false;
  }
  
  clientData.count++;
  return true;
}

/**
 * CSRF protection middleware
 */
export function withCSRFProtection(request: NextRequest): boolean {
  const method = request.method;
  
  // Only check CSRF for state-changing methods
  if (!['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
    return true;
  }
  
  const origin = request.headers.get('origin');
  const referer = request.headers.get('referer');
  const host = request.headers.get('host');
  
  // Check if request is from same origin
  if (origin && host) {
    const originUrl = new URL(origin);
    return originUrl.host === host;
  }
  
  if (referer && host) {
    const refererUrl = new URL(referer);
    return refererUrl.host === host;
  }
  
  return false;
}
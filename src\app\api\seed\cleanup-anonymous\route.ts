import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { Report, Message, User } from '@/lib/db/models';

export const runtime = 'nodejs';

export async function POST(_request: NextRequest) {
  try {
    // Only allow in development environment
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { success: false, error: 'Cleanup is not allowed in production' },
        { status: 403 }
      );
    }

    console.log('🧹 Starting anonymous reports cleanup...');
    
    await connectDB();

    // Delete existing anonymous reports and their messages
    const anonymousReports = await Report.find({ isAnonymous: true });
    const reportIds = anonymousReports.map(report => report._id);

    // Delete messages associated with anonymous reports
    const deletedMessages = await Message.deleteMany({
      reportId: { $in: reportIds }
    });

    // Delete anonymous reports
    const deletedReports = await Report.deleteMany({ isAnonymous: true });

    // Delete system users created for anonymous reports
    const deletedUsers = await User.deleteMany({
      email: { $in: ['<EMAIL>', '<EMAIL>', '<EMAIL>'] }
    });

    console.log(`✅ Cleanup completed:
      - Deleted ${deletedReports.deletedCount} anonymous reports
      - Deleted ${deletedMessages.deletedCount} messages
      - Deleted ${deletedUsers.deletedCount} system users`);
    
    return NextResponse.json({
      success: true,
      message: 'Anonymous reports cleanup completed successfully',
      details: {
        reportsDeleted: deletedReports.deletedCount,
        messagesDeleted: deletedMessages.deletedCount,
        usersDeleted: deletedUsers.deletedCount
      }
    });

  } catch (error) {
    console.error('❌ Error cleaning up anonymous reports:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to cleanup anonymous reports',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST method to cleanup anonymous reports',
    endpoint: '/api/seed/cleanup-anonymous',
    method: 'POST'
  });
}

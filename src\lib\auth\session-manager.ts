/**
 * Session Management System
 * Handles JWT token blacklisting and session control
 */

import connectDB from '@/lib/db/mongodb';
import mongoose from 'mongoose';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

// Types for JWT payload and session data
interface JWTPayload {
  userId: string;
  id: string;
  role: string;
  companyId?: string;
  email: string;
  tokenId: string;
  iat: number;
}

interface SessionDocument {
  _id: string;
  userId: mongoose.Types.ObjectId;
  tokenId: string;
  userAgent?: string;
  ipAddress?: string;
  createdAt: Date;
  expiresAt: Date;
  isActive: boolean;
  lastActivity: Date;
}

interface BlacklistedTokenDocument {
  _id: string;
  tokenId: string;
  userId: mongoose.Types.ObjectId;
  blacklistedAt: Date;
  reason: string;
}

// Session schema for tracking active sessions
const sessionSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  tokenId: { type: String, required: true, unique: true },
  userAgent: String,
  ipAddress: String,
  createdAt: { type: Date, default: Date.now },
  expiresAt: { type: Date, required: true },
  isActive: { type: Boolean, default: true },
  lastActivity: { type: Date, default: Date.now }
}, {
  collection: 'user_sessions',
  timestamps: true
});

// Create indexes for performance
sessionSchema.index({ userId: 1, isActive: 1 });
// tokenId index is already created by unique: true in schema definition
sessionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

const Session = mongoose.models.Session || mongoose.model('Session', sessionSchema);

// Blacklisted tokens schema
const blacklistedTokenSchema = new mongoose.Schema({
  tokenId: { type: String, required: true, unique: true },
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  blacklistedAt: { type: Date, default: Date.now },
  expiresAt: { type: Date, required: true },
  reason: { type: String, default: 'logout' }
}, {
  collection: 'blacklisted_tokens',
  timestamps: true
});

// Auto-expire blacklisted tokens
blacklistedTokenSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

const BlacklistedToken = mongoose.models.BlacklistedToken || mongoose.model('BlacklistedToken', blacklistedTokenSchema);

export interface SessionInfo {
  tokenId: string;
  userId: string;
  userAgent?: string;
  ipAddress?: string;
  expiresAt: Date;
}

/**
 * Generate JWT token with session tracking
 */
export async function generateSessionToken(
  userId: string,
  userRole: string,
  companyId?: string,
  email?: string,
  options: {
    userAgent?: string;
    ipAddress?: string;
    expiresIn?: string;
  } = {}
): Promise<{ token: string; tokenId: string }> {
  await connectDB();

  const tokenId = generateTokenId();
  const expiresIn = options.expiresIn || '24h';
  const expiresAt = new Date(Date.now() + parseExpirationTime(expiresIn));

  // Create JWT token with session ID
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    throw new Error('JWT_SECRET environment variable is not set');
  }

  const payload: JWTPayload = {
    userId: userId.toString(),
    id: userId.toString(),
    role: userRole,
    companyId: companyId?.toString(),
    email,
    tokenId,
    iat: Math.floor(Date.now() / 1000)
  };

  const token = jwt.sign(payload, jwtSecret, { expiresIn: expiresIn as '24h' });

  // Store session in database
  await (Session as mongoose.Model<SessionDocument>).create({
    userId: new mongoose.Types.ObjectId(userId),
    tokenId,
    userAgent: options.userAgent,
    ipAddress: options.ipAddress,
    expiresAt,
    isActive: true,
    lastActivity: new Date()
  });

  return { token, tokenId };
}

/**
 * Validate session token
 */
export async function validateSessionToken(token: string): Promise<{
  valid: boolean;
  decoded?: jwt.JwtPayload | string;
  reason?: string;
}> {
  try {
    await connectDB();

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as jwt.JwtPayload;
    
    if (!decoded.tokenId) {
      return { valid: false, reason: 'Token missing session ID' };
    }

    // Check if token is blacklisted
    const blacklisted = await (BlacklistedToken as mongoose.Model<BlacklistedTokenDocument>).findOne({ tokenId: decoded.tokenId });
    if (blacklisted) {
      return { valid: false, reason: 'Token has been revoked' };
    }

    // Check if session exists and is active
    const session = await (Session as mongoose.Model<SessionDocument>).findOne({
      tokenId: decoded.tokenId,
      isActive: true
    });

    if (!session) {
      return { valid: false, reason: 'Session not found or inactive' };
    }

    // Update last activity
    await (Session as mongoose.Model<SessionDocument>).updateOne(
      { tokenId: decoded.tokenId },
      { lastActivity: new Date() }
    );

    return { valid: true, decoded };
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      return { valid: false, reason: 'Token expired' };
    }
    if (error instanceof jwt.JsonWebTokenError) {
      return { valid: false, reason: 'Invalid token' };
    }
    return { valid: false, reason: 'Token validation failed' };
  }
}

/**
 * Revoke a specific session token
 */
export async function revokeSessionToken(
  tokenId: string,
  reason: string = 'logout'
): Promise<boolean> {
  try {
    await connectDB();

    // Get session info before revoking
    const session = await (Session as mongoose.Model<SessionDocument>).findOne({ tokenId });
    if (!session) {
      return false;
    }

    // Add to blacklist
    await (BlacklistedToken as mongoose.Model<BlacklistedTokenDocument>).create({
      tokenId,
      userId: session.userId,
      blacklistedAt: new Date(),
      expiresAt: session.expiresAt,
      reason
    });

    // Deactivate session
    await (Session as mongoose.Model<SessionDocument>).updateOne(
      { tokenId },
      { isActive: false }
    );

    return true;
  } catch (error) {
    console.error('Failed to revoke session token:', error);
    return false;
  }
}

/**
 * Revoke all sessions for a user
 */
export async function revokeAllUserSessions(
  userId: string,
  reason: string = 'security_action'
): Promise<number> {
  try {
    await connectDB();

    // Get all active sessions for user
    const sessions = await (Session as mongoose.Model<SessionDocument>).find({
      userId: new mongoose.Types.ObjectId(userId),
      isActive: true
    });

    let revokedCount = 0;

    for (const session of sessions) {
      const success = await revokeSessionToken(session.tokenId, reason);
      if (success) {
        revokedCount++;
      }
    }

    return revokedCount;
  } catch (error) {
    console.error('Failed to revoke user sessions:', error);
    return 0;
  }
}

/**
 * Get active sessions for a user
 */
export async function getUserActiveSessions(userId: string): Promise<Array<{
  tokenId: string;
  userAgent?: string;
  ipAddress?: string;
  createdAt: Date;
  lastActivity: Date;
}>> {
  try {
    await connectDB();

    const sessions = await (Session as mongoose.Model<SessionDocument>).find({
      userId: new mongoose.Types.ObjectId(userId),
      isActive: true
    }).sort({ lastActivity: -1 });

    return sessions.map((session: SessionDocument) => ({
      tokenId: session.tokenId,
      userAgent: session.userAgent,
      ipAddress: session.ipAddress,
      createdAt: session.createdAt,
      lastActivity: session.lastActivity
    }));
  } catch (error) {
    console.error('Failed to get user sessions:', error);
    return [];
  }
}

/**
 * Clean up expired sessions and blacklisted tokens
 */
export async function cleanupExpiredSessions(): Promise<{
  sessionsRemoved: number;
  tokensRemoved: number;
}> {
  try {
    await connectDB();

    const now = new Date();

    // Remove expired sessions
    const expiredSessions = await Session.deleteMany({
      expiresAt: { $lt: now }
    });

    // Remove expired blacklisted tokens
    const expiredTokens = await BlacklistedToken.deleteMany({
      expiresAt: { $lt: now }
    });

    return {
      sessionsRemoved: expiredSessions.deletedCount || 0,
      tokensRemoved: expiredTokens.deletedCount || 0
    };
  } catch (error) {
    console.error('Failed to cleanup expired sessions:', error);
    return { sessionsRemoved: 0, tokensRemoved: 0 };
  }
}

/**
 * Generate unique token ID
 */
function generateTokenId(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Parse expiration time string to milliseconds
 */
function parseExpirationTime(expiresIn: string): number {
  const units: { [key: string]: number } = {
    's': 1000,
    'm': 60 * 1000,
    'h': 60 * 60 * 1000,
    'd': 24 * 60 * 60 * 1000
  };

  const match = expiresIn.match(/^(\d+)([smhd])$/);
  if (!match) {
    return 24 * 60 * 60 * 1000; // Default to 24 hours
  }

  const [, amount, unit] = match;
  return parseInt(amount) * (units[unit] || units.h);
}

/**
 * Schedule periodic cleanup of expired sessions
 */
export function startSessionCleanup(): void {
  // Run cleanup every hour
  setInterval(async () => {
    const result = await cleanupExpiredSessions();
    if (result.sessionsRemoved > 0 || result.tokensRemoved > 0) {
      console.log(`Session cleanup: removed ${result.sessionsRemoved} sessions and ${result.tokensRemoved} tokens`);
    }
  }, 60 * 60 * 1000); // 1 hour
}

/**
 * Multi-Key Encryption Manager for Safe Key Rotation
 * 
 * This module provides production-safe key rotation capabilities by:
 * 1. Supporting multiple active encryption keys simultaneously
 * 2. Versioning keys to track which key encrypted which message
 * 3. Providing graceful fallback for decryption with older keys
 * 4. Enabling zero-downtime key rotation
 */

import crypto from 'crypto';
import { validateEncryptionKey } from './messageEncryption';

export interface KeyVersion {
  version: string;
  key: Buffer;
  createdAt: Date;
  isActive: boolean;
  isRetired: boolean;
  description?: string;
}

export interface EncryptionMetadata {
  keyVersion: string;
  algorithm: string;
  encryptedAt: Date;
  migratedFrom?: string;
}

export interface VersionedEncryptedMessage {
  encryptedContent: string;
  iv: string;
  tag: string;
  isEncrypted: boolean;
  metadata: EncryptionMetadata;
}

class KeyManager {
  private keys: Map<string, KeyVersion> = new Map();
  private currentVersion: string = 'v1';
  private readonly ALGORITHM = 'aes-256-cbc';
  private readonly KEY_LENGTH = 32;
  private readonly IV_LENGTH = 16;

  constructor() {
    this.initializeKeys();
  }

  /**
   * Initialize key manager with current environment key
   */
  private initializeKeys(): void {
    const currentKey = process.env.MESSAGE_ENCRYPTION_KEY;
    
    if (currentKey) {
      const keyBuffer = Buffer.from(currentKey, 'hex');
      if (validateEncryptionKey(currentKey)) {
        this.addKey('v1', keyBuffer, 'Initial production key', true);
      } else {
        throw new Error('Invalid MESSAGE_ENCRYPTION_KEY format');
      }
    } else if (process.env.NODE_ENV === 'production') {
      throw new Error('MESSAGE_ENCRYPTION_KEY is required in production');
    } else {
      // Development fallback
      const devKey = crypto.scryptSync('dev-message-encryption-key', 'salt', this.KEY_LENGTH);
      this.addKey('v1', devKey, 'Development fallback key', true);
    }

    // Load additional keys from environment if available
    this.loadAdditionalKeys();
  }

  /**
   * Load additional versioned keys from environment
   */
  private loadAdditionalKeys(): void {
    const keyPattern = /^MESSAGE_ENCRYPTION_KEY_V(\d+)$/;
    
    Object.keys(process.env).forEach(envVar => {
      const match = envVar.match(keyPattern);
      if (match) {
        const version = `v${match[1]}`;
        const keyHex = process.env[envVar];
        
        if (keyHex && validateEncryptionKey(keyHex)) {
          const keyBuffer = Buffer.from(keyHex, 'hex');
          this.addKey(version, keyBuffer, `Environment key ${version}`, false);
        }
      }
    });
  }

  /**
   * Add a new key version
   */
  addKey(version: string, key: Buffer, description: string = '', isActive: boolean = false): void {
    if (key.length !== this.KEY_LENGTH) {
      throw new Error(`Key must be ${this.KEY_LENGTH} bytes long`);
    }

    const keyVersion: KeyVersion = {
      version,
      key,
      createdAt: new Date(),
      isActive,
      isRetired: false,
      description
    };

    this.keys.set(version, keyVersion);
    
    if (isActive) {
      this.currentVersion = version;
    }
  }

  /**
   * Generate a new key version
   */
  generateNewKey(description: string = ''): { version: string; key: string } {
    const newKey = crypto.randomBytes(this.KEY_LENGTH);
    const newVersion = this.getNextVersion();
    
    this.addKey(newVersion, newKey, description, false);
    
    return {
      version: newVersion,
      key: newKey.toString('hex')
    };
  }

  /**
   * Rotate to a new key version
   */
  rotateToKey(version: string): void {
    const keyVersion = this.keys.get(version);
    if (!keyVersion) {
      throw new Error(`Key version ${version} not found`);
    }

    if (keyVersion.isRetired) {
      throw new Error(`Key version ${version} is retired and cannot be activated`);
    }

    // Deactivate current key
    const currentKey = this.keys.get(this.currentVersion);
    if (currentKey) {
      currentKey.isActive = false;
    }

    // Activate new key
    keyVersion.isActive = true;
    this.currentVersion = version;
  }

  /**
   * Retire a key version (mark as no longer usable)
   */
  retireKey(version: string): void {
    const keyVersion = this.keys.get(version);
    if (!keyVersion) {
      throw new Error(`Key version ${version} not found`);
    }

    if (keyVersion.isActive) {
      throw new Error(`Cannot retire active key version ${version}`);
    }

    keyVersion.isRetired = true;
  }

  /**
   * Get current active key
   */
  getCurrentKey(): Buffer {
    const currentKey = this.keys.get(this.currentVersion);
    if (!currentKey || !currentKey.isActive) {
      throw new Error('No active encryption key found');
    }
    return currentKey.key;
  }

  /**
   * Get key by version
   */
  getKeyByVersion(version: string): Buffer {
    const keyVersion = this.keys.get(version);
    if (!keyVersion) {
      throw new Error(`Key version ${version} not found`);
    }
    if (keyVersion.isRetired) {
      throw new Error(`Key version ${version} is retired`);
    }
    return keyVersion.key;
  }

  /**
   * Get current key version
   */
  getCurrentVersion(): string {
    return this.currentVersion;
  }

  /**
   * List all available key versions
   */
  listKeys(): KeyVersion[] {
    return Array.from(this.keys.values()).map(key => ({
      ...key,
      key: Buffer.alloc(0) // Don't expose actual key in listings
    }));
  }

  /**
   * Encrypt message with current key
   */
  encryptMessage(plaintext: string): VersionedEncryptedMessage {
    const currentKey = this.getCurrentKey();
    const ivBytes = crypto.randomBytes(this.IV_LENGTH);
    
    const cipher = crypto.createCipheriv(this.ALGORITHM, currentKey, ivBytes);
    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = crypto.createHash('sha256')
      .update(encrypted + ivBytes.toString('hex'))
      .digest('hex')
      .substring(0, 32);
    
    return {
      encryptedContent: encrypted,
      iv: ivBytes.toString('hex'),
      tag,
      isEncrypted: true,
      metadata: {
        keyVersion: this.currentVersion,
        algorithm: this.ALGORITHM,
        encryptedAt: new Date()
      }
    };
  }

  /**
   * Decrypt message with appropriate key version
   */
  decryptMessage(encryptedData: VersionedEncryptedMessage): { content: string; isEncrypted: boolean } {
    try {
      if (!encryptedData.isEncrypted || !encryptedData.metadata) {
        return {
          content: encryptedData.encryptedContent,
          isEncrypted: false
        };
      }

      const keyVersion = encryptedData.metadata.keyVersion || 'v1';
      const key = this.getKeyByVersion(keyVersion);
      
      // Verify tag for integrity
      const expectedTag = crypto.createHash('sha256')
        .update(encryptedData.encryptedContent + encryptedData.iv)
        .digest('hex')
        .substring(0, 32);
      
      if (expectedTag !== encryptedData.tag) {
        throw new Error('Message integrity check failed');
      }

      const iv = Buffer.from(encryptedData.iv, 'hex');
      const decipher = crypto.createDecipheriv(this.ALGORITHM, key, iv);
      
      let decrypted = decipher.update(encryptedData.encryptedContent, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return {
        content: decrypted,
        isEncrypted: true
      };
    } catch (error) {
      console.error('Message decryption error:', error);
      
      // Try fallback decryption with all available keys
      return this.fallbackDecryption(encryptedData);
    }
  }

  /**
   * Fallback decryption - try all available keys
   */
  private fallbackDecryption(encryptedData: VersionedEncryptedMessage): { content: string; isEncrypted: boolean } {
    for (const [version, keyVersion] of this.keys.entries()) {
      if (keyVersion.isRetired) continue;
      
      try {
        const iv = Buffer.from(encryptedData.iv, 'hex');
        const decipher = crypto.createDecipheriv(this.ALGORITHM, keyVersion.key, iv);
        
        let decrypted = decipher.update(encryptedData.encryptedContent, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        console.log(`✅ Fallback decryption successful with key version ${version}`);
        return {
          content: decrypted,
          isEncrypted: true
        };
      } catch {
        // Continue to next key
        continue;
      }
    }
    
    // All keys failed
    return {
      content: '🔒 Encrypted message (decryption unavailable)',
      isEncrypted: true
    };
  }

  /**
   * Re-encrypt message with current key (for migration)
   */
  reEncryptMessage(oldEncryptedData: VersionedEncryptedMessage): VersionedEncryptedMessage {
    const decrypted = this.decryptMessage(oldEncryptedData);
    if (!decrypted.isEncrypted) {
      throw new Error('Cannot re-encrypt unencrypted message');
    }
    
    const newEncrypted = this.encryptMessage(decrypted.content);
    newEncrypted.metadata.migratedFrom = oldEncryptedData.metadata?.keyVersion;
    
    return newEncrypted;
  }

  /**
   * Get next version number
   */
  private getNextVersion(): string {
    const versions = Array.from(this.keys.keys())
      .map(v => parseInt(v.replace('v', '')))
      .filter(v => !isNaN(v));
    
    const maxVersion = Math.max(...versions, 0);
    return `v${maxVersion + 1}`;
  }

  /**
   * Health check - verify all keys are accessible
   */
  healthCheck(): { healthy: boolean; issues: string[] } {
    const issues: string[] = [];
    
    // Check if we have an active key
    const currentKey = this.keys.get(this.currentVersion);
    if (!currentKey || !currentKey.isActive) {
      issues.push('No active encryption key found');
    }
    
    // Test encryption/decryption with current key
    try {
      const testMessage = 'health-check-test-message';
      const encrypted = this.encryptMessage(testMessage);
      const decrypted = this.decryptMessage(encrypted);
      
      if (decrypted.content !== testMessage) {
        issues.push('Encryption/decryption test failed');
      }
    } catch (error) {
      issues.push(`Encryption test failed: ${error.message}`);
    }
    
    return {
      healthy: issues.length === 0,
      issues
    };
  }
}

// Singleton instance
export const keyManager = new KeyManager();

// Export for testing and advanced usage
export { KeyManager };

/**
 * Legacy compatibility functions for existing code
 */
export function encryptMessage(plaintext: string): VersionedEncryptedMessage {
  return keyManager.encryptMessage(plaintext);
}

export function decryptMessage(encryptedData: any): { content: string; isEncrypted: boolean } {
  // Handle legacy format
  if (!encryptedData.metadata) {
    const legacyData: VersionedEncryptedMessage = {
      ...encryptedData,
      metadata: {
        keyVersion: 'v1',
        algorithm: 'aes-256-cbc',
        encryptedAt: new Date()
      }
    };
    return keyManager.decryptMessage(legacyData);
  }

  return keyManager.decryptMessage(encryptedData);
}

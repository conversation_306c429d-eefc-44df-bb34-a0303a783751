import { NextRequest, NextResponse } from 'next/server';
import { validateSessionToken } from '@/lib/auth/session-manager';
import { getAuditLogs, AuditEventType, AuditSeverity } from '@/lib/security/audit-logger';

export async function GET(request: NextRequest) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Validate the session token
    const sessionValidation = await validateSessionToken(token);
    
    if (!sessionValidation.valid || !sessionValidation.decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid session' },
        { status: 401 }
      );
    }

    const decoded = sessionValidation.decoded as { userId: string; role: string };
    const { userId, role } = decoded;
    
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const eventType = searchParams.get('eventType') as AuditEventType | null;
    const severity = searchParams.get('severity') as AuditSeverity | null;
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Determine what events the user can see
    const filters: Record<string, unknown> = {
      limit: Math.min(limit, 100), // Cap at 100
      offset
    };

    if (eventType) filters.eventType = eventType;
    if (severity) filters.severity = severity;

    // Regular users can only see their own events
    // Admins and investigators can see all events
    if (role === 'whistleblower') {
      filters.userId = userId;
    }

    // Get audit logs
    const events = await getAuditLogs(filters);

    // Format events for response (remove sensitive data for non-admins)
    const formattedEvents = events.map(event => {
      const formattedEvent: Record<string, unknown> = {
        _id: event._id,
        eventType: event.eventType,
        severity: event.severity,
        timestamp: event.timestamp,
        resource: event.resource
      };

      // Only include IP and user agent for admins/investigators
      if (role === 'admin' || role === 'investigator') {
        formattedEvent.ipAddress = event.ipAddress;
        formattedEvent.userAgent = event.userAgent;
        formattedEvent.userId = event.userId;
        formattedEvent.userEmail = event.userEmail;
        formattedEvent.details = event.details;
      } else {
        // For regular users, only show their own events with limited details
        if (event.userId?.toString() === userId) {
          formattedEvent.ipAddress = event.ipAddress?.replace(/\.\d+$/, '.***'); // Mask IP
          formattedEvent.details = event.details;
        }
      }

      return formattedEvent;
    });

    return NextResponse.json({
      success: true,
      events: formattedEvents,
      totalEvents: formattedEvents.length,
      filters: {
        eventType,
        severity,
        limit,
        offset,
        userRole: role
      }
    });

  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve security events' },
      { status: 500 }
    );
  }
}

// Get security statistics (admin/investigator only)
export async function POST(request: NextRequest) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Validate the session token
    const sessionValidation = await validateSessionToken(token);
    
    if (!sessionValidation.valid || !sessionValidation.decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid session' },
        { status: 401 }
      );
    }

    const decoded2 = sessionValidation.decoded as { role: string };
    const { role } = decoded2;
    
    // Only admins and investigators can access security statistics
    if (role !== 'admin' && role !== 'investigator') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get statistics for the last 24 hours
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    const [
      totalEvents,
      criticalEvents,
      failedLogins,
      suspiciousActivity,
      rateLimitViolations
    ] = await Promise.all([
      getAuditLogs({ startDate: last24Hours, limit: 1000 }),
      getAuditLogs({ severity: AuditSeverity.CRITICAL, startDate: last24Hours, limit: 100 }),
      getAuditLogs({ eventType: AuditEventType.LOGIN_FAILED, startDate: last24Hours, limit: 100 }),
      getAuditLogs({ eventType: AuditEventType.SUSPICIOUS_ACTIVITY, startDate: last24Hours, limit: 100 }),
      getAuditLogs({ eventType: AuditEventType.RATE_LIMIT_EXCEEDED, startDate: last24Hours, limit: 100 })
    ]);

    const statistics = {
      last24Hours: {
        totalEvents: totalEvents.length,
        criticalEvents: criticalEvents.length,
        failedLogins: failedLogins.length,
        suspiciousActivity: suspiciousActivity.length,
        rateLimitViolations: rateLimitViolations.length
      },
      eventTypes: {
        [AuditEventType.LOGIN_SUCCESS]: 0,
        [AuditEventType.LOGIN_FAILED]: 0,
        [AuditEventType.SUSPICIOUS_ACTIVITY]: 0,
        [AuditEventType.RATE_LIMIT_EXCEEDED]: 0,
        [AuditEventType.PERMISSION_DENIED]: 0
      },
      severityDistribution: {
        [AuditSeverity.CRITICAL]: 0,
        [AuditSeverity.HIGH]: 0,
        [AuditSeverity.MEDIUM]: 0,
        [AuditSeverity.LOW]: 0
      }
    };

    // Count events by type and severity
    totalEvents.forEach(event => {
      if (statistics.eventTypes.hasOwnProperty(event.eventType)) {
        statistics.eventTypes[event.eventType as keyof typeof statistics.eventTypes]++;
      }
      
      if (statistics.severityDistribution.hasOwnProperty(event.severity)) {
        statistics.severityDistribution[event.severity as keyof typeof statistics.severityDistribution]++;
      }
    });

    return NextResponse.json({
      success: true,
      statistics,
      generatedAt: new Date().toISOString()
    });

  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to generate security statistics' },
      { status: 500 }
    );
  }
}

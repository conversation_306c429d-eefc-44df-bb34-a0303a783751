import { NextRequest, NextResponse } from 'next/server';
import { GDPRService } from '@/lib/compliance/gdpr';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 });
    }

    const success = await GDPRService.deleteUserData(userId);
    
    return NextResponse.json({
      success,
      message: success ? 'User data deleted successfully' : 'Deletion failed'
    });
  } catch {
    return NextResponse.json({ error: 'Deletion failed' }, { status: 500 });
  }
}
import { NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import { SystemSetting } from '@/lib/db/models';
import { ISystemSetting } from '@/lib/db/models/interfaces';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Only admins can view system settings
    if (request.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Admin role required.' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const includeHidden = searchParams.get('includeHidden') === 'true';

    // Build query for company isolation
    const query: any = { companyId: request.user.companyId };
    
    if (category) {
      query.category = category;
    }
    
    if (!includeHidden) {
      query['uiConfig.isHidden'] = { $ne: true };
    }

    // Get system settings
    const settings = await SystemSetting.find(query)
      .sort({ 'uiConfig.displayOrder': 1, name: 1 });

    // Transform data for frontend
    const transformedSettings = settings.map(setting => {
      const s = setting as unknown as ISystemSetting;
      return {
        id: s._id.toString(),
        key: s.key,
        category: s.category,
        name: s.name,
        description: s.description,
        value: s.value,
        valueType: s.valueType,
        defaultValue: s.defaultValue,
        validation: s.validation,
        isPublic: s.isPublic,
        isReadOnly: s.isReadOnly,
        requiresRestart: s.requiresRestart,
        lastModifiedAt: s.lastModifiedAt?.toISOString(),
        version: s.version,
        uiConfig: s.uiConfig
      };
    });

    // Group by category for easier UI rendering
    const groupedSettings = transformedSettings.reduce((acc, setting) => {
      if (!acc[setting.category]) {
        acc[setting.category] = [];
      }
      acc[setting.category].push(setting);
      return acc;
    }, {} as Record<string, typeof transformedSettings>);

    return NextResponse.json({
      success: true,
      data: transformedSettings,
      grouped: groupedSettings,
      categories: Object.keys(groupedSettings)
    });
  } catch (error) {
    console.error('System settings API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const PUT = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Only admins can modify system settings
    if (request.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Admin role required.' },
        { status: 403 }
      );
    }

    const { key, value, reason } = await request.json();
    
    if (!key || value === undefined) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: key and value' },
        { status: 400 }
      );
    }

    // Find the setting
    const setting = await SystemSetting.findOne({ 
      companyId: request.user.companyId, 
      key 
    });
    
    if (!setting) {
      return NextResponse.json(
        { success: false, error: 'Setting not found' },
        { status: 404 }
      );
    }

    const s = setting as unknown as ISystemSetting;

    // Check if setting is read-only
    if (s.isReadOnly) {
      return NextResponse.json(
        { success: false, error: 'Setting is read-only' },
        { status: 403 }
      );
    }

    // Validate the new value based on setting constraints
    const validation = s.validation;
    if (validation) {
      if (validation.required && (value === null || value === undefined || value === '')) {
        return NextResponse.json(
          { success: false, error: 'Value is required' },
          { status: 400 }
        );
      }

      if (s.valueType === 'number') {
        const numValue = Number(value);
        if (isNaN(numValue)) {
          return NextResponse.json(
            { success: false, error: 'Value must be a number' },
            { status: 400 }
          );
        }
        if (validation.min !== undefined && numValue < validation.min) {
          return NextResponse.json(
            { success: false, error: `Value must be at least ${validation.min}` },
            { status: 400 }
          );
        }
        if (validation.max !== undefined && numValue > validation.max) {
          return NextResponse.json(
            { success: false, error: `Value must be at most ${validation.max}` },
            { status: 400 }
          );
        }
      }

      if (s.valueType === 'string') {
        const strValue = String(value);
        if (validation.minLength !== undefined && strValue.length < validation.minLength) {
          return NextResponse.json(
            { success: false, error: `Value must be at least ${validation.minLength} characters` },
            { status: 400 }
          );
        }
        if (validation.maxLength !== undefined && strValue.length > validation.maxLength) {
          return NextResponse.json(
            { success: false, error: `Value must be at most ${validation.maxLength} characters` },
            { status: 400 }
          );
        }
        if (validation.pattern) {
          const regex = new RegExp(validation.pattern);
          if (!regex.test(strValue)) {
            return NextResponse.json(
              { success: false, error: 'Value does not match required pattern' },
              { status: 400 }
            );
          }
        }
      }

      if (validation.allowedValues && validation.allowedValues.length > 0) {
        if (!validation.allowedValues.includes(value)) {
          return NextResponse.json(
            { success: false, error: `Value must be one of: ${validation.allowedValues.join(', ')}` },
            { status: 400 }
          );
        }
      }
    }

    // Update the setting
    const previousValue = s.value;
    (setting as any).value = value;
    (setting as any).lastModifiedBy = request.user.id;
    (setting as any).lastModifiedAt = new Date();

    // Add to change history
    (setting as any).changeHistory.push({
      previousValue,
      newValue: value,
      changedBy: request.user.id,
      changedAt: new Date(),
      reason: reason || 'Updated via API'
    });

    (setting as any).version += 1;
    await setting.save();

    const updatedSetting = setting as unknown as ISystemSetting;
    return NextResponse.json({
      success: true,
      data: {
        key: updatedSetting.key,
        value: updatedSetting.value,
        version: updatedSetting.version,
        requiresRestart: updatedSetting.requiresRestart
      },
      message: 'Setting updated successfully'
    });
  } catch (error) {
    console.error('Update system setting API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

import { NextResponse } from 'next/server';
import { seedRefinedData, clearDatabase } from '@/lib/db/refined-seed';

export const runtime = 'nodejs';

export async function POST(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const clearFirst = searchParams.get('clear') === 'true';

    if (clearFirst) {
      await clearDatabase();
    }

    await seedRefinedData();
    
    return NextResponse.json({
      success: true,
      message: 'Database seeded successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to seed database',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST to seed the database. Add ?clear=true to clear existing data first.',
    endpoints: {
      seed: 'POST /api/debug/seed',
      seedAndClear: 'POST /api/debug/seed?clear=true'
    }
  });
}

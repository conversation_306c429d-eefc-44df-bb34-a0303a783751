"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { FileText, UserCheck, MessageSquare, AlertTriangle, CheckCircle } from "lucide-react";
import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/useAuth";

interface ActivityItem {
    id: string;
    type: string;
    title: string;
    description: string;
    time: string;
    reportId?: string;
    icon: string;
}

const getActivityIcon = (type: string) => {
    switch (type) {
        case 'case_created':
            return FileText;
        case 'assignment':
            return UserCheck;
        case 'message':
            return MessageSquare;
        case 'sla_breach':
            return AlertTriangle;
        case 'case_closed':
            return CheckCircle;
        default:
            return FileText;
    }
};

const getActivityColor = (type: string) => {
    switch (type) {
        case 'case_created':
            return 'text-blue-600 bg-blue-50';
        case 'assignment':
            return 'text-green-600 bg-green-50';
        case 'message':
            return 'text-purple-600 bg-purple-50';
        case 'sla_breach':
            return 'text-red-600 bg-red-50';
        case 'case_closed':
            return 'text-gray-600 bg-gray-50';
        default:
            return 'text-gray-600 bg-gray-50';
    }
};

export default function AdminRecentActivity() {
    const { user, isAuthenticated } = useAuth();
    const [activities, setActivities] = useState<ActivityItem[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    const loadRecentActivity = useCallback(async () => {
        if (!isAuthenticated || !user?.id) {
            setActivities([]);
            setIsLoading(false);
            return;
        }

        try {
            setIsLoading(true);
            const response = await fetch('/api/dashboard/recent-activity', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();

            if (data.success) {
                setActivities(data.data);
            }
        } catch (error) {
            console.error('Error loading recent activity:', error);
            // Fallback to empty array
            setActivities([]);
        } finally {
            setIsLoading(false);
        }
    }, [isAuthenticated, user?.id]);

    useEffect(() => {
        loadRecentActivity();
    }, [loadRecentActivity]);

    return (
        <Card className="bg-white border-0 shadow-sm">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="text-lg font-semibold text-gray-900">
                        Recent Activity
                    </CardTitle>
                    <Button variant="outline" size="sm" className="hover:bg-[#BBF49C] hover:border-[#BBF49C]">
                        View All
                    </Button>
                </div>
            </CardHeader>
            <CardContent>
                {isLoading ? (
                    <div className="space-y-4">
                        {[...Array(5)].map((_, index) => (
                            <div key={index} className="flex items-start gap-3">
                                <Skeleton className="w-8 h-8 rounded-lg" />
                                <div className="flex-1 min-w-0">
                                    <Skeleton className="h-4 w-3/4 mb-2" />
                                    <Skeleton className="h-3 w-1/2" />
                                </div>
                            </div>
                        ))}
                    </div>
                ) : activities.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <FileText className="w-8 h-8 text-gray-400" />
                        </div>
                        <h3 className="text-sm font-medium text-gray-900 mb-1">No recent activity</h3>
                        <p className="text-xs text-gray-500">Activity will appear here as reports are processed</p>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {activities.map((activity) => {
                            const Icon = getActivityIcon(activity.type);
                            const colorClasses = getActivityColor(activity.type);
                            
                            return (
                                <div key={activity.id} className="flex items-start gap-3">
                                    <div className={`p-2 rounded-lg ${colorClasses}`}>
                                        <Icon className="h-4 w-4" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <p className="text-sm text-gray-900 font-medium">
                                            {activity.title}
                                        </p>
                                        <p className="text-xs text-gray-500 mt-1">
                                            {activity.description}
                                        </p>
                                        <p className="text-xs text-gray-400 mt-1">
                                            {activity.time}
                                        </p>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
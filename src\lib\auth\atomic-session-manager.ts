/**
 * Atomic Session Management System
 * 
 * This module provides race-condition-free session management with:
 * 1. Atomic session operations using MongoDB transactions
 * 2. Concurrent session protection and limits
 * 3. Session cleanup and garbage collection
 * 4. Session hijacking prevention
 * 5. Comprehensive audit logging
 */

import mongoose from 'mongoose';
import crypto from 'crypto';
import { enhancedJWTManager } from './enhanced-jwt-manager';
import connectDB from '@/lib/db/mongodb';

interface SessionData {
  _id?: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  tokenId: string;
  userAgent?: string;
  ipAddress?: string;
  createdAt: Date;
  expiresAt: Date;
  lastActivity: Date;
  isActive: boolean;
  deviceFingerprint?: string;
  sessionType: 'web' | 'mobile' | 'api';
  securityFlags: {
    isSecure: boolean;
    httpOnly: boolean;
    sameSite: 'strict' | 'lax' | 'none';
  };
}

interface SessionCreationOptions {
  userAgent?: string;
  ipAddress?: string;
  expiresIn?: string;
  deviceFingerprint?: string;
  sessionType?: 'web' | 'mobile' | 'api';
  maxConcurrentSessions?: number;
}

interface SessionValidationResult {
  isValid: boolean;
  session?: SessionData;
  shouldRefresh?: boolean;
  error?: string;
}

class AtomicSessionManager {
  private readonly SESSION_COLLECTION = 'sessions';
  private readonly BLACKLISTED_TOKENS_COLLECTION = 'blacklistedtokens';
  private readonly MAX_CONCURRENT_SESSIONS = 5;
  private readonly SESSION_CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startSessionCleanup();
  }

  /**
   * Convert userId to appropriate format for database queries
   */
  private convertUserId(userId: string): any {
    try {
      // Try to create ObjectId if it's a valid ObjectId string
      return new mongoose.Types.ObjectId(userId);
    } catch {
      // If not a valid ObjectId, use as string
      return userId;
    }
  }

  /**
   * Create new session atomically
   */
  async createSession(
    userId: string,
    userRole: string,
    companyId?: string,
    email?: string,
    options: SessionCreationOptions = {}
  ): Promise<{ token: string; tokenId: string; session: SessionData }> {
    await connectDB();
    
    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        // Generate secure token ID and device fingerprint
        const tokenId = this.generateSecureTokenId();
        const deviceFingerprint = options.deviceFingerprint || this.generateDeviceFingerprint(options);
        
        // Calculate expiration
        const expiresIn = options.expiresIn || '24h';
        const expiresAt = new Date(Date.now() + this.parseExpirationTime(expiresIn));
        
        // Check concurrent session limits
        await this.enforceConcurrentSessionLimits(
          userId, 
          options.maxConcurrentSessions || this.MAX_CONCURRENT_SESSIONS,
          session
        );
        
        // Create session document
        const sessionData: SessionData = {
          userId: this.convertUserId(userId),
          tokenId,
          userAgent: options.userAgent,
          ipAddress: options.ipAddress,
          createdAt: new Date(),
          expiresAt,
          lastActivity: new Date(),
          isActive: true,
          deviceFingerprint,
          sessionType: options.sessionType || 'web',
          securityFlags: {
            isSecure: process.env.NODE_ENV === 'production',
            httpOnly: true,
            sameSite: 'strict'
          }
        };
        
        // Insert session atomically
        const insertResult = await mongoose.connection.db
          .collection(this.SESSION_COLLECTION)
          .insertMany([sessionData], { session });

        // Get the inserted ID from the result
        const insertedIds = Object.values(insertResult.insertedIds);
        sessionData._id = insertedIds[0];
        
        // Generate JWT token
        const token = enhancedJWTManager.signToken({
          userId,
          id: userId,
          role: userRole,
          companyId,
          email: email || '',
          tokenId
        });
        
        console.log(`✅ Session created atomically for user ${userId} (${tokenId})`);
        
        return { token, tokenId, session: sessionData };
      });
    } catch (error) {
      console.error('❌ Atomic session creation failed:', error);
      throw new Error('Failed to create session');
    } finally {
      await session.endSession();
    }
  }

  /**
   * Validate session atomically
   */
  async validateSession(token: string, ipAddress?: string): Promise<SessionValidationResult> {
    try {
      await connectDB();
      
      // Verify JWT token
      const { payload, version: _version, isLegacy: _isLegacy } = enhancedJWTManager.verifyToken(token);
      
      if (!payload.tokenId) {
        return { isValid: false, error: 'Token missing session ID' };
      }
      
      const session = await mongoose.startSession();
      
      try {
        return await session.withTransaction(async () => {
          // Find and validate session
          const sessionDoc = await mongoose.connection.db
            .collection(this.SESSION_COLLECTION)
            .findOne(
              {
                tokenId: payload.tokenId,
                userId: this.convertUserId(payload.userId),
                isActive: true,
                expiresAt: { $gt: new Date() }
              },
              { session }
            );
          
          if (!sessionDoc) {
            return { isValid: false, error: 'Session not found or expired' };
          }
          
          // Check for session hijacking indicators
          const hijackingRisk = this.detectSessionHijacking(sessionDoc as unknown as SessionData, ipAddress);
          if (hijackingRisk.isRisk) {
            // Invalidate session immediately
            await this.invalidateSessionAtomic(payload.tokenId, session);
            return { isValid: false, error: 'Session security violation detected' };
          }
          
          // Update last activity atomically
          await mongoose.connection.db
            .collection(this.SESSION_COLLECTION)
            .updateOne(
              { tokenId: payload.tokenId },
              { 
                $set: { 
                  lastActivity: new Date(),
                  ...(ipAddress && { ipAddress })
                }
              },
              { session }
            );
          
          return {
            isValid: true,
            session: sessionDoc as SessionData,
            shouldRefresh: _isLegacy // Suggest refresh for legacy tokens
          };
        });
      } finally {
        await session.endSession();
      }
    } catch (error) {
      console.error('❌ Session validation error:', error);
      return { isValid: false, error: 'Session validation failed' };
    }
  }

  /**
   * Invalidate session atomically
   */
  async invalidateSession(tokenId: string, reason: string = 'User logout'): Promise<void> {
    await connectDB();
    
    const session = await mongoose.startSession();
    
    try {
      await session.withTransaction(async () => {
        await this.invalidateSessionAtomic(tokenId, session, reason);
      });
      
      console.log(`✅ Session invalidated: ${tokenId} (${reason})`);
    } catch (error) {
      console.error('❌ Session invalidation failed:', error);
      throw new Error('Failed to invalidate session');
    } finally {
      await session.endSession();
    }
  }

  /**
   * Invalidate all sessions for a user
   */
  async invalidateAllUserSessions(userId: string, reason: string = 'Security action'): Promise<number> {
    await connectDB();
    
    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        // Get all active sessions for user
        const userSessions = await mongoose.connection.db
          .collection(this.SESSION_COLLECTION)
          .find(
            {
              userId: this.convertUserId(userId),
              isActive: true
            },
            { session }
          )
          .toArray();
        
        if (userSessions.length === 0) {
          return 0;
        }
        
        // Blacklist all tokens
        const blacklistEntries = userSessions.map(sessionDoc => ({
          tokenId: sessionDoc.tokenId,
          userId: this.convertUserId(userId),
          blacklistedAt: new Date(),
          reason
        }));
        
        await mongoose.connection.db
          .collection(this.BLACKLISTED_TOKENS_COLLECTION)
          .insertMany(blacklistEntries, { session });
        
        // Deactivate all sessions
        const result = await mongoose.connection.db
          .collection(this.SESSION_COLLECTION)
          .updateMany(
            {
              userId: this.convertUserId(userId),
              isActive: true
            },
            { 
              $set: { 
                isActive: false,
                invalidatedAt: new Date(),
                invalidationReason: reason
              }
            },
            { session }
          );
        
        console.log(`✅ Invalidated ${result.modifiedCount} sessions for user ${userId}`);
        
        return result.modifiedCount;
      });
    } catch (error) {
      console.error('❌ Failed to invalidate user sessions:', error);
      throw new Error('Failed to invalidate user sessions');
    } finally {
      await session.endSession();
    }
  }

  /**
   * Enforce concurrent session limits
   */
  private async enforceConcurrentSessionLimits(
    userId: string,
    maxSessions: number,
    session: mongoose.ClientSession
  ): Promise<void> {
    // Count active sessions
    const activeSessions = await mongoose.connection.db
      .collection(this.SESSION_COLLECTION)
      .countDocuments(
        {
          userId: this.convertUserId(userId),
          isActive: true,
          expiresAt: { $gt: new Date() }
        },
        { session }
      );
    
    if (activeSessions >= maxSessions) {
      // Remove oldest sessions to make room
      const sessionsToRemove = activeSessions - maxSessions + 1;
      
      const oldestSessions = await mongoose.connection.db
        .collection(this.SESSION_COLLECTION)
        .find(
          {
            userId: this.convertUserId(userId),
            isActive: true
          },
          { session }
        )
        .sort({ lastActivity: 1 })
        .limit(sessionsToRemove)
        .toArray();
      
      for (const oldSession of oldestSessions) {
        await this.invalidateSessionAtomic(oldSession.tokenId, session, 'Session limit exceeded');
      }
      
      console.log(`🔄 Removed ${sessionsToRemove} old sessions for user ${userId}`);
    }
  }

  /**
   * Invalidate session atomically (internal)
   */
  private async invalidateSessionAtomic(
    tokenId: string,
    session: mongoose.ClientSession,
    reason: string = 'Session invalidated'
  ): Promise<void> {
    // Add to blacklist
    await mongoose.connection.db
      .collection(this.BLACKLISTED_TOKENS_COLLECTION)
      .insertOne(
        {
          tokenId,
          blacklistedAt: new Date(),
          reason
        },
        { session }
      );
    
    // Deactivate session
    await mongoose.connection.db
      .collection(this.SESSION_COLLECTION)
      .updateOne(
        { tokenId },
        { 
          $set: { 
            isActive: false,
            invalidatedAt: new Date(),
            invalidationReason: reason
          }
        },
        { session }
      );
  }

  /**
   * Detect potential session hijacking
   */
  private detectSessionHijacking(session: SessionData, currentIp?: string): { isRisk: boolean; reasons: string[] } {
    const reasons: string[] = [];

    // Check IP address changes (if tracking enabled)
    if (session.ipAddress && currentIp && session.ipAddress !== currentIp) {
      reasons.push('IP address changed');
    }

    // Check for suspicious activity patterns - but be more reasonable
    const timeSinceLastActivity = Date.now() - session.lastActivity.getTime();

    // Only flag as suspicious if there are multiple rapid requests (< 100ms apart)
    // and it's not the first validation (which happens immediately after creation)
    if (timeSinceLastActivity < 100 && timeSinceLastActivity > 0) {
      // This could indicate automated attacks, but normal usage right after creation is fine
      const timeSinceCreation = Date.now() - session.createdAt.getTime();
      if (timeSinceCreation > 5000) { // Only suspicious if session is older than 5 seconds
        reasons.push('Suspicious rapid activity');
      }
    }

    return {
      isRisk: reasons.length > 0,
      reasons
    };
  }

  /**
   * Generate secure token ID
   */
  private generateSecureTokenId(): string {
    const timestamp = Date.now().toString(36);
    const random = crypto.randomBytes(24).toString('hex');
    return `${timestamp}_${random}`;
  }

  /**
   * Generate device fingerprint
   */
  private generateDeviceFingerprint(options: SessionCreationOptions): string {
    const components = [
      options.userAgent || 'unknown',
      options.sessionType || 'web',
      Date.now().toString()
    ];
    
    return crypto.createHash('sha256').update(components.join('|')).digest('hex');
  }

  /**
   * Parse expiration time string
   */
  private parseExpirationTime(expiresIn: string): number {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) return 24 * 60 * 60 * 1000; // Default 24 hours
    
    const value = parseInt(match[1]);
    const unit = match[2];
    
    switch (unit) {
      case 's': return value * 1000;
      case 'm': return value * 60 * 1000;
      case 'h': return value * 60 * 60 * 1000;
      case 'd': return value * 24 * 60 * 60 * 1000;
      default: return 24 * 60 * 60 * 1000;
    }
  }

  /**
   * Start automatic session cleanup
   */
  private startSessionCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.cleanupInterval = setInterval(async () => {
      try {
        await this.cleanupExpiredSessions();
      } catch (error) {
        console.error('❌ Session cleanup error:', error);
      }
    }, this.SESSION_CLEANUP_INTERVAL);
  }

  /**
   * Clean up expired sessions
   */
  private async cleanupExpiredSessions(): Promise<void> {
    try {
      await connectDB();
      
      const result = await mongoose.connection.db
        .collection(this.SESSION_COLLECTION)
        .deleteMany({
          $or: [
            { expiresAt: { $lt: new Date() } },
            { isActive: false, invalidatedAt: { $lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } }
          ]
        });
      
      if (result.deletedCount > 0) {
        console.log(`🧹 Cleaned up ${result.deletedCount} expired sessions`);
      }
    } catch (error) {
      console.error('❌ Session cleanup failed:', error);
    }
  }

  /**
   * Get session statistics
   */
  async getSessionStats(): Promise<{
    totalActiveSessions: number;
    totalExpiredSessions: number;
    totalBlacklistedTokens: number;
  }> {
    await connectDB();
    
    const [activeSessions, expiredSessions, blacklistedTokens] = await Promise.all([
      mongoose.connection.db.collection(this.SESSION_COLLECTION).countDocuments({
        isActive: true,
        expiresAt: { $gt: new Date() }
      }),
      mongoose.connection.db.collection(this.SESSION_COLLECTION).countDocuments({
        expiresAt: { $lt: new Date() }
      }),
      mongoose.connection.db.collection(this.BLACKLISTED_TOKENS_COLLECTION).countDocuments({})
    ]);
    
    return {
      totalActiveSessions: activeSessions,
      totalExpiredSessions: expiredSessions,
      totalBlacklistedTokens: blacklistedTokens
    };
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
}

// Singleton instance
export const atomicSessionManager = new AtomicSessionManager();

export { AtomicSessionManager };
export type { SessionData, SessionCreationOptions, SessionValidationResult };

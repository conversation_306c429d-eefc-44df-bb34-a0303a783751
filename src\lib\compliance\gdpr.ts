import connectDB from '@/lib/db/mongodb';
import type { Types as MTypes } from 'mongoose';

export interface GDPRRequest {
  userId: string;
  type: 'export' | 'delete' | 'rectify';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  requestedAt: Date;
  completedAt?: Date;
  data?: unknown;
}

export interface ConsentRecord {
  userId: string;
  consentType: 'data_processing' | 'marketing' | 'analytics';
  granted: boolean;
  timestamp: Date;
  ipAddress?: string;
}

export class GDPRService {
  static async exportUserData(userId: string): Promise<{ exportedAt: Date; userId: string; data: unknown }> {
    await connectDB();
    
    const User = (await import('@/lib/db/models/User')).default;
    const Report = (await import('@/lib/db/models/Report')).default;
    const Message = (await import('@/lib/db/models/Message')).default;
    const Conversation = (await import('@/lib/db/models/Conversation')).default;
    
    const userData = {
      profile: await User.findById(userId).select('-hashedPassword -passwordResetToken').lean(),
      reports: await Report.find({ reporterId: userId }).lean(),
      messages: await Message.find({ senderId: userId }).lean(),
      conversations: await Conversation.find({ participants: userId }).lean(),
      consents: await this.getUserConsents(userId)
    };
    
    return {
      exportedAt: new Date(),
      userId,
      data: userData
    };
  }

  static async deleteUserData(userId: string): Promise<boolean> {
    try {
      await connectDB();
      
      const User = (await import('@/lib/db/models/User')).default;
      const Report = (await import('@/lib/db/models/Report')).default;
      const Message = (await import('@/lib/db/models/Message')).default;
      const Conversation = (await import('@/lib/db/models/Conversation')).default;
      
      await Promise.all([
        User.findByIdAndDelete(userId),
        Report.updateMany({ reporterId: userId }, { reporterId: null, reporterEmail: '<EMAIL>' }),
        Message.deleteMany({ senderId: userId }),
        Conversation.updateMany({ participants: userId }, { $pull: { participants: userId } })
      ]);
      return true;
    } catch {
      return false;
    }
  }

  static async recordConsent(userId: string, consentType: string, granted: boolean, ipAddress?: string): Promise<void> {
    await connectDB();
    
    const { Schema, model, models, Types } = await import('mongoose');
    
    interface ConsentDoc {
      userId: MTypes.ObjectId;
      consentType: string;
      granted: boolean;
      timestamp: Date;
      ipAddress?: string;
    }
    const ConsentSchema = new Schema<ConsentDoc>({
      userId: { type: Schema.Types.ObjectId, required: true },
      consentType: { type: String, required: true },
      granted: { type: Boolean, required: true },
      timestamp: { type: Date, default: Date.now },
      ipAddress: String
    });
    
    const Consent = (models.Consent as import('mongoose').Model<ConsentDoc>) || model<ConsentDoc>('Consent', ConsentSchema);
    
    await Consent.create({
      userId: new Types.ObjectId(userId),
      consentType,
      granted,
      timestamp: new Date(),
      ipAddress: this.anonymizeIP(ipAddress)
    });
  }

  private static anonymizeIP(ip?: string): string | undefined {
    if (!ip) return undefined;
    const parts = ip.split('.');
    if (parts.length === 4) {
      return `${parts[0]}.${parts[1]}.${parts[2]}.0`;
    }
    return ip.substring(0, ip.lastIndexOf(':')) + ':0000';
  }

  private static async getUserConsents(userId: string) {
    const { Schema, model, models, Types } = await import('mongoose');
    
    interface ConsentDoc {
      userId: MTypes.ObjectId;
      consentType: string;
      granted: boolean;
      timestamp: Date;
      ipAddress?: string;
    }
    const ConsentSchema = new Schema<ConsentDoc>({
      userId: { type: Schema.Types.ObjectId, required: true },
      consentType: { type: String, required: true },
      granted: { type: Boolean, required: true },
      timestamp: { type: Date, default: Date.now },
      ipAddress: String
    });
    
    const Consent = (models.Consent as import('mongoose').Model<ConsentDoc>) || model<ConsentDoc>('Consent', ConsentSchema);
    return await Consent.find({ userId: new Types.ObjectId(userId) }).lean();
  }
}